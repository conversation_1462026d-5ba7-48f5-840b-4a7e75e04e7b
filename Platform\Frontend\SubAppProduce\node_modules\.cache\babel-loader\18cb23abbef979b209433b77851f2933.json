{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\components\\processHead.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\components\\processHead.vue", "mtime": 1757572678807}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgYmltZGlhbG9nIGZyb20gJ0Avdmlld3MvcGxtL2NvbXBvbmVudHMvZGlhbG9nJzsKLy8gaW1wb3J0IHsgVmVyaWZpY2F0aW9uIH0gZnJvbSAnQC9hcGkvcGxtL3Byb2Nlc3NtYW5hZ2VtZW50JwppbXBvcnQgeyBWZXJpZmljYXRpb24gfSBmcm9tICdAL2FwaS9zeXMvZmxvdyc7CmltcG9ydCB7IGNsb3NlVGFnVmlldyB9IGZyb20gJ0AvdXRpbHMnOwppbXBvcnQgeyBiYXNlVXJsIH0gZnJvbSAnQC91dGlscy9iYXNldXJsJzsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIGJpbWRpYWxvZzogYmltZGlhbG9nCiAgfSwKICBwcm9wczogewogICAgdGl0bGU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAn5rWB56iL6K6h5YiS5a6h5om5JwogICAgfSwKICAgIC8vIOa1geeoi0lkCiAgICBwcm9jZXNzSWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIGFwcHJvdmU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAn6YCaIOi/hycKICAgIH0sCiAgICByZWplY3Q6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAn6amzIOWbnicKICAgIH0sCiAgICBub2RlUmVqZWN0VHlwZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcxJwogICAgfSwKICAgIG5vZGVSZWplY3RTdGVwOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0sCiAgICByZWZ1c2U6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAn5LiN6YCa6L+HJwogICAgfSwKICAgIHNob3dyZWZ1c2U6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0sCiAgICBiZWZvcmVhcHByb3ZlOiB7CiAgICAgIHR5cGU6IEZ1bmN0aW9uLAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkgewogICAgICAgICAgcmVzb2x2ZSgpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy8g5a6h5om55oSP6KeB5b+F5aGrCiAgICByZXF1aXJlZDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiB0cnVlCiAgICB9LAogICAgbm9TdHlsZTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIHNob3dSZWplY3Q6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogdHJ1ZQogICAgfSwKICAgIHdlYklkOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0sCiAgICBidXNpbmVzc0lkOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaG93YXVkaXQ6IGZhbHNlLAogICAgICB0eXBlOiAnJywKICAgICAgVmVyaWZpY2F0aW9uT3BpbmlvbjogJycKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBvcGVuZGlhbG9nOiBmdW5jdGlvbiBvcGVuZGlhbG9nKHR5cGUpIHsKICAgICAgdGhpcy5zaG93YXVkaXQgPSB0cnVlOwogICAgICB0aGlzLnR5cGUgPSB0eXBlOwogICAgfSwKICAgIGNsb3NlYXVkaXQ6IGZ1bmN0aW9uIGNsb3NlYXVkaXQoKSB7CiAgICAgIHRoaXMuVmVyaWZpY2F0aW9uT3BpbmlvbiA9ICcnOwogICAgICB0aGlzLnNob3dhdWRpdCA9IGZhbHNlOwogICAgfSwKICAgIGF1ZGl0OiBmdW5jdGlvbiBhdWRpdCgpIHsKICAgICAgaWYgKHRoaXMudHlwZSA9PT0gJ2FwcHJvdmUnKSB7CiAgICAgICAgdGhpcy5hcHByb3ZlSXQoKTsKICAgICAgfSBlbHNlIGlmICh0aGlzLnR5cGUgPT09ICdyZWplY3QnKSB7CiAgICAgICAgdGhpcy5yZWplY3RJdCgpOwogICAgICB9IGVsc2UgaWYgKHRoaXMudHlwZSA9PT0gJ3JlZnVzZScpIHsKICAgICAgICB0aGlzLnJlZnVzZUl0KCk7CiAgICAgIH0KICAgIH0sCiAgICAvLyBmbG93SW5zdGFuY2VJZDrmtYHnqItJZAogICAgLy8gVmVyaWZpY2F0aW9uRmluYWxseToxOuWQjOaEj++8mzLvvJrkuI3lkIzmhI/vvJsz77ya6amz5ZueCiAgICAvLyBWZXJpZmljYXRpb25PcGluaW9uOuWuoeaguOaEj+ingQogICAgLy8gbm9kZVJlamVjdFR5cGU66amz5Zue6IezMDrliY3kuIDmraUvMTrnrKzkuIDmraUvMu+8muaMh+WumuiKgueCuQogICAgLy8gbm9kZVJlamVjdFN0ZXA65b2T6amz5Zue57G75Z6L5Li6MuaXtu+8jOmps+Wbnue7k+eCuWNvZGUKICAgIC8vIOWQjOaEjwogICAgYXBwcm92ZUl0OiBmdW5jdGlvbiBhcHByb3ZlSXQoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAoIShfdGhpcy5yZXF1aXJlZCAmJiAhX3RoaXMuVmVyaWZpY2F0aW9uT3BpbmlvbiAmJiBfdGhpcy5WZXJpZmljYXRpb25PcGluaW9uICE9PSAwKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDM7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35aGr5YaZ5a6h5qC45oSP6KeBJyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmFicnVwdCgicmV0dXJuIik7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBfdGhpcy5iZWZvcmVhcHByb3ZlKCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICB2YXIgcGFyYW0gPSB7CiAgICAgICAgICAgICAgICAgIGZsb3dJbnN0YW5jZUlkOiBfdGhpcy5wcm9jZXNzSWQsCiAgICAgICAgICAgICAgICAgIFZlcmlmaWNhdGlvbkZpbmFsbHk6IDEsCiAgICAgICAgICAgICAgICAgIFZlcmlmaWNhdGlvbk9waW5pb246IF90aGlzLlZlcmlmaWNhdGlvbk9waW5pb24sCiAgICAgICAgICAgICAgICAgIE5vZGVSZWplY3RUeXBlOiBfdGhpcy5ub2RlUmVqZWN0VHlwZSwKICAgICAgICAgICAgICAgICAgTm9kZVJlamVjdFN0ZXA6IF90aGlzLm5vZGVSZWplY3RTdGVwLAogICAgICAgICAgICAgICAgICBXZWJJZDogX3RoaXMud2ViSWQsCiAgICAgICAgICAgICAgICAgIEJ1c2luZXNzRGF0ZWxkOiBfdGhpcy5idXNpbmVzc0lkLAogICAgICAgICAgICAgICAgICBGb3JtRGF0YUpzb246ICcnLAogICAgICAgICAgICAgICAgICBQbGF0ZUZvcm1fVXJsOiBiYXNlVXJsKCkKICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICBWZXJpZmljYXRpb24ocGFyYW0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfmj5DkuqTmiJDlip8nLAogICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgX3RoaXMuJGVtaXQoJ2FmdGVyYXBwcm92YWwnLCAnYXBwcm92ZScpOwogICAgICAgICAgICAgICAgICAgIF90aGlzLnNob3dhdWRpdCA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgIC8vIHRoaXMuY2FuY2VsKCkKICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDpqbPlm54KICAgIHJlamVjdEl0OiBmdW5jdGlvbiByZWplY3RJdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIHBhcmFtOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDIucHJldiA9IF9jb250ZXh0Mi5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAoIShfdGhpczIucmVxdWlyZWQgJiYgIV90aGlzMi5WZXJpZmljYXRpb25PcGluaW9uICYmIF90aGlzMi5WZXJpZmljYXRpb25PcGluaW9uICE9PSAwKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAzOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS53YXJuaW5nKCfor7floavlhpnlrqHmoLjmhI/op4EnKTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLmFicnVwdCgicmV0dXJuIik7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBwYXJhbSA9IHsKICAgICAgICAgICAgICAgIGZsb3dJbnN0YW5jZUlkOiBfdGhpczIucHJvY2Vzc0lkLAogICAgICAgICAgICAgICAgVmVyaWZpY2F0aW9uRmluYWxseTogMiwKICAgICAgICAgICAgICAgIFZlcmlmaWNhdGlvbk9waW5pb246IF90aGlzMi5WZXJpZmljYXRpb25PcGluaW9uLAogICAgICAgICAgICAgICAgTm9kZVJlamVjdFR5cGU6IF90aGlzMi5ub2RlUmVqZWN0VHlwZSwKICAgICAgICAgICAgICAgIE5vZGVSZWplY3RTdGVwOiBfdGhpczIubm9kZVJlamVjdFN0ZXAsCiAgICAgICAgICAgICAgICBXZWJJZDogX3RoaXMyLndlYklkLAogICAgICAgICAgICAgICAgQnVzaW5lc3NEYXRlbGQ6IF90aGlzMi5idXNpbmVzc0lkLAogICAgICAgICAgICAgICAgRm9ybURhdGFKc29uOiAnJywKICAgICAgICAgICAgICAgIFBsYXRlRm9ybV9Vcmw6IGJhc2VVcmwoKQogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgVmVyaWZpY2F0aW9uKHBhcmFtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ+aPkOS6pOaIkOWKnycsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICBfdGhpczIuJGVtaXQoJ2FmdGVyYXBwcm92YWwnLCAncmVqZWN0Jyk7CiAgICAgICAgICAgICAgICAgIF90aGlzMi5zaG93YXVkaXQgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgLy8gdGhpcy5jYW5jZWwoKQogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTIpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDkuI3lkIzmhI8KICAgIHJlZnVzZUl0OiBmdW5jdGlvbiByZWZ1c2VJdCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTMoKSB7CiAgICAgICAgdmFyIHBhcmFtOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMyQoX2NvbnRleHQzKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDMucHJldiA9IF9jb250ZXh0My5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAoIShfdGhpczMucmVxdWlyZWQgJiYgIV90aGlzMy5WZXJpZmljYXRpb25PcGluaW9uICYmIF90aGlzMy5WZXJpZmljYXRpb25PcGluaW9uICE9PSAwKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSAzOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS53YXJuaW5nKCfor7floavlhpnlrqHmoLjmhI/op4EnKTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLmFicnVwdCgicmV0dXJuIik7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBwYXJhbSA9IHsKICAgICAgICAgICAgICAgIGZsb3dJbnN0YW5jZUlkOiBfdGhpczMucHJvY2Vzc0lkLAogICAgICAgICAgICAgICAgVmVyaWZpY2F0aW9uRmluYWxseTogMiwKICAgICAgICAgICAgICAgIFZlcmlmaWNhdGlvbk9waW5pb246IF90aGlzMy5WZXJpZmljYXRpb25PcGluaW9uLAogICAgICAgICAgICAgICAgTm9kZVJlamVjdFR5cGU6IF90aGlzMy5ub2RlUmVqZWN0VHlwZSwKICAgICAgICAgICAgICAgIE5vZGVSZWplY3RTdGVwOiBfdGhpczMubm9kZVJlamVjdFN0ZXAKICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIFZlcmlmaWNhdGlvbihwYXJhbSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfmj5DkuqTmiJDlip8nLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgX3RoaXMzLiRlbWl0KCdhZnRlcmFwcHJvdmFsJywgJ3JlZnVzZScpOwogICAgICAgICAgICAgICAgICBfdGhpczMuc2hvd2F1ZGl0ID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIC8vIHRoaXMuY2FuY2VsKCkKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5YWz6Zet5b2T5YmN6aG1CiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgY2xvc2VUYWdWaWV3KHRoaXMuJHN0b3JlLCB0aGlzLiRyb3V0ZSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["bimdialog", "Verification", "closeTagView", "baseUrl", "components", "props", "title", "type", "String", "default", "processId", "approve", "reject", "nodeRejectType", "nodeRejectStep", "refuse", "showrefuse", "Boolean", "beforeapprove", "Function", "Promise", "resolve", "required", "noStyle", "showReject", "webId", "businessId", "data", "showaudit", "VerificationOpinion", "methods", "opendialog", "<PERSON><PERSON><PERSON>", "audit", "approveIt", "rejectIt", "refuseIt", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "$message", "warning", "abrupt", "then", "param", "flowInstanceId", "VerificationFinally", "NodeRejectType", "NodeRejectStep", "WebId", "BusinessDateld", "FormDataJson", "PlateForm_Url", "res", "IsSucceed", "message", "$emit", "Message", "stop", "_this2", "_callee2", "_callee2$", "_context2", "_this3", "_callee3", "_callee3$", "_context3", "cancel", "$store", "$route"], "sources": ["src/views/PRO/components/processHead.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div v-if=\"noStyle\">\r\n      <el-button type=\"primary\" @click=\"opendialog('approve')\">{{ approve }}</el-button>\r\n      <el-button v-if=\"showReject\" type=\"danger\" @click=\"opendialog('reject')\">{{ reject }}</el-button>\r\n      <el-button v-if=\"showrefuse\" type=\"danger\" @click=\"opendialog('refuse')\">{{ refuse }}</el-button>\r\n    </div>\r\n    <div v-else class=\"processhead\">\r\n      <div class=\"title\"><span class=\"span\" />{{ title }}</div>\r\n      <div>\r\n        <el-button type=\"primary\" @click=\"opendialog('approve')\">{{ approve }}</el-button>\r\n        <el-button v-if=\"showReject\" type=\"danger\" @click=\"opendialog('reject')\">{{ reject }}</el-button>\r\n        <el-button v-if=\"showrefuse\" type=\"danger\" @click=\"opendialog('refuse')\">{{ refuse }}</el-button>\r\n      </div>\r\n    </div>\r\n    <bimdialog\r\n      dialog-title=\"审批意见\"\r\n      dialog-width=\"660px\"\r\n      :visible.sync=\"showaudit\"\r\n      :hidebtn=\"false\"\r\n      append-to-body\r\n      @submitbtn=\"audit\"\r\n      @cancelbtn=\"closeaudit\"\r\n      @handleClose=\"closeaudit\"\r\n    >\r\n      <el-form ref=\"form\">\r\n        <el-form-item label=\"审批意见:\" :required=\"required\" label-width=\"80\">\r\n          <el-input v-model=\"VerificationOpinion\" type=\"textarea\" />\r\n        </el-form-item>\r\n      </el-form>\r\n    </bimdialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport bimdialog from '@/views/plm/components/dialog'\r\n// import { Verification } from '@/api/plm/processmanagement'\r\nimport { Verification } from '@/api/sys/flow'\r\nimport { closeTagView } from '@/utils'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nexport default {\r\n  components: {\r\n    bimdialog\r\n  },\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: '流程计划审批'\r\n    },\r\n    // 流程Id\r\n    processId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    approve: {\r\n      type: String,\r\n      default: '通 过'\r\n    },\r\n    reject: {\r\n      type: String,\r\n      default: '驳 回'\r\n    },\r\n    nodeRejectType: {\r\n      type: String,\r\n      default: '1'\r\n    },\r\n    nodeRejectStep: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    refuse: {\r\n      type: String,\r\n      default: '不通过'\r\n    },\r\n    showrefuse: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    beforeapprove: {\r\n      type: Function,\r\n      default: () => { return new Promise((resolve, reject) => { resolve() }) }\r\n    },\r\n    // 审批意见必填\r\n    required: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    noStyle: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showReject: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    webId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    businessId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      showaudit: false,\r\n      type: '',\r\n      VerificationOpinion: ''\r\n    }\r\n  },\r\n  methods: {\r\n    opendialog(type) {\r\n      this.showaudit = true\r\n      this.type = type\r\n    },\r\n    closeaudit() {\r\n      this.VerificationOpinion = ''\r\n      this.showaudit = false\r\n    },\r\n    audit() {\r\n      if (this.type === 'approve') {\r\n        this.approveIt()\r\n      } else if (this.type === 'reject') {\r\n        this.rejectIt()\r\n      } else if (this.type === 'refuse') {\r\n        this.refuseIt()\r\n      }\r\n    },\r\n    // flowInstanceId:流程Id\r\n    // VerificationFinally:1:同意；2：不同意；3：驳回\r\n    // VerificationOpinion:审核意见\r\n    // nodeRejectType:驳回至0:前一步/1:第一步/2：指定节点\r\n    // nodeRejectStep:当驳回类型为2时，驳回结点code\r\n    // 同意\r\n    async approveIt() {\r\n      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {\r\n        this.$message.warning('请填写审核意见')\r\n        return\r\n      }\r\n      this.beforeapprove().then(() => {\r\n        var param = {\r\n          flowInstanceId: this.processId,\r\n          VerificationFinally: 1,\r\n          VerificationOpinion: this.VerificationOpinion,\r\n          NodeRejectType: this.nodeRejectType,\r\n          NodeRejectStep: this.nodeRejectStep,\r\n          WebId: this.webId,\r\n          BusinessDateld: this.businessId,\r\n          FormDataJson: '',\r\n          PlateForm_Url: baseUrl()\r\n        }\r\n        Verification(param).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '提交成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('afterapproval', 'approve')\r\n            this.showaudit = false\r\n            // this.cancel()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    // 驳回\r\n    async rejectIt() {\r\n      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {\r\n        this.$message.warning('请填写审核意见')\r\n        return\r\n      }\r\n      var param = {\r\n        flowInstanceId: this.processId,\r\n        VerificationFinally: 2,\r\n        VerificationOpinion: this.VerificationOpinion,\r\n        NodeRejectType: this.nodeRejectType,\r\n        NodeRejectStep: this.nodeRejectStep,\r\n        WebId: this.webId,\r\n        BusinessDateld: this.businessId,\r\n        FormDataJson: '',\r\n        PlateForm_Url: baseUrl()\r\n      }\r\n      Verification(param).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '提交成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('afterapproval', 'reject')\r\n          this.showaudit = false\r\n          // this.cancel()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 不同意\r\n    async refuseIt() {\r\n      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {\r\n        this.$message.warning('请填写审核意见')\r\n        return\r\n      }\r\n      var param = {\r\n        flowInstanceId: this.processId,\r\n        VerificationFinally: 2,\r\n        VerificationOpinion: this.VerificationOpinion,\r\n        NodeRejectType: this.nodeRejectType,\r\n        NodeRejectStep: this.nodeRejectStep\r\n      }\r\n      Verification(param).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '提交成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('afterapproval', 'refuse')\r\n          this.showaudit = false\r\n          // this.cancel()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 关闭当前页\r\n    cancel() {\r\n      closeTagView(this.$store, this.$route)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scope lang=\"scss\">\r\n.processhead{\r\n    width:100%;\r\n    height:64px;\r\n    background:#fff;\r\n    line-height:24px;\r\n    display:flex;\r\n    justify-content:space-between;\r\n    box-shadow: 0px 1px 3px 1px rgba(20,35,78,0.08);\r\n    margin-bottom:15px;\r\n    // border:1px solid #000;\r\n    padding:20px 16px;\r\n    .title{\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        color: rgba(34,40,52,0.85);\r\n    }\r\n    .span{\r\n        background-image: linear-gradient(180deg, #71B3FF 0%, #298DFF 100%);\r\n        width:4px;\r\n        height:14px;\r\n        display:inline-block;\r\n        border-radius:3px;\r\n        margin-right:8px;\r\n    }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,OAAAA,SAAA;AACA;AACA,SAAAC,YAAA;AACA,SAAAC,YAAA;AACA,SAAAC,OAAA;AACA;EACAC,UAAA;IACAJ,SAAA,EAAAA;EACA;EACAK,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,OAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAG,MAAA;MACAL,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAI,cAAA;MACAN,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAK,cAAA;MACAP,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAM,MAAA;MACAR,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAO,UAAA;MACAT,IAAA,EAAAU,OAAA;MACAR,OAAA;IACA;IACAS,aAAA;MACAX,IAAA,EAAAY,QAAA;MACAV,OAAA,WAAAA,SAAA;QAAA,WAAAW,OAAA,WAAAC,OAAA,EAAAT,MAAA;UAAAS,OAAA;QAAA;MAAA;IACA;IACA;IACAC,QAAA;MACAf,IAAA,EAAAU,OAAA;MACAR,OAAA;IACA;IACAc,OAAA;MACAhB,IAAA,EAAAU,OAAA;MACAR,OAAA;IACA;IACAe,UAAA;MACAjB,IAAA,EAAAU,OAAA;MACAR,OAAA;IACA;IACAgB,KAAA;MACAlB,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAiB,UAAA;MACAnB,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAkB,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACArB,IAAA;MACAsB,mBAAA;IACA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAxB,IAAA;MACA,KAAAqB,SAAA;MACA,KAAArB,IAAA,GAAAA,IAAA;IACA;IACAyB,UAAA,WAAAA,WAAA;MACA,KAAAH,mBAAA;MACA,KAAAD,SAAA;IACA;IACAK,KAAA,WAAAA,MAAA;MACA,SAAA1B,IAAA;QACA,KAAA2B,SAAA;MACA,gBAAA3B,IAAA;QACA,KAAA4B,QAAA;MACA,gBAAA5B,IAAA;QACA,KAAA6B,QAAA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAF,SAAA,WAAAA,UAAA;MAAA,IAAAG,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,MACAT,KAAA,CAAAf,QAAA,KAAAe,KAAA,CAAAR,mBAAA,IAAAQ,KAAA,CAAAR,mBAAA;gBAAAe,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAT,KAAA,CAAAU,QAAA,CAAAC,OAAA;cAAA,OAAAJ,QAAA,CAAAK,MAAA;YAAA;cAGAZ,KAAA,CAAAnB,aAAA,GAAAgC,IAAA;gBACA,IAAAC,KAAA;kBACAC,cAAA,EAAAf,KAAA,CAAA3B,SAAA;kBACA2C,mBAAA;kBACAxB,mBAAA,EAAAQ,KAAA,CAAAR,mBAAA;kBACAyB,cAAA,EAAAjB,KAAA,CAAAxB,cAAA;kBACA0C,cAAA,EAAAlB,KAAA,CAAAvB,cAAA;kBACA0C,KAAA,EAAAnB,KAAA,CAAAZ,KAAA;kBACAgC,cAAA,EAAApB,KAAA,CAAAX,UAAA;kBACAgC,YAAA;kBACAC,aAAA,EAAAxD,OAAA;gBACA;gBACAF,YAAA,CAAAkD,KAAA,EAAAD,IAAA,WAAAU,GAAA;kBACA,IAAAA,GAAA,CAAAC,SAAA;oBACAxB,KAAA,CAAAU,QAAA;sBACAe,OAAA;sBACAvD,IAAA;oBACA;oBACA8B,KAAA,CAAA0B,KAAA;oBACA1B,KAAA,CAAAT,SAAA;oBACA;kBACA;oBACAS,KAAA,CAAAU,QAAA;sBACAe,OAAA,EAAAF,GAAA,CAAAI,OAAA;sBACAzD,IAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAqC,QAAA,CAAAqB,IAAA;UAAA;QAAA,GAAAxB,OAAA;MAAA;IACA;IACA;IACAN,QAAA,WAAAA,SAAA;MAAA,IAAA+B,MAAA;MAAA,OAAA5B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2B,SAAA;QAAA,IAAAhB,KAAA;QAAA,OAAAZ,mBAAA,GAAAG,IAAA,UAAA0B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxB,IAAA,GAAAwB,SAAA,CAAAvB,IAAA;YAAA;cAAA,MACAoB,MAAA,CAAA5C,QAAA,KAAA4C,MAAA,CAAArC,mBAAA,IAAAqC,MAAA,CAAArC,mBAAA;gBAAAwC,SAAA,CAAAvB,IAAA;gBAAA;cAAA;cACAoB,MAAA,CAAAnB,QAAA,CAAAC,OAAA;cAAA,OAAAqB,SAAA,CAAApB,MAAA;YAAA;cAGAE,KAAA;gBACAC,cAAA,EAAAc,MAAA,CAAAxD,SAAA;gBACA2C,mBAAA;gBACAxB,mBAAA,EAAAqC,MAAA,CAAArC,mBAAA;gBACAyB,cAAA,EAAAY,MAAA,CAAArD,cAAA;gBACA0C,cAAA,EAAAW,MAAA,CAAApD,cAAA;gBACA0C,KAAA,EAAAU,MAAA,CAAAzC,KAAA;gBACAgC,cAAA,EAAAS,MAAA,CAAAxC,UAAA;gBACAgC,YAAA;gBACAC,aAAA,EAAAxD,OAAA;cACA;cACAF,YAAA,CAAAkD,KAAA,EAAAD,IAAA,WAAAU,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAK,MAAA,CAAAnB,QAAA;oBACAe,OAAA;oBACAvD,IAAA;kBACA;kBACA2D,MAAA,CAAAH,KAAA;kBACAG,MAAA,CAAAtC,SAAA;kBACA;gBACA;kBACAsC,MAAA,CAAAnB,QAAA;oBACAe,OAAA,EAAAF,GAAA,CAAAI,OAAA;oBACAzD,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA8D,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IACA;IACA/B,QAAA,WAAAA,SAAA;MAAA,IAAAkC,MAAA;MAAA,OAAAhC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+B,SAAA;QAAA,IAAApB,KAAA;QAAA,OAAAZ,mBAAA,GAAAG,IAAA,UAAA8B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5B,IAAA,GAAA4B,SAAA,CAAA3B,IAAA;YAAA;cAAA,MACAwB,MAAA,CAAAhD,QAAA,KAAAgD,MAAA,CAAAzC,mBAAA,IAAAyC,MAAA,CAAAzC,mBAAA;gBAAA4C,SAAA,CAAA3B,IAAA;gBAAA;cAAA;cACAwB,MAAA,CAAAvB,QAAA,CAAAC,OAAA;cAAA,OAAAyB,SAAA,CAAAxB,MAAA;YAAA;cAGAE,KAAA;gBACAC,cAAA,EAAAkB,MAAA,CAAA5D,SAAA;gBACA2C,mBAAA;gBACAxB,mBAAA,EAAAyC,MAAA,CAAAzC,mBAAA;gBACAyB,cAAA,EAAAgB,MAAA,CAAAzD,cAAA;gBACA0C,cAAA,EAAAe,MAAA,CAAAxD;cACA;cACAb,YAAA,CAAAkD,KAAA,EAAAD,IAAA,WAAAU,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAS,MAAA,CAAAvB,QAAA;oBACAe,OAAA;oBACAvD,IAAA;kBACA;kBACA+D,MAAA,CAAAP,KAAA;kBACAO,MAAA,CAAA1C,SAAA;kBACA;gBACA;kBACA0C,MAAA,CAAAvB,QAAA;oBACAe,OAAA,EAAAF,GAAA,CAAAI,OAAA;oBACAzD,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAkE,SAAA,CAAAR,IAAA;UAAA;QAAA,GAAAM,QAAA;MAAA;IACA;IACA;IACAG,MAAA,WAAAA,OAAA;MACAxE,YAAA,MAAAyE,MAAA,OAAAC,MAAA;IACA;EACA;AACA", "ignoreList": []}]}
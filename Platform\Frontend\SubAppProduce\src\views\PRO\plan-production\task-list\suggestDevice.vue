<template>
  <div class="contentBox">
    <div class="device-total">
      <div class="first">加工设备数量<span>2</span></div>
      <div class="second">累计加工时间<span>10h</span></div>
      <div class="third">累计切割米数<span>200.25</span></div>
    </div>
    <div class="device-list">
      <div class="device-info">
        <div class="image"><ElTableEmpty :empty-content="emptyContent" /></div>
        <div class="info">信息</div>
      </div>
    </div>
  </div>
</template>
<script>
import ElTableEmpty from '@/components/ElTableEmpty/index.vue'
export default {
  name: 'SuggestDevice',
  components: {
    ElTableEmpty
  },
  data() {
    return {
      emptyContent: '暂无图片'
    }
  },
  methods: {}
}
</script>
<style scoped lang="scss">
@import "~@/styles/mixin.scss";
.contentBox {
  height: 70vh;

  .device-total {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    div {
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 16px;
      border-radius: 4px;
      font-size: 14px;
      span {
        font-weight: bold;
        font-size: 16px;
        margin-left: 12px;
      }
    }
    div:nth-child(1) {
      margin-left: 8px;
      margin-right: 8px;
      background-color: rgba(0, 72, 152, .1);
      color: rgba(0, 72, 152, 1);
    }
    div:nth-child(2) {
      margin-left: 8px;
      margin-right: 8px;
      background-color: rgba(0, 141, 80, .1);
      color: rgba(0, 141, 80, 1);
    }
    div:nth-child(3) {
      margin-left: 8px;
      margin-right: 8px;
      background-color: rgba(33, 77, 194, .1);
      color: rgba(33, 77, 194, 1);
    }
  }
  .device-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    .device-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      .image {
        width: 50%;
        height: 270px;
        border-radius: 4px;
        background-color: #f5f5f5;
      }
      .info {
        flex: 1;
        padding-left: 16px;
      }
    }
  }
}
</style>

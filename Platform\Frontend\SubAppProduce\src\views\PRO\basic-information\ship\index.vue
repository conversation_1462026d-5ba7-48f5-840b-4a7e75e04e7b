<template>
  <div class="x-container">
    <top-header style="padding: 0 8px; margin-bottom: 10px">
      <template #left>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        <el-button type="success" @click="carImport">导 入</el-button>
      </template>
      <template #right>
        <!--        <el-select v-model="listQuery.Factory_Id" style="margin-right: 8px" placeholder="服务工厂" clearable="" @change="factoryChange">
          <el-option
            v-for="item in factoryOption"
            :key="item.Id"
            :label="item.Name"
            :value="item.Id"
          />
        </el-select>-->
        <el-input v-model="listQuery.Shipnumber" style="width: 180px" placeholder="请输入船号" clearable="" @change="licenseChange" />
        <el-button type="primary" @click="licenseChange">查 询</el-button>
        
      </template>
    </top-header>
    <main class="cs-main">
      <card
        v-for="(item, index) in list"
        :key="index"
        :item="item"
        @edit="handleEdit"
        @delete="handleDelete"
      />
    </main>
    <!-- :class="[{'mr-8':!index%4}]" -->
    <div class="cs-pagination-container">
      <Pagination
        :total="total"
        :page.sync="listQuery.Page"
        :limit.sync="listQuery.PageSize"
        :page-sizes="tablePageSize"
        @pagination="fetchData"
      />
    </div>
    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
       class="plm-custom-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      width="40%"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        ref="content"
        @close="handleClose"
        @refresh="fetchData"
      />
    </el-dialog>

  </div>
</template>

<script>
import Card from './component/Card'
import TopHeader from '@/components/TopHeader'
import addEdit from '@/views/PRO/basic-information/ship/component/AddEdit'
import CarImport from './component/Import'
import { GetPageInfo,DeleteBoat } from '@/api/PRO/car'
import Pagination from '@/components/Pagination'
import { GetFactoryList } from '@/api/PRO/factory'
import { tablePageSize } from '@/views/PRO/setting'

export default {
  name: 'PROBasicVehicle',
  components: {
    Card,
    TopHeader,
    CarImport,
    addEdit,
    Pagination
  },
  data() {
    return {
      tablePageSize: tablePageSize,
      total: 0,
      listQuery: {
        PageSize: 12,
        Page: 1,
        // Project_Id: '',
        Shipnumber: ''
      },
      value: '',
      title: '',
      list: [],
      currentComponent: '',
      dialogVisible: false,
      carOptions: [],
      factoryOption: [],
      factoryId: ''
    }
  },
  mounted() {
    this.fetchData()
    // this.getFactory()
  },
  methods: {
    fetchData() {
      GetPageInfo(this.listQuery).then(res => {
        if (res.IsSucceed) {
          this.list = res.Data.Data
          this.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    factoryChange(v) {
      this.fetchData()
    },
    // getFactory() {
    //   GetFactoryList({}).then(res => {
    //     if (res.IsSucceed) {
    //       this.factoryOption = res.Data
    //     } else {
    //       this.$message({
    //         message: res.Message,
    //         type: 'error'
    //       })
    //     }
    //   })
    // },
    licenseChange(v) {
      this.fetchData()
    },
    handleAdd() {
      this.currentComponent = 'addEdit'
      this.title = '新增船舶'
      this.dialogVisible = true
    },
    handleEdit(row) {
      console.log(row,'row');
      
      this.currentComponent = 'addEdit'
      this.title = '编辑船舶'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].editInit(row)
      })
    },
    handleDelete(row) {
      this.$confirm('是否删除该船舶?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteBoat({
          id: row.Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.$emit('close')
            this.fetchData()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    carImport() {
      this.currentComponent = 'CarImport'
      this.title = '导入'
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
.x-container {
  padding: 16px 8px;
  .cs-top-header-box {
    background: #F4F5F7;
    padding: 0;
  }
  .mb-8{
    margin-bottom: 8px;
  }
  .mt-8{
    margin-top: 8px;
  }
  .ml-8{
    margin-left: 8px;
  }
  .mr-8{
    margin-right: 8px;
  }
  .cs-main{
    display: flex;
    flex-wrap: wrap;
  }
  .cs-pagination-container {
    padding: 0 8px;
  }
}

</style>


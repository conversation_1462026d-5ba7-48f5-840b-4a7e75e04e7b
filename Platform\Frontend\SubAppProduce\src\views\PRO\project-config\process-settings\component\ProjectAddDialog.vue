<template>
  <div class="form-wrapper">
    <div class="instruction">请选择项目，添加所选项目的<span>所有工序</span></div>
    <div>
      <el-form ref="form" :model="form" label-width="82px">
        <el-form-item v-if="!projectList.length" label="项目名称：">
          <div>暂无可同步的项目</div>
        </el-form-item>
        <el-form-item v-else label="项目名称：">
          <el-select v-model="form.From_Sys_Project_Id" placeholder="请选择项目" style="width: 300px">
            <el-option v-for="(item, index) in projectList" :key="index" :label="item.Short_Name" :value="item.Sys_Project_Id" :disabled="item.Sys_Project_Id===sysProjectId" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="btn-x">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button v-if="projectList.length" type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
    </div>
  </div>
</template>

<script>
import { GetProcessOfProjectList, SyncProjectProcessFromProject } from '@/api/PRO/technology-lib'
export default {
  components: {
  },
  props: {
    sysProjectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      btnLoading: false,
      projectList: [],
      form: {
        From_Sys_Project_Id: ''
      }
    }
  },
  async mounted() {
    await this.getProcessOfProjectList()
  },
  methods: {
    async getProcessOfProjectList() {
      const res = await GetProcessOfProjectList({ })
      if (res.IsSucceed) {
        this.projectList = res.Data
      }
    },
    handleSubmit() {
      if (this.form.From_Sys_Project_Id === '') return this.$message.warning('请选择项目')
      this.btnLoading = true
      SyncProjectProcessFromProject({
        From_Sys_Project_Id: this.form.From_Sys_Project_Id,
        To_Sys_Project_Id: this.sysProjectId
      }).then(res => {
        if (res.IsSucceed) {
          this.$emit('refresh')
          this.$emit('close')
          this.$message.success('同步成功！')
        } else {
          this.$message.error(res.msg)
        }
        this.btnLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .form-wrapper {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    max-height: 70vh;

    .btn-x {
      padding-top: 16px;
      text-align: right;
    }
    .instruction {
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    color: #1890ff;
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    font-weight: 500;
  }
  }
</style>

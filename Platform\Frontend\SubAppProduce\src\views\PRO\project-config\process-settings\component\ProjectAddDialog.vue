<template>
  <div class="form-wrapper">
    <div class="instruction">请选择项目，添加所选项目的<span>所有工序</span></div>
    <div>
      <el-form ref="form" :model="form" label-width="82px">
        <el-form-item label="项目名称：">
          <el-select v-model="form.From_Sys_Project_Id" placeholder="请选择项目" style="width: 300px">
            <el-option label="区域一" value="shanghai" />
            <el-option label="区域二" value="beijing" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="btn-x">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
    </div>
  </div>
</template>

<script>
import { GetProcessOfProjectList, SyncProjectProcessFromProject } from '@/api/PRO/technology-lib'
export default {
  components: {
  },
  data() {
    return {
      form: {
        From_Sys_Project_Id: ''
      }
    }
  },
  async mounted() {

  },
  methods: {
    handleSubmit() {

    }
  }
}
</script>

<style scoped lang="scss">
  .form-wrapper {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    max-height: 70vh;
    .instruction {
      font-size: 14px;
      color: #333333;
      margin-bottom: 30px;
      span {
        color: #000000;
      }
    }
    .btn-x {
      padding-top: 16px;
      text-align: right;
    }
  }
</style>

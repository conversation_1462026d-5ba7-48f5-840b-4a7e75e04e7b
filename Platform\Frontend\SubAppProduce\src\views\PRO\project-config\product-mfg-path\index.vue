<template>
  <div class="app-container abs100">
    <ProjectData @setProjectData="setProjectData" />
    <div class="card-x">
      <div class="card-x-top">
        <el-button type="success" :disabled="!sysProjectId" @click="handleAdd">新增</el-button>

        <el-button type="primary" :disabled="!sysProjectId" @click="handleAddProject">同步项目配置</el-button>
        <el-button type="primary" :disabled="!sysProjectId" @click="handleReset">恢复工厂默认配置</el-button>
      </div>
      <div class="table-section">
        <bt-table
          ref="projectTable"
          code="ProductMftPathConfig"
          :custom-table-config="tableConfig"
          :grid-data-handler="handleGridData"
          :loading="loading"
        >
          <template #Type="{row}">
            <div>
              <span :style="{color:row.Type===1 ?'#d29730': row.Type===2?'#20bbc7':'#de85e4'}">
                {{ getCurBomName(row.Bom_Level) }}工艺
              </span>
            </div>
          </template>
          <template #Type1="{row}">
            {{ row.Component_Type }}
          </template>
          <template #actions="{row}">
            <div>
              <el-button v-if="!row.isSysDefault" type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" style="color: red" @click="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </bt-table>

      </div>
    </div>
    <el-dialog
      v-dialogDrag
      :title="title"
      class="plm-custom-dialog"
      :visible.sync="dialogVisible"
      width="30%"
      top="5vh"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        ref="content"
        :sys-project-id="sysProjectId"
        @refresh="fetchData"
        @close="handleClose"
      />
    </el-dialog>

    <AddDialog ref="dialog" :bom-list="bomList" :sys-project-id="sysProjectId" @refresh="fetchData" />
  </div>
</template>

<script>
import ProjectData from '../components/ProjectData.vue'
import { GetLibList, DeleteTechnology, RestoreFactoryTechnologyFromProject } from '@/api/PRO/technology-lib'
import ProjectAdd from './component/ProjectAddDialog.vue'
import AddDialog from '@/views/PRO/process-path/compoments/Add.vue'
import { GetBOMInfo, getBomName } from '@/views/PRO/bom-setting/utils'

export default {
  name: 'PROProductMfgPath',
  components: {
    ProjectData,
    ProjectAdd,
    AddDialog
  },
  data() {
    return {
      dialogVisible: false,
      currentComponent: '',
      bomList: [],
      title: '',
      sysProjectId: '',
      tableConfig: {
        tableColumns: [],
        tableActions: [],
        tableData: [],
        operateOptions: {
          width: 120,
          align: 'center',
          isShow: false
        }
      },
      loading: false,
      selectedProjects: []
    }
  },
  async mounted() {
    // this.fetchData()
    const { list } = await GetBOMInfo()
    this.bomList = list
    console.log('bomList', this.bomList)
  },
  methods: {
    getCurBomName(code) {
      const currentBomInfo = this.bomList.find(item => {
        return item.Code.toString() === code.toString()
      })
      return currentBomInfo?.Display_Name || ''
    },
    async fetchData() {
      if (!this.sysProjectId) {
        this.tableConfig.tableData = []
        return
      }
      this.loading = true
      try {
        const params = {
          // Bom_Level: 1,
          Type: 0,
          Sys_Project_Id: this.sysProjectId
        }
        const res = await GetLibList(params)
        if (res.IsSucceed) {
          this.tableConfig.tableData = await Promise.all(
            res.Data.map(async v => {
              v.isSysDefault = !v.Sys_Project_Id
              v.bomName = await getBomName(v.Type)
              return v
            })
          )
        }
      } catch (error) {
        console.log('error', error)
      } finally {
        this.loading = false
      }
    },
    handleGridData(data) {
      console.log('data', data)
      return data
    },
    handleAdd() {
      console.log('新增')
      this.$nextTick(() => {
        this.$refs['dialog'].handleOpen()
      })
    },
    handleEdit(row) {
      console.log(row)
      this.$nextTick(() => {
        this.$refs['dialog'].handleOpen(row)
      })
    },
    handleDelete(row) {
      this.$confirm('是否删除该工艺', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tbLoading = true
        DeleteTechnology({
          technologyId: row.Id,
          sysProjectId: this.sysProjectId
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.fetchData()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleReset() {
      this.$confirm('此操作将会恢复到工厂的产品生产路径, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        RestoreFactoryTechnologyFromProject({
          Sys_Project_Id: this.sysProjectId
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '恢复成功!'
            })
            this.fetchData()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      })
    },
    handleAddProject() {
      console.log('同步项目配置')
      this.dialogVisible = true
      this.currentComponent = 'ProjectAdd'
      this.title = '同步项目配置'
    },

    handleClose() {
      this.dialogVisible = false
      this.currentComponent = ''
      this.title = ''
    },
    setProjectData(data) {
      this.selectedProjects = data
      this.sysProjectId = data?.Sys_Project_Id || ''
      console.log('selectedProjects', this.selectedProjects)
      this.fetchData()
    }
  }

}
</script>

<style lang="scss" scoped>
.app-container{
  display: flex;
  flex-direction: row;
  height: 100%;
  .card-x{
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    .card-x-top{
      margin-bottom: 16px;
    }
    .table-section {
      flex: 1;
      background: #fff;
      border-radius: 4px;
      overflow: hidden;
    }
  }
}
</style>

<template>
  <div class="app-container abs100">
    <ProjectData />
    <div class="card-x">
      <div class="card-x-top">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="danger" @click="handleDelete">删除</el-button>

        <el-button type="primary" @click="handleAddProject">从项目添加</el-button>
        <el-button type="primary" @click="handleAddFactory">从工厂添加</el-button>
      </div>
      <div class="table-section">
        <bt-table
          ref="projectTable"
          code="ProductMftPathConfig"
          :custom-table-config="tableConfig"
          :grid-data-handler="handleGridData"
          :loading="loading"
          @selection-change="handleSelectionChange"
        />
      </div>
    </div>
    <el-dialog
      v-dialogDrag
      :title="title"
      class="plm-custom-dialog"
      :visible.sync="dialogVisible"
      width="80%"
      top="5vh"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        ref="content"
        @close="handleClose"
      />
    </el-dialog>
  </div>
</template>

<script>
import ProjectData from '../components/ProjectData.vue'
import { GetLibList } from '@/api/PRO/technology-lib'
import ProjectAdd from './component/ProjectAddDialog.vue'
import FactoryAdd from './component/FactoryAddDialog.vue'
export default {
  name: 'PROProductMfgPath',
  components: {
    ProjectData,
    ProjectAdd,
    FactoryAdd
  },
  data() {
    return {
      dialogVisible: false,
      currentComponent: '',
      title: '',
      tableConfig: {
        tableColumns: [],
        tableActions: [],
        tableData: [],
        checkbox: true,
        operateOptions: {
          width: 120,
          align: 'center',
          isShow: false
        }
      },
      loading: false,
      selectedProjects: []
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      try {
        const params = {
          Id: '',
          Type: 0
        }
        const res = await GetLibList(params)
        if (res.IsSucceed) {
          this.tableConfig.tableData = res.Data
        }
      } catch (error) {
        console.log('error', error)
      } finally {
        this.loading = false
      }
    },
    handleGridData(data) {
      console.log('data', data)
      return data
    },
    handleSelectionChange(selection) {
      console.log('selection', selection)
    },
    handleAdd() {
      console.log('新增')
    },
    handleDelete() {
      console.log('删除')
    },
    handleAddProject() {
      console.log('从项目添加')
      this.dialogVisible = true
      this.currentComponent = 'ProjectAdd'
      this.title = '从项目添加'
    },
    handleAddFactory() {
      console.log('从工厂添加')
      this.dialogVisible = true
      this.currentComponent = 'FactoryAdd'
      this.title = '从工厂添加'
    },
    handleClose() {
      this.dialogVisible = false
      this.currentComponent = ''
      this.title = ''
    }
  }

}
</script>

<style lang="scss" scoped>
.app-container{
  display: flex;
  flex-direction: row;
  height: 100%;
  .card-x{
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    .card-x-top{
      margin-bottom: 16px;
    }
    .table-section {
      flex: 1;
      background: #fff;
      border-radius: 4px;
      overflow: hidden;
    }
  }
}
</style>

<template>
  <div class="add-project-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="项目编号：">
          <el-input
            v-model="searchForm.ProjectCode"
            placeholder="请输入"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="项目简称：">
          <el-input
            v-model="searchForm.ProjectAbbr"
            placeholder="请输入"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 提示信息 -->
    <div class="instruction-section">
      <div class="instruction">
        请选择工厂级配置的工序类型，添加到项目
      </div>
      <el-button type="primary" @click="handleAddToList"> 加入列表 </el-button>
    </div>

    <!-- 项目表格 -->
    <div class="table-section">
      <bt-table
        ref="projectTable"
        code="AddProjectList"
        :custom-table-config="tableConfig"
        :grid-data-handler="handleGridData"
        :loading="loading"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      />
    </div>

    <!-- 底部按钮 -->
    <div class="footer-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :disabled="selectedProjects.length === 0" @click="handleConfirm">
        确定
      </el-button>
    </div>
  </div>
</template>

<script>
import { GetProjectPageList } from '@/api/PRO/project'

export default {
  name: 'AddProject',
  props: {
    typeId: {
      type: String,
      default: ''
    },
    addLevel: {
      type: Number,
      default: 1
    },
    parentId: {
      type: String,
      default: ''
    },
    activeType: {
      type: String,
      default: ''
    },
    typeCode: {
      type: String,
      default: ''
    },
    isComp: {
      type: Boolean,
      default: false
    },
    showDirect: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {
        ProjectCode: '',
        ProjectAbbr: ''
      },
      selectedProjects: [],
      tableConfig: {
        tableColumns: [],
        tableActions: [],
        tableData: [],
        checkbox: false,
        operateOptions: {
          width: 120,
          align: 'center',
          isShow: false
        }

      }
    }
  },
  created() {
    this.fetchProjectList()
  },
  methods: {

    // 获取项目列表
    async fetchProjectList() {
      this.loading = true
      try {
        const params = {
          Page: this.tableConfig.currentPage,
          PageSize: this.tableConfig.pageSize
          // SortName: 'Create_Date',
          // SortOrder: 'DESC',
          // Search: '',
          // ParameterJson: JSON.stringify({
          //   ProjectCode: this.searchForm.ProjectCode,
          //   ProjectAbbr: this.searchForm.ProjectAbbr
          // })
        }

        const res = await GetProjectPageList(params)
        if (res.IsSucceed) {
          const table = res.Data.Data || []

          // 为数据添加行号
          table.forEach((item, index) => {
            // item.IscheckMethod = undefined
          })
          this.tableConfig.tableData = table
        } else {
          this.$message.error(res.Message || '获取项目列表失败')
        }
      } catch (error) {
        console.error('获取项目列表失败:', error)
        this.$message.error('获取项目列表失败')
      } finally {
        this.loading = false
      }
    },

    handleGridData(data) {
      console.log(data, 3313)
      return data
    },
    // 搜索
    handleSearch() {
      this.tableConfig.currentPage = 1
      this.fetchProjectList()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        ProjectCode: '',
        ProjectAbbr: ''
      }
      this.tableConfig.currentPage = 1
      this.fetchProjectList()
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedProjects = selection
    },

    // 行点击
    handleRowClick(row) {
      // 可以在这里添加行点击逻辑
      console.log('点击行:', row)
    },

    // 取消
    handleCancel() {
      this.$emit('close')
    },

    // 确认选择
    handleConfirm() {
      if (this.selectedProjects.length === 0) {
        this.$message.warning('请至少选择一个项目')
        return
      }

      // 这里可以处理选中的项目数据
      console.log('选中的项目:', this.selectedProjects)

      // 触发父组件事件
      this.$emit('close')
      this.$emit('getTreeList')

      this.$message.success(`已选择 ${this.selectedProjects.length} 个项目`)
    },
    handleAddToList() {
      console.log('加入列表')
    }
  }
}
</script>

<style scoped lang="scss">
.add-project-container {
  height:70vh;

  display: flex;
  flex-direction: column;

  .search-section {
    background: #fff;
    border-radius: 4px;
  }

  .instruction-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .instruction {
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    color: #1890ff;
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    font-weight: 500;
  }

  .table-section {
    flex: 1;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
  }

  .footer-actions {
    display: flex;
    justify-content: flex-end;
    padding: 0px 8px 0 0;
    background: #fff;
  }
}
</style>

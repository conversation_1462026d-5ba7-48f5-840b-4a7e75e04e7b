{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\index.vue", "mtime": 1757468112156}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgZ2V0VGJJbmZvIGZyb20gJ0AvbWl4aW5zL1BSTy9nZXQtdGFibGUtaW5mbycNCmltcG9ydCBEeW5hbWljRGF0YVRhYmxlIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljRGF0YVRhYmxlL0R5bmFtaWNEYXRhVGFibGUnDQppbXBvcnQgVG9wSGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9Ub3BIZWFkZXInDQppbXBvcnQgeyBEZWxldGVXb3JraW5nVGVhbXMsIEdldFdvcmtpbmdUZWFtcywgR2V0V29ya2luZ1RlYW1zUGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInDQppbXBvcnQgZGV0YWlsIGZyb20gJy4vY29tcG9uZW50L2RldGFpbCcNCmltcG9ydCBpbmZvIGZyb20gJy4vY29tcG9uZW50L2luZm8nDQppbXBvcnQgZ2V0Q29tbW9uRGF0YSBmcm9tICdAL21peGlucy9QUk8vZ2V0LWNvbW1vbi1kYXRhJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQUk9Hcm91cCcsDQogIGNvbXBvbmVudHM6IHsNCiAgICBEeW5hbWljRGF0YVRhYmxlLA0KICAgIFRvcEhlYWRlciwNCiAgICBpbmZvLA0KICAgIGRldGFpbA0KICB9LA0KICBtaXhpbnM6IFtnZXRUYkluZm8sIGdldENvbW1vbkRhdGFdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0YkNvbmZpZzogew0KICAgICAgICBQYWdlcl9BbGlnbjogJ2NlbnRlcicNCiAgICAgIH0sDQogICAgICBxdWVyeUluZm86IHsNCiAgICAgICAgUGFnZTogMSwNCiAgICAgICAgUGFnZVNpemU6IDEwLA0KICAgICAgICBTb3J0TmFtZTogJ1NvcnQnLA0KICAgICAgICBTb3J0T3JkZXI6ICdhc2MnLA0KICAgICAgICBQYXJhbWV0ZXJKc29uOiBbXQ0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgY29sdW1uczogW10sDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgdG90YWw6IDAsDQogICAgICB0YkxvYWRpbmc6IGZhbHNlLA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBzZWxlY3RMaXN0OiBbXSwNCiAgICAgIGtleXdvcmRzOiAnJw0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBjb2x1bW5zKGUpIHsNCiAgICAgIC8vIOi9pumXtOacquW8gOWQr+S4jeaYvuekuuaJgOWxnui9pumXtOS/oeaBrw0KICAgICAgaWYgKCF0aGlzLkZhY3RvcnlEZXRhaWxEYXRhLklzX1dvcmtzaG9wX0VuYWJsZWQpIHsNCiAgICAgICAgZS5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgICBpZiAoaXRlbS5Db2RlID09PSAnV29ya3Nob3BfTmFtZScpIHsNCiAgICAgICAgICAgIGl0ZW0uSXNfRGlzcGxheSA9IGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICAvLyDojrflj5blt6XljoLor6bmg4UNCiAgICBhd2FpdCB0aGlzLmdldEN1ckZhY3RvcnkoKQ0KICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcoJ3Byb19ncm91cF9saXN0JykNCiAgICBhd2FpdCB0aGlzLmZldGNoRGF0YSgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBmZXRjaERhdGEoKSB7DQogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgIEdldFdvcmtpbmdUZWFtc1BhZ2VMaXN0KHsga2V5d29yZHM6IHRoaXMua2V5d29yZHMsIHBhZ2VJbmZvOiB0aGlzLnF1ZXJ5SW5mbyB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnRiRGF0YSA9IHJlcy5EYXRhLkRhdGENCiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzLkRhdGEuVG90YWxDb3VudA0KICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMudG90YWwpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVDbG9zZSgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UobGlzdCkgew0KICAgICAgY29uc29sZS5sb2coJ2xpc3QnLCBsaXN0KQ0KICAgICAgdGhpcy5zZWxlY3RMaXN0ID0gbGlzdC5yZWNvcmRzDQogICAgfSwNCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnZGV0YWlsJw0KICAgICAgdGhpcy50aXRsZSA9ICfmlrDlop4nDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdERhdGEoJycsIHRoaXMuRmFjdG9yeURldGFpbERhdGEuSXNfV29ya3Nob3BfRW5hYmxlZCkNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVFZGl0KHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ2RldGFpbCcNCiAgICAgIHRoaXMudGl0bGUgPSAn57yW6L6RJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXREYXRhKHJvdy5JZCwgdGhpcy5GYWN0b3J5RGV0YWlsRGF0YS5Jc19Xb3Jrc2hvcF9FbmFibGVkKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZURldGFpbChyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdpbmZvJw0KICAgICAgdGhpcy50aXRsZSA9ICfmn6XnnIsnDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdERhdGEocm93LCB0aGlzLkZhY3RvcnlEZXRhaWxEYXRhLklzX1dvcmtzaG9wX0VuYWJsZWQpDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKGlzQWxsLCByb3cpIHsNCiAgICAgIGNvbnN0IGlkcyA9ICFpc0FsbCA/IHJvdy5JZCA6IHRoaXMuc2VsZWN0TGlzdC5tYXAoKGkpID0+IGkuSWQpLnRvU3RyaW5nKCkNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuWIoOmZpOmAieS4reePree7hD8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZygnaWQnLCBpZHMpDQogICAgICAgICAgRGVsZXRlV29ya2luZ1RlYW1zKHsNCiAgICAgICAgICAgIGlkczogaWRzLnRvU3RyaW5nKCkNCiAgICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy54VGFibGUuY2xlYXJDaGVja2JveFJvdygpDQogICAgICAgICAgICAgIHRoaXMuc2VsZWN0TGlzdCA9IFtdDQogICAgICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlU2VhcmNoKCkgew0KICAgICAgdGhpcy4kcmVmcy54VGFibGUuY2xlYXJDaGVja2JveFJvdygpDQogICAgICB0aGlzLnNlbGVjdExpc3QgPSBbXQ0KICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgIH0sDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmtleXdvcmRzID0gJycNCiAgICAgIHRoaXMuJHJlZnMueFRhYmxlLmNsZWFyQ2hlY2tib3hSb3coKQ0KICAgICAgdGhpcy5zZWxlY3RMaXN0ID0gW10NCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2JA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/basic-information/group", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <top-header padding=\"0\">\r\n        <template #right>\r\n          <el-form label-width=\"80px\" :inline=\"true\" @submit.native.prevent>\r\n            <el-form-item label=\"班组名称\">\r\n              <el-input v-model=\"keywords\" placeholder=\"请输入\" clearable @keyup.enter.native=\"handleSearch\" />\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n              <el-button @click=\"reset\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </template>\r\n        <template #left>\r\n          <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\r\n          <el-button\r\n            type=\"danger\"\r\n            :disabled=\"!selectList.length\"\r\n            @click=\"handleDelete(true)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </top-header>\r\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          v-loading=\"tbLoading\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          element-loading-spinner=\"el-icon-loading\"\r\n          element-loading-text=\"拼命加载中\"\r\n          empty-text=\"暂无数据\"\r\n          height=\"100%\"\r\n          :data=\"tbData\"\r\n          stripe\r\n          resizable\r\n          :auto-resize=\"true\"\r\n          class=\"cs-vxe-table\"\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"handleSelectionChange\"\r\n          @checkbox-change=\"handleSelectionChange\"\r\n        >\r\n          <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n          <vxe-column\r\n            v-for=\"(item, index) in columns\"\r\n            :key=\"index\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            :visible=\"item.Is_Display\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <span v-if=\"item.Code === 'Manager_UserName'\">\r\n                <div>{{ row.Manager_UserName || \"-\" }}</div>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Load_Unit'\">\r\n                <div>{{ row.Load_Unit || \"-\" }}</div>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Workshop_Name'\">\r\n                <div>{{ row.Workshop_Name || \"-\" }}</div>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Outsource'\">\r\n                <el-tag v-if=\"row.Is_Outsource\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Enabled'\">\r\n                <el-tag v-if=\"row.Is_Enabled\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Sort'\">\r\n                <span>{{ item.Sort || item.Sort === 0 ? row[item.Code] : '-' }}</span>\r\n              </span>\r\n              <span v-else>{{ row[item.Code] || \"-\" }}</span>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column fixed=\"right\" title=\"操作\" width=\"160\" show-overflow align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              <el-button type=\"text\" @click=\"handleDetail(row)\">查看</el-button>\r\n              <el-button type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\r\n              <el-button\r\n                type=\"text\"\r\n                style=\"color: red\"\r\n                @click=\"handleDelete(false, row)\"\r\n              >删除</el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n        <!-- <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          :columns=\"columns\"\r\n          :config=\"tbConfig\"\r\n          :data=\"tbData\"\r\n          :page=\"queryInfo.Page\"\r\n          :total=\"total\"\r\n          border\r\n          stripe\r\n          class=\"cs-plm-dy-table\"\r\n          @multiSelectedChange=\"handleSelectionChange\"\r\n          @gridPageChange=\"handlePageChange\"\r\n          @gridSizeChange=\"handlePageChange\"\r\n          @tableSearch=\"tableSearch\"\r\n        >\r\n          <template slot=\"Manager_UserName\" slot-scope=\"{ row }\">\r\n            <div>{{ row.Manager_UserName || \"-\" }}</div>\r\n          </template>\r\n          <template slot=\"Load_Unit\" slot-scope=\"{ row }\">\r\n            <div>{{ row.Load_Unit || \"-\" }}</div>\r\n          </template>\r\n          <template slot=\"Workshop_Name\" slot-scope=\"{ row }\">\r\n            <div>{{ row.Workshop_Name || \"-\" }}</div>\r\n          </template>\r\n          <template slot=\"Is_Outsource\" slot-scope=\"{ row }\">\r\n            <div><el-tag v-if=\"row.Is_Outsource\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag></div>\r\n          </template>\r\n          <template slot=\"Is_Enabled\" slot-scope=\"{ row }\">\r\n            <div><el-tag v-if=\"row.Is_Enabled\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag></div>\r\n          </template>\r\n          <template slot=\"op\" slot-scope=\"{ row }\">\r\n            <el-button type=\"text\" @click=\"handleDetail(row)\">查看</el-button>\r\n            <el-button type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              style=\"color: red\"\r\n              @click=\"handleDelete(false, row)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </dynamic-data-table> -->\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"cs-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      custom-class=\"dialogCustomClass\"\r\n      width=\"900px\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :dialog-visible=\"dialogVisible\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport TopHeader from '@/components/TopHeader'\r\nimport { DeleteWorkingTeams, GetWorkingTeams, GetWorkingTeamsPageList } from '@/api/PRO/technology-lib'\r\nimport detail from './component/detail'\r\nimport info from './component/info'\r\nimport getCommonData from '@/mixins/PRO/get-common-data'\r\n\r\nexport default {\r\n  name: 'PROGroup',\r\n  components: {\r\n    DynamicDataTable,\r\n    TopHeader,\r\n    info,\r\n    detail\r\n  },\r\n  mixins: [getTbInfo, getCommonData],\r\n  data() {\r\n    return {\r\n      tbConfig: {\r\n        Pager_Align: 'center'\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n        SortName: 'Sort',\r\n        SortOrder: 'asc',\r\n        ParameterJson: []\r\n      },\r\n      currentComponent: '',\r\n      title: '',\r\n      columns: [],\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      dialogVisible: false,\r\n      selectList: [],\r\n      keywords: ''\r\n    }\r\n  },\r\n  watch: {\r\n    columns(e) {\r\n      // 车间未开启不显示所属车间信息\r\n      if (!this.FactoryDetailData.Is_Workshop_Enabled) {\r\n        e.map((item) => {\r\n          if (item.Code === 'Workshop_Name') {\r\n            item.Is_Display = false\r\n          }\r\n        })\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    // 获取工厂详情\r\n    await this.getCurFactory()\r\n    this.tbLoading = true\r\n    await this.getTableConfig('pro_group_list')\r\n    await this.fetchData()\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      GetWorkingTeamsPageList({ keywords: this.keywords, pageInfo: this.queryInfo }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data\r\n          this.total = res.Data.TotalCount\r\n          console.log(this.total)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleSelectionChange(list) {\r\n      console.log('list', list)\r\n      this.selectList = list.records\r\n    },\r\n    handleAdd() {\r\n      this.currentComponent = 'detail'\r\n      this.title = '新增'\r\n      this.dialogVisible = true\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].initData('', this.FactoryDetailData.Is_Workshop_Enabled)\r\n      })\r\n    },\r\n    handleEdit(row) {\r\n      this.currentComponent = 'detail'\r\n      this.title = '编辑'\r\n      this.dialogVisible = true\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].initData(row.Id, this.FactoryDetailData.Is_Workshop_Enabled)\r\n      })\r\n    },\r\n    handleDetail(row) {\r\n      this.currentComponent = 'info'\r\n      this.title = '查看'\r\n      this.dialogVisible = true\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].initData(row, this.FactoryDetailData.Is_Workshop_Enabled)\r\n      })\r\n    },\r\n    handleDelete(isAll, row) {\r\n      const ids = !isAll ? row.Id : this.selectList.map((i) => i.Id).toString()\r\n      this.$confirm('是否删除选中班组?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          console.log('id', ids)\r\n          DeleteWorkingTeams({\r\n            ids: ids.toString()\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.$refs.xTable.clearCheckboxRow()\r\n              this.selectList = []\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    handleSearch() {\r\n      this.$refs.xTable.clearCheckboxRow()\r\n      this.selectList = []\r\n      this.fetchData()\r\n    },\r\n    reset() {\r\n      this.keywords = ''\r\n      this.$refs.xTable.clearCheckboxRow()\r\n      this.selectList = []\r\n      this.fetchData()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.cs-dialog {\r\n  ::v-deep {\r\n    .el-dialog__body {\r\n      padding-top: 0;\r\n    }\r\n  }\r\n}\r\n::v-deep {\r\n  .cs-top-header-box {\r\n    line-height: 0px;\r\n  }\r\n}\r\n\r\n::v-deep .pagination {\r\n    justify-content: flex-end !important;\r\n    margin-top: 12px !important;\r\n    .el-input--small .el-input__inner {\r\n        height: 28px;\r\n        line-height: 28px;\r\n    }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue?vue&type=template&id=31dd5805&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue", "mtime": 1757581469561}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\draft.vue?vue&type=template&id=a3c6173a&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\draft.vue", "mtime": 1757468113354}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
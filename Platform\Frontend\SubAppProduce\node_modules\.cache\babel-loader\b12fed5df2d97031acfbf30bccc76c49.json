{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\template-print\\providers.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\template-print\\providers.js", "mtime": 1757468128204}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiOwovKiBlc2xpbnQtZGlzYWJsZSAqLwppbXBvcnQgeyBoaXByaW50IH0gZnJvbSAidnVlLXBsdWdpbi1oaXByaW50IjsKaW1wb3J0IHsgR2V0UHJlZmVyZW5jZVNldHRpbmdWYWx1ZSB9IGZyb20gIkAvYXBpL3N5cy9zeXN0ZW0tc2V0dGluZyI7CmltcG9ydCB7IEdldENvbXBhbnkgfSBmcm9tICJAL2FwaS9wbG0vc2l0ZSI7CgovLyDoh6rlrprkuYnorr7orqHlhYPntKAxCmV4cG9ydCB2YXIgRGVsaXZlcnlOb3RlUHJvdmlkZXIgPSBmdW5jdGlvbiBEZWxpdmVyeU5vdGVQcm92aWRlcihvcHMpIHsKICB2YXIgYWRkRWxlbWVudFR5cGVzID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHsKICAgIHZhciBfcmVmID0gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoY29udGV4dCkgewogICAgICB2YXIgcmVzLCBsb2dvOwogICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMjsKICAgICAgICAgICAgcmV0dXJuIEdldENvbXBhbnkoKTsKICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgcmVzID0gX2NvbnRleHQuc2VudDsKICAgICAgICAgICAgbG9nbyA9IHJlcy5EYXRhLkljb247CiAgICAgICAgICAgIGNvbnRleHQucmVtb3ZlUHJpbnRFbGVtZW50VHlwZXMoIlNoaXBtZW50Iik7CiAgICAgICAgICAgIGNvbnRleHQuYWRkUHJpbnRFbGVtZW50VHlwZXMoMSwgW25ldyBoaXByaW50LlByaW50RWxlbWVudFR5cGVHcm91cCgi5bmz5Y+wIiwgW3sKICAgICAgICAgICAgICB0aWQ6ICdTaGlwbWVudC5Mb2dvJywKICAgICAgICAgICAgICB0aXRsZTogJ0xvZ2/lm77niYcnLAogICAgICAgICAgICAgIGRhdGE6ICcnLAogICAgICAgICAgICAgIHR5cGU6ICdpbWFnZScsCiAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgZmllbGQ6ICdMb2dvJywKICAgICAgICAgICAgICAgIGhlaWdodDogNDAsCiAgICAgICAgICAgICAgICB3aWR0aDogNDAsCiAgICAgICAgICAgICAgICBzcmM6IGxvZ28KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICB0aWQ6ICdTaGlwbWVudC5RcmNvZGVUZXh0JywKICAgICAgICAgICAgICB0aXRsZTogJ+S6jOe7tOeggScsCiAgICAgICAgICAgICAgZGF0YTogJ1hTODg4ODg4ODg4JywKICAgICAgICAgICAgICB0eXBlOiAndGV4dCcsCiAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgZmllbGQ6ICdRcmNvZGVUZXh0JywKICAgICAgICAgICAgICAgIHRlc3REYXRhOiAnWFM4ODg4ODg4ODgnLAogICAgICAgICAgICAgICAgaGVpZ2h0OiA2NCwKICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMiwKICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDE4LAogICAgICAgICAgICAgICAgdGV4dFR5cGU6ICJxcmNvZGUiCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgdGlkOiAnU2hpcG1lbnQuQ29udHJhY3ROdW1iZXInLAogICAgICAgICAgICAgIHRpdGxlOiAn5YaF6YOo5ZCI5ZCM57yW5Y+3JywKICAgICAgICAgICAgICBkYXRhOiAnSFQtMDAwMScsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIHRlc3REYXRhOiAnSFQtMDAwMScsCiAgICAgICAgICAgICAgICBmaWVsZDogIkNvbnRyYWN0TnVtYmVyIiwKICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMiwKICAgICAgICAgICAgICAgIHdpZHRoOiAyMDAKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICB0aWQ6ICdTaGlwbWVudC5Db2RlJywKICAgICAgICAgICAgICB0aXRsZTogJ+WNleaNruWPtycsCiAgICAgICAgICAgICAgZGF0YTogJ0JJTS1DSy0wMDI0OScsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIHRlc3REYXRhOiAnQklNLUNLLTAwMjQ5JywKICAgICAgICAgICAgICAgIGZpZWxkOiAiQ29kZSIsCiAgICAgICAgICAgICAgICBmb250U2l6ZTogMTIsCiAgICAgICAgICAgICAgICB3aWR0aDogMjAwCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgdGlkOiAnU2hpcG1lbnQuU2VuZERhdGUnLAogICAgICAgICAgICAgIHRpdGxlOiAn5pel5pyfJywKICAgICAgICAgICAgICBkYXRhOiAnMjAyNC0wNC0wMycsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIHRlc3REYXRhOiAnMjAyNC0wNC0wMycsCiAgICAgICAgICAgICAgICBmb250U2l6ZTogMTIsCiAgICAgICAgICAgICAgICBmaWVsZDogIlNlbmREYXRlIgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50Lk51bWJlcicsCiAgICAgICAgICAgICAgdGl0bGU6ICflj5HotKfluo/lj7cnLAogICAgICAgICAgICAgIGRhdGE6ICcxJywKICAgICAgICAgICAgICB0eXBlOiAndGV4dCcsCiAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgZmllbGQ6ICdOdW1iZXInLAogICAgICAgICAgICAgICAgdGVzdERhdGE6ICcxJywKICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LkFkZHJlc3MnLAogICAgICAgICAgICAgIHRpdGxlOiAn6aG555uu5Zyw5Z2AJywKICAgICAgICAgICAgICBkYXRhOiAn5rWZ5rGf55yB57uN5YW05biC5p+v5qGl5Yy66Ym05rmW6LevMTU4N+WPtycsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIGZpZWxkOiAnQWRkcmVzcycsCiAgICAgICAgICAgICAgICB0ZXN0RGF0YTogJ+a1meaxn+ecgee7jeWFtOW4guafr+ahpeWMuumJtOa5lui3rzE1ODflj7cnLAogICAgICAgICAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgICAgICAgICAgd2lkdGg6IDMwMAogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LlByb2plY3ROYW1lJywKICAgICAgICAgICAgICB0aXRsZTogJ+mhueebruWQjeensCcsCiAgICAgICAgICAgICAgZGF0YTogJ+avlOWnhuazsOWuoua1i+ivlemhueebricsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIGZpZWxkOiAnUHJvamVjdE5hbWUnLAogICAgICAgICAgICAgICAgdGVzdERhdGE6ICfmr5Tlp4bms7DlrqLmtYvor5Xpobnnm64nLAogICAgICAgICAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgICAgICAgICAgd2lkdGg6IDI1MAogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50Lk1ha2VyTmFtZScsCiAgICAgICAgICAgICAgdGl0bGU6ICflh7rlupPkuronLAogICAgICAgICAgICAgIGRhdGE6ICdMaWx5JywKICAgICAgICAgICAgICB0eXBlOiAndGV4dCcsCiAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgZmllbGQ6ICdNYWtlck5hbWUnLAogICAgICAgICAgICAgICAgdGVzdERhdGE6ICdMaWx5JywKICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LkNvbnNpZ25lZScsCiAgICAgICAgICAgICAgdGl0bGU6ICfmlLbotKfkuronLAogICAgICAgICAgICAgIGRhdGE6ICdMaWx5JywKICAgICAgICAgICAgICB0eXBlOiAndGV4dCcsCiAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgZmllbGQ6ICdDb25zaWduZWUnLAogICAgICAgICAgICAgICAgdGVzdERhdGE6ICdMaWx5JywKICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LkNvbnNpZ25lZVRlbCcsCiAgICAgICAgICAgICAgdGl0bGU6ICfogZTns7vnlLXor50nLAogICAgICAgICAgICAgIGRhdGE6ICcxODUqKioqNDIzNScsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIGZpZWxkOiAnQ29uc2lnbmVlVGVsJywKICAgICAgICAgICAgICAgIHRlc3REYXRhOiAnMTg1KioqKjQyMzUnLAogICAgICAgICAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgICAgICAgICAgd2lkdGg6IDE1MAogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LlZlaGljbGVObycsCiAgICAgICAgICAgICAgdGl0bGU6ICfovabniYwnLAogICAgICAgICAgICAgIGRhdGE6ICfmtZlEMzg4NDMyJywKICAgICAgICAgICAgICB0eXBlOiAndGV4dCcsCiAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgZmllbGQ6ICdWZWhpY2xlTm8nLAogICAgICAgICAgICAgICAgdGVzdERhdGE6ICfmtZlEMzg4NDMyJywKICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LlRlbGVwaG9uZScsCiAgICAgICAgICAgICAgdGl0bGU6ICflj7jmnLrnlLXor50nLAogICAgICAgICAgICAgIGRhdGE6ICcxODUqKioqNDIzNScsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIGZpZWxkOiAnVGVsZXBob25lJywKICAgICAgICAgICAgICAgIHRlc3REYXRhOiAnMTg1KioqKjQyMzUnLAogICAgICAgICAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgICAgICAgICAgd2lkdGg6IDE1MAogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LkxvYWRpbmdzTmFtZScsCiAgICAgICAgICAgICAgdGl0bGU6ICfoo4Xovabnj60nLAogICAgICAgICAgICAgIGRhdGE6ICfku5Plgqjpg6jpl6gnLAogICAgICAgICAgICAgIHR5cGU6ICd0ZXh0JywKICAgICAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgICAgICBmaWVsZDogJ0xvYWRpbmdzTmFtZScsCiAgICAgICAgICAgICAgICB0ZXN0RGF0YTogJ+S7k+WCqOmDqOmXqCcsCiAgICAgICAgICAgICAgICBmb250U2l6ZTogMTIsCiAgICAgICAgICAgICAgICB3aWR0aDogMTUwCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgdGlkOiAnU2hpcG1lbnQuTG9hZGluZ3NQZXJzb25uZWxOYW1lJywKICAgICAgICAgICAgICB0aXRsZTogJ+ijhei9puePreS6uuWRmCcsCiAgICAgICAgICAgICAgZGF0YTogJ+W8oOS4iScsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIGZpZWxkOiAnTG9hZGluZ3NQZXJzb25uZWxOYW1lJywKICAgICAgICAgICAgICAgIHRlc3REYXRhOiAn5byg5LiJJywKICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMiwKICAgICAgICAgICAgICAgIHdpZHRoOiAxNTAKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICB0aWQ6ICdTaGlwbWVudC5SZWNlaXZpbmdVbml0JywKICAgICAgICAgICAgICB0aXRsZTogJ+aUtui0p+WNleS9jScsCiAgICAgICAgICAgICAgZGF0YTogJ1hY5Y2V5L2NJywKICAgICAgICAgICAgICB0eXBlOiAndGV4dCcsCiAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgZmllbGQ6ICdSZWNlaXZpbmdVbml0JywKICAgICAgICAgICAgICAgIHRlc3REYXRhOiAnWFjljZXkvY0nLAogICAgICAgICAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgICAgICAgICAgd2lkdGg6IDE1MAogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LlRyaXBzJywKICAgICAgICAgICAgICB0aXRsZTogJ+i9puasoScsCiAgICAgICAgICAgICAgZGF0YTogJ+esrOS4gOi9picsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIGZpZWxkOiAnVHJpcHMnLAogICAgICAgICAgICAgICAgdGVzdERhdGE6ICfnrKzkuIDovaYnLAogICAgICAgICAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgICAgICAgICAgd2lkdGg6IDE1MAogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LlBvdW5kX1dlaWdodCcsCiAgICAgICAgICAgICAgdGl0bGU6ICfno4Xph43vvIhrZ++8iScsCiAgICAgICAgICAgICAgZGF0YTogJzUzNjkuNjQnLAogICAgICAgICAgICAgIHR5cGU6ICd0ZXh0JywKICAgICAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgICAgICBmaWVsZDogJ1BvdW5kX1dlaWdodCcsCiAgICAgICAgICAgICAgICB0ZXN0RGF0YTogJzUzNjkuNjQnLAogICAgICAgICAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgICAgICAgICAgd2lkdGg6IDE1MAogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LlRhcmVfV2VpZ2h0JywKICAgICAgICAgICAgICB0aXRsZTogJ+earumHje+8iGtn77yJJywKICAgICAgICAgICAgICBkYXRhOiAnNTM2OS42NCcsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIGZpZWxkOiAnVGFyZV9XZWlnaHQnLAogICAgICAgICAgICAgICAgdGVzdERhdGE6ICc1MzY5LjY0JywKICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMiwKICAgICAgICAgICAgICAgIHdpZHRoOiAxNTAKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICB0aWQ6ICdTaGlwbWVudC5OZXRfV2VpZ2h0JywKICAgICAgICAgICAgICB0aXRsZTogJ+WHgOmHje+8iGtn77yJJywKICAgICAgICAgICAgICBkYXRhOiAnNTM2OS42NCcsCiAgICAgICAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIGZpZWxkOiAnTmV0X1dlaWdodCcsCiAgICAgICAgICAgICAgICB0ZXN0RGF0YTogJzUzNjkuNjQnLAogICAgICAgICAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgICAgICAgICAgd2lkdGg6IDE1MAogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgLy8gewogICAgICAgICAgICAvLyAgIHRpZDogJ1NoaXBtZW50LkN1c3RvbVRleHQnLAogICAgICAgICAgICAvLyAgIHRpdGxlOiAnJywKICAgICAgICAgICAgLy8gICBjdXN0b21UZXh0OiAn6Ieq5a6a5LmJ5paH5pysJywKICAgICAgICAgICAgLy8gICBjdXN0b206IHRydWUsCiAgICAgICAgICAgIC8vICAgdHlwZTogJ3RleHQnLAogICAgICAgICAgICAvLyAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgLy8gICAgIGZpZWxkOidDdXN0b21UZXh0JywKICAgICAgICAgICAgLy8gICAgIHRlc3REYXRhOiAn6Ieq5a6a5LmJ5paH5pysJywKICAgICAgICAgICAgLy8gICAgIGhlaWdodDogMTYsCiAgICAgICAgICAgIC8vICAgICBmb250U2l6ZTogMTUsCiAgICAgICAgICAgIC8vICAgICBoaWRlVGl0bGU6IHRydWUKICAgICAgICAgICAgLy8gICB9CiAgICAgICAgICAgIC8vIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICB0aWQ6ICJTaGlwbWVudC5jdXN0b21UZXh0IiwKICAgICAgICAgICAgICB0aXRsZTogIuiHquWumuS5ieaWh+acrCIsCiAgICAgICAgICAgICAgY3VzdG9tVGV4dDogIuiHquWumuS5ieaWh+acrCIsCiAgICAgICAgICAgICAgY3VzdG9tOiB0cnVlLAogICAgICAgICAgICAgIHR5cGU6ICJ0ZXh0IgogICAgICAgICAgICB9XSksIG5ldyBoaXByaW50LlByaW50RWxlbWVudFR5cGVHcm91cCgi6KGo5qC8L+WFtuS7liIsIFt7CiAgICAgICAgICAgICAgdGlkOiAnU2hpcG1lbnQuVGFibGUnLAogICAgICAgICAgICAgIHRpdGxlOiAn6K6i5Y2V5pWw5o2uJywKICAgICAgICAgICAgICB0eXBlOiAndGFibGUnLAogICAgICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgICAgIGZpZWxkOiAnVGFibGUnLAogICAgICAgICAgICAgICAgdGFibGVIZWFkZXJSZXBlYXQ6ICdmaXJzdCcsCiAgICAgICAgICAgICAgICB0YWJsZUZvb3RlclJlcGVhdDogJ2xhc3QnLAogICAgICAgICAgICAgICAgZmllbGRzOiBbewogICAgICAgICAgICAgICAgICB0ZXh0OiAn5bqP5Y+3JywKICAgICAgICAgICAgICAgICAgZmllbGQ6ICdSb3dObycKICAgICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgICAgdGV4dDogJ+aehOS7ti/ljIXlkI3np7AnLAogICAgICAgICAgICAgICAgICBmaWVsZDogJ1N0ZWVsTmFtZScKICAgICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgICAgdGV4dDogJ+aVsOmHjycsCiAgICAgICAgICAgICAgICAgIGZpZWxkOiAnU3RlZWxBbW91bnQnCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIHRleHQ6ICfop4TmoLwnLAogICAgICAgICAgICAgICAgICBmaWVsZDogJ1N0ZWVsU3BlYycKICAgICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgICAgdGV4dDogJ+mVv+W6picsCiAgICAgICAgICAgICAgICAgIGZpZWxkOiAnU3RlZWxMZW5ndGgnCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIHRleHQ6ICfljZXph43vvIhrZ++8iScsCiAgICAgICAgICAgICAgICAgIGZpZWxkOiAnU3RlZWxXZWlnaHQnCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIHRleHQ6ICfmgLvph43vvIhrZ++8iScsCiAgICAgICAgICAgICAgICAgIGZpZWxkOiAnU3RlZWxBbGxXZWlnaHQnCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIHRleHQ6ICfljZXmr5vph43vvIhrZ++8iScsCiAgICAgICAgICAgICAgICAgIGZpZWxkOiAnR3Jvc3NXZWlnaHQnCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIHRleHQ6ICfmgLvmr5vph43vvIhrZ++8iScsCiAgICAgICAgICAgICAgICAgIGZpZWxkOiAnR3Jvc3NBbGxXZWlnaHQnCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIHRleHQ6ICflpIfms6jvvIjmnoTku7bmuIXljZXlpIfms6jkv6Hmga/vvIknLAogICAgICAgICAgICAgICAgICBmaWVsZDogJ0NvbXBvbmVudERldGFpbHMnCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIHRleHQ6ICflpIfms6jvvIjopoHotKfljLrln5/vvIknLAogICAgICAgICAgICAgICAgICBmaWVsZDogJ0FyZWFQb3NpdGlvbicKICAgICAgICAgICAgICAgIH1dLAogICAgICAgICAgICAgICAgdGVzdERhdGE6IEpTT04uc3RyaW5naWZ5KFt7CiAgICAgICAgICAgICAgICAgIFN0ZWVsTmFtZTogJ+aehOS7tuWMhS0xJywKICAgICAgICAgICAgICAgICAgU3RlZWxBbW91bnQ6IDIsCiAgICAgICAgICAgICAgICAgIFN0ZWVsU3BlYzogJ1BLRzAwMDY4JywKICAgICAgICAgICAgICAgICAgU3RlZWxMZW5ndGg6ICc1MCcsCiAgICAgICAgICAgICAgICAgIEFyZWFQb3NpdGlvbjogJ+esrOS4gOaJueasoeWKoOW3peaehOS7ticsCiAgICAgICAgICAgICAgICAgIFN0ZWVsV2VpZ2h0OiAnMTAwMCcsCiAgICAgICAgICAgICAgICAgIFN0ZWVsQWxsV2VpZ2h0OiAnMjAwMCcsCiAgICAgICAgICAgICAgICAgIEdyb3NzV2VpZ2h0OiAnMTA1MCcsCiAgICAgICAgICAgICAgICAgIEdyb3NzQWxsV2VpZ2h0OiAnMjEwMCcsCiAgICAgICAgICAgICAgICAgIFJvd05vOiAxLAogICAgICAgICAgICAgICAgICBVbml0OiAna2cnLAogICAgICAgICAgICAgICAgICBJc1BhY2thZ2U6IHRydWUKICAgICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgICAgU3RlZWxOYW1lOiAnQUtQLTAwMScsCiAgICAgICAgICAgICAgICAgIFN0ZWVsQW1vdW50OiAyLAogICAgICAgICAgICAgICAgICBTdGVlbFNwZWM6ICc4OCo4OCcsCiAgICAgICAgICAgICAgICAgIFN0ZWVsTGVuZ3RoOiAnNTAnLAogICAgICAgICAgICAgICAgICBBcmVhUG9zaXRpb246ICfnrKzkuIDmibnmrKHliqDlt6XmnoTku7YnLAogICAgICAgICAgICAgICAgICBTdGVlbFdlaWdodDogJzEwMDAnLAogICAgICAgICAgICAgICAgICBTdGVlbEFsbFdlaWdodDogJzIwMDAnLAogICAgICAgICAgICAgICAgICBHcm9zc1dlaWdodDogJzEwNTAnLAogICAgICAgICAgICAgICAgICBHcm9zc0FsbFdlaWdodDogJzIxMDAnLAogICAgICAgICAgICAgICAgICBSb3dObzogMiwKICAgICAgICAgICAgICAgICAgVW5pdDogJ2tnJwogICAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgICBTdGVlbE5hbWU6ICdBS1AtMDAyJywKICAgICAgICAgICAgICAgICAgU3RlZWxBbW91bnQ6IDMsCiAgICAgICAgICAgICAgICAgIFN0ZWVsU3BlYzogJzg4Kjg4JywKICAgICAgICAgICAgICAgICAgU3RlZWxMZW5ndGg6ICc1MCcsCiAgICAgICAgICAgICAgICAgIEFyZWFQb3NpdGlvbjogJ+esrOS4gOaJueasoeWKoOW3peaehOS7ticsCiAgICAgICAgICAgICAgICAgIFN0ZWVsV2VpZ2h0OiAnMTAwMCcsCiAgICAgICAgICAgICAgICAgIFN0ZWVsQWxsV2VpZ2h0OiAnMzAwMCcsCiAgICAgICAgICAgICAgICAgIEdyb3NzV2VpZ2h0OiAnMTA1MCcsCiAgICAgICAgICAgICAgICAgIEdyb3NzQWxsV2VpZ2h0OiAzMTUwLAogICAgICAgICAgICAgICAgICBSb3dObzogMywKICAgICAgICAgICAgICAgICAgVW5pdDogJ2tnJwogICAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgICBTdGVlbE5hbWU6ICflkIjorqEnLAogICAgICAgICAgICAgICAgICBTdGVlbEFtb3VudDogNSwKICAgICAgICAgICAgICAgICAgU3RlZWxTcGVjOiAnJywKICAgICAgICAgICAgICAgICAgU3RlZWxMZW5ndGg6ICcnLAogICAgICAgICAgICAgICAgICBBcmVhUG9zaXRpb246ICcnLAogICAgICAgICAgICAgICAgICBTdGVlbFdlaWdodDogJycsCiAgICAgICAgICAgICAgICAgIFN0ZWVsQWxsV2VpZ2h0OiAnNTAwMCcsCiAgICAgICAgICAgICAgICAgIEdyb3NzV2VpZ2h0OiAnJywKICAgICAgICAgICAgICAgICAgR3Jvc3NBbGxXZWlnaHQ6IDczNTAsCiAgICAgICAgICAgICAgICAgIFJvd05vOiA0LAogICAgICAgICAgICAgICAgICBVbml0OiAnJwogICAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgICBTdGVlbE5hbWU6ICflpIfms6gnLAogICAgICAgICAgICAgICAgICBTdGVlbEFtb3VudDogNSwKICAgICAgICAgICAgICAgICAgU3RlZWxTcGVjOiAnJywKICAgICAgICAgICAgICAgICAgU3RlZWxMZW5ndGg6ICcnLAogICAgICAgICAgICAgICAgICBBcmVhUG9zaXRpb246ICcnLAogICAgICAgICAgICAgICAgICBTdGVlbFdlaWdodDogJycsCiAgICAgICAgICAgICAgICAgIFN0ZWVsQWxsV2VpZ2h0OiAnNTAwMCcsCiAgICAgICAgICAgICAgICAgIEdyb3NzV2VpZ2h0OiAnJywKICAgICAgICAgICAgICAgICAgR3Jvc3NBbGxXZWlnaHQ6IDczNTAsCiAgICAgICAgICAgICAgICAgIFJvd05vOiA0LAogICAgICAgICAgICAgICAgICBVbml0OiAnJywKICAgICAgICAgICAgICAgICAgSXNSZW1hcms6IHRydWUKICAgICAgICAgICAgICAgIH1dKQogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgZWRpdGFibGU6IHRydWUsCiAgICAgICAgICAgICAgY29sdW1uRGlzcGxheUVkaXRhYmxlOiB0cnVlLAogICAgICAgICAgICAgIC8v5YiX5pi+56S65piv5ZCm6IO957yW6L6RCiAgICAgICAgICAgICAgY29sdW1uRGlzcGxheUluZGV4RWRpdGFibGU6IHRydWUsCiAgICAgICAgICAgICAgLy/liJfpobrluo/mmL7npLrmmK/lkKbog73nvJbovpEKICAgICAgICAgICAgICBjb2x1bW5UaXRsZUVkaXRhYmxlOiB0cnVlLAogICAgICAgICAgICAgIC8v5YiX5qCH6aKY5piv5ZCm6IO957yW6L6RCiAgICAgICAgICAgICAgY29sdW1uUmVzaXphYmxlOiB0cnVlLAogICAgICAgICAgICAgIC8v5YiX5a695piv5ZCm6IO96LCD5pW0CiAgICAgICAgICAgICAgY29sdW1uQWxpZ25FZGl0YWJsZTogdHJ1ZSwKICAgICAgICAgICAgICAvL+WIl+Wvuem9kOaYr+WQpuiwg+aVtAogICAgICAgICAgICAgIGlzRW5hYmxlRWRpdEZpZWxkOiB0cnVlLAogICAgICAgICAgICAgIC8v57yW6L6R5a2X5q61CiAgICAgICAgICAgICAgaXNFbmFibGVDb250ZXh0TWVudTogdHJ1ZSwKICAgICAgICAgICAgICAvL+W8gOWQr+WPs+mUruiPnOWNlSDpu5jorqR0cnVlCiAgICAgICAgICAgICAgaXNFbmFibGVJbnNlcnRSb3c6IHRydWUsCiAgICAgICAgICAgICAgLy/mj5LlhaXooYwKICAgICAgICAgICAgICBpc0VuYWJsZURlbGV0ZVJvdzogdHJ1ZSwKICAgICAgICAgICAgICAvL+WIoOmZpOihjAogICAgICAgICAgICAgIGlzRW5hYmxlSW5zZXJ0Q29sdW1uOiB0cnVlLAogICAgICAgICAgICAgIC8v5o+S5YWl5YiXCiAgICAgICAgICAgICAgaXNFbmFibGVEZWxldGVDb2x1bW46IHRydWUsCiAgICAgICAgICAgICAgLy/liKDpmaTliJcKICAgICAgICAgICAgICBpc0VuYWJsZU1lcmdlQ2VsbDogdHJ1ZSwKICAgICAgICAgICAgICAvL+WQiOW5tuWNleWFg+agvAogICAgICAgICAgICAgIGNvbHVtbnM6IFtbewogICAgICAgICAgICAgICAgdGl0bGU6ICfluo/lj7cnLAogICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgZmllbGQ6ICdSb3dObycsCiAgICAgICAgICAgICAgICB3aWR0aDogODAsCiAgICAgICAgICAgICAgICBjaGVja2VkOiBmYWxzZSwKICAgICAgICAgICAgICAgIGRyYWc6IGZhbHNlLAogICAgICAgICAgICAgICAgaXNEcmFnOiBmYWxzZQogICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgIHRpdGxlOiAn5p6E5Lu2L+WMheWQjeensCcsCiAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICBmaWVsZDogJ1N0ZWVsTmFtZScsCiAgICAgICAgICAgICAgICB3aWR0aDogMTQwLAogICAgICAgICAgICAgICAgY2hlY2tlZDogdHJ1ZSwKICAgICAgICAgICAgICAgIGRyYWc6IGZhbHNlLAogICAgICAgICAgICAgICAgaXNEcmFnOiBmYWxzZQogICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgIHRpdGxlOiAn6KeE5qC8JywKICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgIGZpZWxkOiAnU3RlZWxTcGVjJywKICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAsCiAgICAgICAgICAgICAgICBjaGVja2VkOiB0cnVlLAogICAgICAgICAgICAgICAgaXNEcmFnOiBmYWxzZQogICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgIHRpdGxlOiAn6ZW/5bqmJywKICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgIGZpZWxkOiAnU3RlZWxMZW5ndGgnLAogICAgICAgICAgICAgICAgd2lkdGg6IDgwLAogICAgICAgICAgICAgICAgY2hlY2tlZDogdHJ1ZQogICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgIHRpdGxlOiAn5pWw6YePJywKICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgIGZpZWxkOiAnU3RlZWxBbW91bnQnLAogICAgICAgICAgICAgICAgd2lkdGg6IDgwLAogICAgICAgICAgICAgICAgY2hlY2tlZDogdHJ1ZQogICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgIHRpdGxlOiAn5Y2V6YeN77yIa2fvvIknLAogICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgZmllbGQ6ICdTdGVlbFdlaWdodCcsCiAgICAgICAgICAgICAgICB3aWR0aDogMTEwLAogICAgICAgICAgICAgICAgY2hlY2tlZDogdHJ1ZQogICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgIHRpdGxlOiAn5oC76YeN77yIa2fvvIknLAogICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgZmllbGQ6ICdTdGVlbEFsbFdlaWdodCcsCiAgICAgICAgICAgICAgICB3aWR0aDogMTEwLAogICAgICAgICAgICAgICAgY2hlY2tlZDogdHJ1ZQogICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgIHRpdGxlOiAn5Y2V5q+b6YeN77yIa2fvvIknLAogICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgZmllbGQ6ICdHcm9zc1dlaWdodCcsCiAgICAgICAgICAgICAgICB3aWR0aDogMTIwLAogICAgICAgICAgICAgICAgY2hlY2tlZDogZmFsc2UKICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICB0aXRsZTogJ+aAu+avm+mHje+8iGtn77yJJywKICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgIGZpZWxkOiAnR3Jvc3NBbGxXZWlnaHQnLAogICAgICAgICAgICAgICAgd2lkdGg6IDEyMCwKICAgICAgICAgICAgICAgIGNoZWNrZWQ6IGZhbHNlCiAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgdGl0bGU6ICflpIfms6jvvIjmnoTku7bmuIXljZXlpIfms6jkv6Hmga/vvIknLAogICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgZmllbGQ6ICdDb21wb25lbnREZXRhaWxzJywKICAgICAgICAgICAgICAgIHdpZHRoOiAyMjAsCiAgICAgICAgICAgICAgICBjaGVja2VkOiB0cnVlCiAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgdGl0bGU6ICflpIfms6jvvIjopoHotKfljLrln5/vvIknLAogICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgZmllbGQ6ICdBcmVhUG9zaXRpb24nLAogICAgICAgICAgICAgICAgd2lkdGg6IDE2MCwKICAgICAgICAgICAgICAgIGNoZWNrZWQ6IHRydWUKICAgICAgICAgICAgICB9XV0sCiAgICAgICAgICAgICAgLy8gQGluZGV4IOWIl2luZGV477ybQGNvbCDliJfphY3nva7vvJtAZGF0YSDliJfmlbDmja4KICAgICAgICAgICAgICByb3dzQ29sdW1uc01lcmdlOiBmdW5jdGlvbiByb3dzQ29sdW1uc01lcmdlKGRhdGEsIGNvbCwgaW5kZXgpIHsKICAgICAgICAgICAgICAgIC8vIOi/lOWbnuS4gOS4quaVsOe7hCzlj4LmlbDkuIDkuLrooYzvvIhyb3dzcGFu77yJ5ZCI5bm25pWwLOWPguaVsOS6jOS4uuWIl++8iGNvbHNwYW7vvInlkIjlubbmlbAsIOiiq+WQiOW5tueahOihjOaIluiAheWIl+WAvOiuvuS4ujAKICAgICAgICAgICAgICAgIC8vIOWkhOeQhuWMheWQjeihjAogICAgICAgICAgICAgICAgaWYgKGNvbC5maWVsZCA9PT0gJ1N0ZWVsU3BlYycgJiYgZGF0YS5Jc1BhY2thZ2UpIHsKICAgICAgICAgICAgICAgICAgcmV0dXJuIFsxLCAxMDBdOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKGNvbC5maWVsZCAhPT0gJ1Jvd05vJyAmJiBjb2wuZmllbGQgIT09ICdTdGVlbE5hbWUnICYmIGRhdGEuSXNQYWNrYWdlKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiBbMSwgMF07CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAvLyDlpITnkIblpIfms6jooYwKICAgICAgICAgICAgICAgIGlmIChpbmRleCA9PT0gMSAmJiBkYXRhLklzUmVtYXJrKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiBbMSwgMTAwXTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGlmIChpbmRleCAhPT0gMCAmJiBkYXRhLklzUmVtYXJrKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiBbMSwgMF07CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9XSksIG5ldyBoaXByaW50LlByaW50RWxlbWVudFR5cGVHcm91cCgi6L6F5YqpIiwgW3sKICAgICAgICAgICAgICB0aWQ6ICdTaGlwbWVudC5obGluZScsCiAgICAgICAgICAgICAgdGl0bGU6ICfmqKrnur8nLAogICAgICAgICAgICAgIHR5cGU6ICdobGluZScsCiAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgZmllbGQ6ICdobGluZScKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICBmaWVsZDogJ3ZsaW5lJywKICAgICAgICAgICAgICB0aWQ6ICdTaGlwbWVudC52bGluZScsCiAgICAgICAgICAgICAgdGl0bGU6ICfnq5bnur8nLAogICAgICAgICAgICAgIHR5cGU6ICd2bGluZScsCiAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgZmllbGQ6ICd2bGluZScKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICBmaWVsZDogJ3JlY3QnLAogICAgICAgICAgICAgIHRpZDogJ1NoaXBtZW50LnJlY3QnLAogICAgICAgICAgICAgIHRpdGxlOiAn55+p5b2iJywKICAgICAgICAgICAgICB0eXBlOiAncmVjdCcsCiAgICAgICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICAgICAgZmllbGQ6ICdyZWN0JwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGZpZWxkOiAnb3ZhbCcsCiAgICAgICAgICAgICAgdGlkOiAnU2hpcG1lbnQub3ZhbCcsCiAgICAgICAgICAgICAgdGl0bGU6ICfmpK3lnIYnLAogICAgICAgICAgICAgIHR5cGU6ICdvdmFsJywKICAgICAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgICAgICBmaWVsZDogJ292YWwnCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9XSldKTsKICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgfQogICAgICB9LCBfY2FsbGVlKTsKICAgIH0pKTsKICAgIHJldHVybiBmdW5jdGlvbiBhZGRFbGVtZW50VHlwZXMoX3gpIHsKICAgICAgcmV0dXJuIF9yZWYuYXBwbHkodGhpcywgYXJndW1lbnRzKTsKICAgIH07CiAgfSgpOwogIHJldHVybiB7CiAgICBhZGRFbGVtZW50VHlwZXM6IGFkZEVsZW1lbnRUeXBlcwogIH07Cn07CmV4cG9ydCBkZWZhdWx0IFt7CiAgbmFtZTogJ+WPkei0p+WNlScsCiAgdmFsdWU6IDEsCiAgZjogRGVsaXZlcnlOb3RlUHJvdmlkZXIoKQp9XTs="}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "GetPreferenceSettingValue", "GetCompany", "DeliveryNoteProvider", "ops", "addElementTypes", "_ref", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "context", "res", "logo", "wrap", "_callee$", "_context", "prev", "next", "sent", "Data", "Icon", "removePrintElementTypes", "addPrintElementTypes", "PrintElementTypeGroup", "tid", "title", "data", "type", "options", "field", "height", "width", "src", "testData", "fontSize", "lineHeight", "textType", "customText", "custom", "tableHeaderRepeat", "tableFooterRepeat", "fields", "text", "JSON", "stringify", "SteelName", "SteelAmount", "SteelSpec", "Steel<PERSON><PERSON><PERSON>", "AreaPosition", "SteelWeight", "SteelAllWeight", "GrossWeight", "GrossAllWeight", "RowNo", "Unit", "IsPackage", "IsRemark", "editable", "columnDisplayEditable", "columnDisplayIndexEditable", "columnTitleEditable", "columnResizable", "columnAlignEditable", "isEnableEditField", "isEnableContextMenu", "isEnableInsertRow", "isEnableDeleteRow", "isEnableInsertColumn", "isEnableDeleteColumn", "isEnableMergeCell", "columns", "align", "checked", "drag", "isDrag", "rowsColumnsMerge", "col", "index", "stop", "_x", "apply", "arguments", "name", "value", "f"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/views/PRO/shipment/template-print/providers.js"], "sourcesContent": ["/* eslint-disable */\r\nimport { hiprint} from \"vue-plugin-hiprint\";\r\nimport {GetPreferenceSettingValue} from \"@/api/sys/system-setting\";\r\nimport {GetCompany} from \"@/api/plm/site\";\r\n\r\n// 自定义设计元素1\r\nexport const DeliveryNoteProvider = function (ops) {\r\n  var addElementTypes = async function (context) {\r\n    const res = await GetCompany()\r\n    let logo = res.Data.Icon\r\n    context.removePrintElementTypes(\"Shipment\");\r\n    context.addPrintElementTypes(\r\n      1,\r\n      [\r\n        new hiprint.PrintElementTypeGroup(\"平台\", [\r\n          {\r\n            tid: 'Shipment.Logo', title: 'Logo图片', data: '', type: 'image',\r\n            options:{\r\n              field: 'Logo',\r\n              height: 40,\r\n              width:40,\r\n              src:logo\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.QrcodeText', title: '二维码', data: 'XS888888888', type: 'text',\r\n            options: {\r\n              field: 'QrcodeText',\r\n              testData: 'XS888888888',\r\n              height: 64,\r\n              fontSize: 12,\r\n              lineHeight: 18,\r\n              textType: \"qrcode\"\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.ContractNumber', title: '内部合同编号', data: 'HT-0001', type: 'text',\r\n            options: {\r\n              testData: 'HT-0001',\r\n              field: \"ContractNumber\",\r\n              fontSize: 12,\r\n              width: 200\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.Code', title: '单据号', data: 'BIM-CK-00249', type: 'text',\r\n            options: {\r\n              testData: 'BIM-CK-00249',\r\n              field: \"Code\",\r\n              fontSize: 12,\r\n              width: 200\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.SendDate', title: '日期', data: '2024-04-03', type: 'text',\r\n            options: {\r\n              testData: '2024-04-03',\r\n              fontSize: 12,\r\n              field: \"SendDate\",\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.Number', title: '发货序号', data: '1', type: 'text',\r\n            options: {\r\n              field: 'Number',\r\n              testData: '1',\r\n              fontSize: 12,\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.Address', title: '项目地址', data: '浙江省绍兴市柯桥区鉴湖路1587号', type: 'text',\r\n            options: {\r\n              field: 'Address',\r\n              testData: '浙江省绍兴市柯桥区鉴湖路1587号',\r\n              fontSize: 12,\r\n              width: 300\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.ProjectName', title: '项目名称', data: '比姆泰客测试项目', type: 'text',\r\n            options: {\r\n              field: 'ProjectName',\r\n              testData: '比姆泰客测试项目',\r\n              fontSize: 12,\r\n              width: 250\r\n            }\r\n          },\r\n\r\n          {\r\n            tid: 'Shipment.MakerName', title: '出库人', data: 'Lily', type: 'text',\r\n            options: {\r\n              field: 'MakerName',\r\n              testData: 'Lily',\r\n              fontSize: 12,\r\n            }\r\n          },\r\n\r\n          {\r\n            tid: 'Shipment.Consignee', title: '收货人', data: 'Lily', type: 'text',\r\n            options: {\r\n              field: 'Consignee',\r\n              testData: 'Lily',\r\n              fontSize: 12,\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.ConsigneeTel', title: '联系电话', data: '185****4235', type: 'text',\r\n            options: {\r\n              field: 'ConsigneeTel',\r\n              testData: '185****4235',\r\n              fontSize: 12,\r\n              width: 150\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.VehicleNo', title: '车牌', data: '浙D388432', type: 'text',\r\n            options: {\r\n              field: 'VehicleNo',\r\n              testData: '浙D388432',\r\n              fontSize: 12,\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.Telephone', title: '司机电话', data: '185****4235', type: 'text',\r\n            options: {\r\n              field: 'Telephone',\r\n              testData: '185****4235',\r\n              fontSize: 12,\r\n              width: 150\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.LoadingsName', title: '装车班', data: '仓储部门', type: 'text',\r\n            options: {\r\n              field: 'LoadingsName',\r\n              testData: '仓储部门',\r\n              fontSize: 12,\r\n              width: 150\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.LoadingsPersonnelName', title: '装车班人员', data: '张三', type: 'text',\r\n            options: {\r\n              field: 'LoadingsPersonnelName',\r\n              testData: '张三',\r\n              fontSize: 12,\r\n              width: 150\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.ReceivingUnit', title: '收货单位', data: 'XX单位', type: 'text',\r\n            options: {\r\n              field: 'ReceivingUnit',\r\n              testData: 'XX单位',\r\n              fontSize: 12,\r\n              width: 150\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.Trips', title: '车次', data: '第一车', type: 'text',\r\n            options: {\r\n              field: 'Trips',\r\n              testData: '第一车',\r\n              fontSize: 12,\r\n              width: 150\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.Pound_Weight', title: '磅重（kg）', data: '5369.64', type: 'text',\r\n            options: {\r\n              field: 'Pound_Weight',\r\n              testData: '5369.64',\r\n              fontSize: 12,\r\n              width: 150\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.Tare_Weight', title: '皮重（kg）', data: '5369.64', type: 'text',\r\n            options: {\r\n              field: 'Tare_Weight',\r\n              testData: '5369.64',\r\n              fontSize: 12,\r\n              width: 150\r\n            }\r\n          },\r\n          {\r\n            tid: 'Shipment.Net_Weight', title: '净重（kg）', data: '5369.64', type: 'text',\r\n            options: {\r\n              field: 'Net_Weight',\r\n              testData: '5369.64',\r\n              fontSize: 12,\r\n              width: 150\r\n            }\r\n          },\r\n          // {\r\n          //   tid: 'Shipment.CustomText',\r\n          //   title: '',\r\n          //   customText: '自定义文本',\r\n          //   custom: true,\r\n          //   type: 'text',\r\n          //   options: {\r\n          //     field:'CustomText',\r\n          //     testData: '自定义文本',\r\n          //     height: 16,\r\n          //     fontSize: 15,\r\n          //     hideTitle: true\r\n          //   }\r\n          // },\r\n          {\r\n            tid: \"Shipment.customText\",\r\n            title: \"自定义文本\",\r\n            customText: \"自定义文本\",\r\n            custom: true,\r\n            type: \"text\"\r\n          }\r\n        ]),\r\n        new hiprint.PrintElementTypeGroup(\"表格/其他\", [\r\n          {\r\n            tid: 'Shipment.Table', title: '订单数据',\r\n            type: 'table',\r\n            options: {\r\n              field: 'Table',\r\n              tableHeaderRepeat: 'first',\r\n              tableFooterRepeat: 'last',\r\n              fields: [\r\n                {text: '序号', field: 'RowNo'},\r\n                {text: '构件/包名称', field: 'SteelName'},\r\n                {text: '数量', field: 'SteelAmount'},\r\n                {text: '规格', field: 'SteelSpec'},\r\n                {text: '长度', field: 'SteelLength'},\r\n                {text: '单重（kg）', field: 'SteelWeight'},\r\n                {text: '总重（kg）', field: 'SteelAllWeight'},\r\n                {text: '单毛重（kg）', field: 'GrossWeight'},\r\n                {text: '总毛重（kg）', field: 'GrossAllWeight'},\r\n                {text: '备注（构件清单备注信息）', field: 'ComponentDetails'},\r\n                {text: '备注（要货区域）', field: 'AreaPosition'},\r\n              ],\r\n              testData:JSON.stringify([\r\n                {\r\n                  SteelName:'构件包-1',\r\n                  SteelAmount:2,\r\n                  SteelSpec:'PKG00068',\r\n                  SteelLength:'50',\r\n                  AreaPosition:'第一批次加工构件',\r\n                  SteelWeight:'1000',\r\n                  SteelAllWeight:'2000',\r\n                  GrossWeight:'1050',\r\n                  GrossAllWeight:'2100',\r\n                  RowNo:1,\r\n                  Unit:'kg',\r\n                  IsPackage:true\r\n                },\r\n                {\r\n                  SteelName:'AKP-001',\r\n                  SteelAmount:2,\r\n                  SteelSpec:'88*88',\r\n                  SteelLength:'50',\r\n                  AreaPosition:'第一批次加工构件',\r\n                  SteelWeight:'1000',\r\n                  SteelAllWeight:'2000',\r\n                  GrossWeight:'1050',\r\n                  GrossAllWeight:'2100',\r\n                  RowNo:2,\r\n                  Unit:'kg'\r\n                },\r\n                {\r\n                  SteelName:'AKP-002',\r\n                  SteelAmount:3,\r\n                  SteelSpec:'88*88',\r\n                  SteelLength:'50',\r\n                  AreaPosition:'第一批次加工构件',\r\n                  SteelWeight:'1000',\r\n                  SteelAllWeight:'3000',\r\n                  GrossWeight:'1050',\r\n                  GrossAllWeight:3150,\r\n                  RowNo:3,\r\n                  Unit:'kg'\r\n                },\r\n                {\r\n                  SteelName:'合计',\r\n                  SteelAmount:5,\r\n                  SteelSpec:'',\r\n                  SteelLength:'',\r\n                  AreaPosition:'',\r\n                  SteelWeight:'',\r\n                  SteelAllWeight:'5000',\r\n                  GrossWeight:'',\r\n                  GrossAllWeight:7350,\r\n                  RowNo:4,\r\n                  Unit:''\r\n                },\r\n                {\r\n                  SteelName:'备注',\r\n                  SteelAmount:5,\r\n                  SteelSpec:'',\r\n                  SteelLength:'',\r\n                  AreaPosition:'',\r\n                  SteelWeight:'',\r\n                  SteelAllWeight:'5000',\r\n                  GrossWeight:'',\r\n                  GrossAllWeight:7350,\r\n                  RowNo:4,\r\n                  Unit:'',\r\n                  IsRemark:true\r\n                }\r\n              ])\r\n            },\r\n            editable: true,\r\n            columnDisplayEditable: true,//列显示是否能编辑\r\n            columnDisplayIndexEditable: true,//列顺序显示是否能编辑\r\n            columnTitleEditable: true,//列标题是否能编辑\r\n            columnResizable: true, //列宽是否能调整\r\n            columnAlignEditable: true,//列对齐是否调整\r\n            isEnableEditField: true, //编辑字段\r\n            isEnableContextMenu: true, //开启右键菜单 默认true\r\n            isEnableInsertRow: true, //插入行\r\n            isEnableDeleteRow: true, //删除行\r\n            isEnableInsertColumn: true, //插入列\r\n            isEnableDeleteColumn: true, //删除列\r\n            isEnableMergeCell: true, //合并单元格\r\n            columns: [\r\n              [\r\n                {title: '序号', align: 'center', field: 'RowNo', width: 80, checked: false, drag:false, isDrag:false},\r\n                {title: '构件/包名称', align: 'center', field: 'SteelName', width: 140, checked: true, drag:false, isDrag:false},\r\n                {title: '规格', align: 'center', field: 'SteelSpec', width: 100, checked: true, isDrag:false},\r\n                {title: '长度', align: 'center', field: 'SteelLength', width: 80, checked: true},\r\n                {title: '数量', align: 'center', field: 'SteelAmount', width: 80, checked: true},\r\n                {title: '单重（kg）', align: 'center', field: 'SteelWeight', width: 110, checked: true},\r\n                {title: '总重（kg）', align: 'center', field: 'SteelAllWeight', width: 110, checked: true},\r\n                {title: '单毛重（kg）', align: 'center', field: 'GrossWeight', width: 120, checked: false},\r\n                {title: '总毛重（kg）', align: 'center', field: 'GrossAllWeight', width: 120, checked: false},\r\n                {title: '备注（构件清单备注信息）', align: 'center', field: 'ComponentDetails', width: 220, checked: true},\r\n                {title: '备注（要货区域）', align: 'center', field: 'AreaPosition', width: 160, checked: true},\r\n              ],\r\n            ],\r\n            // @index 列index；@col 列配置；@data 列数据\r\n            rowsColumnsMerge: function (data, col, index) {\r\n              // 返回一个数组,参数一为行（rowspan）合并数,参数二为列（colspan）合并数, 被合并的行或者列值设为0\r\n              // 处理包名行\r\n              if(col.field === 'SteelSpec' && data.IsPackage){\r\n                return [1,100]\r\n              }\r\n              if((col.field !== 'RowNo' && col.field !== 'SteelName' ) && data.IsPackage){\r\n                return [1,0]\r\n              }\r\n              // 处理备注行\r\n              if(index===1 && data.IsRemark){\r\n                return [1,100]\r\n              }\r\n              if(index!==0 && data.IsRemark){\r\n                return [1,0]\r\n              }\r\n            },\r\n\r\n          },\r\n        ]),\r\n        new hiprint.PrintElementTypeGroup(\"辅助\", [\r\n          {\r\n            tid: 'Shipment.hline',\r\n            title: '横线',\r\n            type: 'hline',\r\n            options:{\r\n              field:'hline',\r\n            }\r\n          },\r\n          {\r\n            field: 'vline',\r\n            tid: 'Shipment.vline',\r\n            title: '竖线',\r\n            type: 'vline',\r\n            options:{\r\n              field:'vline',\r\n            }\r\n          },\r\n          {\r\n            field: 'rect',\r\n            tid: 'Shipment.rect',\r\n            title: '矩形',\r\n            type: 'rect',\r\n            options:{\r\n              field:'rect',\r\n            }\r\n          },\r\n          {\r\n            field: 'oval',\r\n            tid: 'Shipment.oval',\r\n            title: '椭圆',\r\n            type: 'oval',\r\n            options:{\r\n              field:'oval',\r\n            }\r\n          },\r\n        ])\r\n      ]\r\n    );\r\n  };\r\n  return {\r\n    addElementTypes: addElementTypes\r\n  };\r\n};\r\n\r\nexport default [{\r\n  name: '发货单',\r\n  value: 1,\r\n  f: DeliveryNoteProvider()\r\n}]\r\n"], "mappings": ";;;AAAA;AACA,SAASA,OAAO,QAAO,oBAAoB;AAC3C,SAAQC,yBAAyB,QAAO,0BAA0B;AAClE,SAAQC,UAAU,QAAO,gBAAgB;;AAEzC;AACA,OAAO,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAaC,GAAG,EAAE;EACjD,IAAIC,eAAe;IAAA,IAAAC,IAAA,GAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAAG,SAAAC,QAAgBC,OAAO;MAAA,IAAAC,GAAA,EAAAC,IAAA;MAAA,OAAAL,mBAAA,GAAAM,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACzBhB,UAAU,CAAC,CAAC;UAAA;YAAxBU,GAAG,GAAAI,QAAA,CAAAG,IAAA;YACLN,IAAI,GAAGD,GAAG,CAACQ,IAAI,CAACC,IAAI;YACxBV,OAAO,CAACW,uBAAuB,CAAC,UAAU,CAAC;YAC3CX,OAAO,CAACY,oBAAoB,CAC1B,CAAC,EACD,CACE,IAAIvB,OAAO,CAACwB,qBAAqB,CAAC,IAAI,EAAE,CACtC;cACEC,GAAG,EAAE,eAAe;cAAEC,KAAK,EAAE,QAAQ;cAAEC,IAAI,EAAE,EAAE;cAAEC,IAAI,EAAE,OAAO;cAC9DC,OAAO,EAAC;gBACNC,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,EAAE;gBACVC,KAAK,EAAC,EAAE;gBACRC,GAAG,EAACpB;cACN;YACF,CAAC,EACD;cACEY,GAAG,EAAE,qBAAqB;cAAEC,KAAK,EAAE,KAAK;cAAEC,IAAI,EAAE,aAAa;cAAEC,IAAI,EAAE,MAAM;cAC3EC,OAAO,EAAE;gBACPC,KAAK,EAAE,YAAY;gBACnBI,QAAQ,EAAE,aAAa;gBACvBH,MAAM,EAAE,EAAE;gBACVI,QAAQ,EAAE,EAAE;gBACZC,UAAU,EAAE,EAAE;gBACdC,QAAQ,EAAE;cACZ;YACF,CAAC,EACD;cACEZ,GAAG,EAAE,yBAAyB;cAAEC,KAAK,EAAE,QAAQ;cAAEC,IAAI,EAAE,SAAS;cAAEC,IAAI,EAAE,MAAM;cAC9EC,OAAO,EAAE;gBACPK,QAAQ,EAAE,SAAS;gBACnBJ,KAAK,EAAE,gBAAgB;gBACvBK,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,eAAe;cAAEC,KAAK,EAAE,KAAK;cAAEC,IAAI,EAAE,cAAc;cAAEC,IAAI,EAAE,MAAM;cACtEC,OAAO,EAAE;gBACPK,QAAQ,EAAE,cAAc;gBACxBJ,KAAK,EAAE,MAAM;gBACbK,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,mBAAmB;cAAEC,KAAK,EAAE,IAAI;cAAEC,IAAI,EAAE,YAAY;cAAEC,IAAI,EAAE,MAAM;cACvEC,OAAO,EAAE;gBACPK,QAAQ,EAAE,YAAY;gBACtBC,QAAQ,EAAE,EAAE;gBACZL,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEL,GAAG,EAAE,iBAAiB;cAAEC,KAAK,EAAE,MAAM;cAAEC,IAAI,EAAE,GAAG;cAAEC,IAAI,EAAE,MAAM;cAC9DC,OAAO,EAAE;gBACPC,KAAK,EAAE,QAAQ;gBACfI,QAAQ,EAAE,GAAG;gBACbC,QAAQ,EAAE;cACZ;YACF,CAAC,EACD;cACEV,GAAG,EAAE,kBAAkB;cAAEC,KAAK,EAAE,MAAM;cAAEC,IAAI,EAAE,mBAAmB;cAAEC,IAAI,EAAE,MAAM;cAC/EC,OAAO,EAAE;gBACPC,KAAK,EAAE,SAAS;gBAChBI,QAAQ,EAAE,mBAAmB;gBAC7BC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,sBAAsB;cAAEC,KAAK,EAAE,MAAM;cAAEC,IAAI,EAAE,UAAU;cAAEC,IAAI,EAAE,MAAM;cAC1EC,OAAO,EAAE;gBACPC,KAAK,EAAE,aAAa;gBACpBI,QAAQ,EAAE,UAAU;gBACpBC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EAED;cACEP,GAAG,EAAE,oBAAoB;cAAEC,KAAK,EAAE,KAAK;cAAEC,IAAI,EAAE,MAAM;cAAEC,IAAI,EAAE,MAAM;cACnEC,OAAO,EAAE;gBACPC,KAAK,EAAE,WAAW;gBAClBI,QAAQ,EAAE,MAAM;gBAChBC,QAAQ,EAAE;cACZ;YACF,CAAC,EAED;cACEV,GAAG,EAAE,oBAAoB;cAAEC,KAAK,EAAE,KAAK;cAAEC,IAAI,EAAE,MAAM;cAAEC,IAAI,EAAE,MAAM;cACnEC,OAAO,EAAE;gBACPC,KAAK,EAAE,WAAW;gBAClBI,QAAQ,EAAE,MAAM;gBAChBC,QAAQ,EAAE;cACZ;YACF,CAAC,EACD;cACEV,GAAG,EAAE,uBAAuB;cAAEC,KAAK,EAAE,MAAM;cAAEC,IAAI,EAAE,aAAa;cAAEC,IAAI,EAAE,MAAM;cAC9EC,OAAO,EAAE;gBACPC,KAAK,EAAE,cAAc;gBACrBI,QAAQ,EAAE,aAAa;gBACvBC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,oBAAoB;cAAEC,KAAK,EAAE,IAAI;cAAEC,IAAI,EAAE,UAAU;cAAEC,IAAI,EAAE,MAAM;cACtEC,OAAO,EAAE;gBACPC,KAAK,EAAE,WAAW;gBAClBI,QAAQ,EAAE,UAAU;gBACpBC,QAAQ,EAAE;cACZ;YACF,CAAC,EACD;cACEV,GAAG,EAAE,oBAAoB;cAAEC,KAAK,EAAE,MAAM;cAAEC,IAAI,EAAE,aAAa;cAAEC,IAAI,EAAE,MAAM;cAC3EC,OAAO,EAAE;gBACPC,KAAK,EAAE,WAAW;gBAClBI,QAAQ,EAAE,aAAa;gBACvBC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,uBAAuB;cAAEC,KAAK,EAAE,KAAK;cAAEC,IAAI,EAAE,MAAM;cAAEC,IAAI,EAAE,MAAM;cACtEC,OAAO,EAAE;gBACPC,KAAK,EAAE,cAAc;gBACrBI,QAAQ,EAAE,MAAM;gBAChBC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,gCAAgC;cAAEC,KAAK,EAAE,OAAO;cAAEC,IAAI,EAAE,IAAI;cAAEC,IAAI,EAAE,MAAM;cAC/EC,OAAO,EAAE;gBACPC,KAAK,EAAE,uBAAuB;gBAC9BI,QAAQ,EAAE,IAAI;gBACdC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,wBAAwB;cAAEC,KAAK,EAAE,MAAM;cAAEC,IAAI,EAAE,MAAM;cAAEC,IAAI,EAAE,MAAM;cACxEC,OAAO,EAAE;gBACPC,KAAK,EAAE,eAAe;gBACtBI,QAAQ,EAAE,MAAM;gBAChBC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,gBAAgB;cAAEC,KAAK,EAAE,IAAI;cAAEC,IAAI,EAAE,KAAK;cAAEC,IAAI,EAAE,MAAM;cAC7DC,OAAO,EAAE;gBACPC,KAAK,EAAE,OAAO;gBACdI,QAAQ,EAAE,KAAK;gBACfC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,uBAAuB;cAAEC,KAAK,EAAE,QAAQ;cAAEC,IAAI,EAAE,SAAS;cAAEC,IAAI,EAAE,MAAM;cAC5EC,OAAO,EAAE;gBACPC,KAAK,EAAE,cAAc;gBACrBI,QAAQ,EAAE,SAAS;gBACnBC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,sBAAsB;cAAEC,KAAK,EAAE,QAAQ;cAAEC,IAAI,EAAE,SAAS;cAAEC,IAAI,EAAE,MAAM;cAC3EC,OAAO,EAAE;gBACPC,KAAK,EAAE,aAAa;gBACpBI,QAAQ,EAAE,SAAS;gBACnBC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC,EACD;cACEP,GAAG,EAAE,qBAAqB;cAAEC,KAAK,EAAE,QAAQ;cAAEC,IAAI,EAAE,SAAS;cAAEC,IAAI,EAAE,MAAM;cAC1EC,OAAO,EAAE;gBACPC,KAAK,EAAE,YAAY;gBACnBI,QAAQ,EAAE,SAAS;gBACnBC,QAAQ,EAAE,EAAE;gBACZH,KAAK,EAAE;cACT;YACF,CAAC;YACD;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;cACEP,GAAG,EAAE,qBAAqB;cAC1BC,KAAK,EAAE,OAAO;cACdY,UAAU,EAAE,OAAO;cACnBC,MAAM,EAAE,IAAI;cACZX,IAAI,EAAE;YACR,CAAC,CACF,CAAC,EACF,IAAI5B,OAAO,CAACwB,qBAAqB,CAAC,OAAO,EAAE,CACzC;cACEC,GAAG,EAAE,gBAAgB;cAAEC,KAAK,EAAE,MAAM;cACpCE,IAAI,EAAE,OAAO;cACbC,OAAO,EAAE;gBACPC,KAAK,EAAE,OAAO;gBACdU,iBAAiB,EAAE,OAAO;gBAC1BC,iBAAiB,EAAE,MAAM;gBACzBC,MAAM,EAAE,CACN;kBAACC,IAAI,EAAE,IAAI;kBAAEb,KAAK,EAAE;gBAAO,CAAC,EAC5B;kBAACa,IAAI,EAAE,QAAQ;kBAAEb,KAAK,EAAE;gBAAW,CAAC,EACpC;kBAACa,IAAI,EAAE,IAAI;kBAAEb,KAAK,EAAE;gBAAa,CAAC,EAClC;kBAACa,IAAI,EAAE,IAAI;kBAAEb,KAAK,EAAE;gBAAW,CAAC,EAChC;kBAACa,IAAI,EAAE,IAAI;kBAAEb,KAAK,EAAE;gBAAa,CAAC,EAClC;kBAACa,IAAI,EAAE,QAAQ;kBAAEb,KAAK,EAAE;gBAAa,CAAC,EACtC;kBAACa,IAAI,EAAE,QAAQ;kBAAEb,KAAK,EAAE;gBAAgB,CAAC,EACzC;kBAACa,IAAI,EAAE,SAAS;kBAAEb,KAAK,EAAE;gBAAa,CAAC,EACvC;kBAACa,IAAI,EAAE,SAAS;kBAAEb,KAAK,EAAE;gBAAgB,CAAC,EAC1C;kBAACa,IAAI,EAAE,cAAc;kBAAEb,KAAK,EAAE;gBAAkB,CAAC,EACjD;kBAACa,IAAI,EAAE,UAAU;kBAAEb,KAAK,EAAE;gBAAc,CAAC,CAC1C;gBACDI,QAAQ,EAACU,IAAI,CAACC,SAAS,CAAC,CACtB;kBACEC,SAAS,EAAC,OAAO;kBACjBC,WAAW,EAAC,CAAC;kBACbC,SAAS,EAAC,UAAU;kBACpBC,WAAW,EAAC,IAAI;kBAChBC,YAAY,EAAC,UAAU;kBACvBC,WAAW,EAAC,MAAM;kBAClBC,cAAc,EAAC,MAAM;kBACrBC,WAAW,EAAC,MAAM;kBAClBC,cAAc,EAAC,MAAM;kBACrBC,KAAK,EAAC,CAAC;kBACPC,IAAI,EAAC,IAAI;kBACTC,SAAS,EAAC;gBACZ,CAAC,EACD;kBACEX,SAAS,EAAC,SAAS;kBACnBC,WAAW,EAAC,CAAC;kBACbC,SAAS,EAAC,OAAO;kBACjBC,WAAW,EAAC,IAAI;kBAChBC,YAAY,EAAC,UAAU;kBACvBC,WAAW,EAAC,MAAM;kBAClBC,cAAc,EAAC,MAAM;kBACrBC,WAAW,EAAC,MAAM;kBAClBC,cAAc,EAAC,MAAM;kBACrBC,KAAK,EAAC,CAAC;kBACPC,IAAI,EAAC;gBACP,CAAC,EACD;kBACEV,SAAS,EAAC,SAAS;kBACnBC,WAAW,EAAC,CAAC;kBACbC,SAAS,EAAC,OAAO;kBACjBC,WAAW,EAAC,IAAI;kBAChBC,YAAY,EAAC,UAAU;kBACvBC,WAAW,EAAC,MAAM;kBAClBC,cAAc,EAAC,MAAM;kBACrBC,WAAW,EAAC,MAAM;kBAClBC,cAAc,EAAC,IAAI;kBACnBC,KAAK,EAAC,CAAC;kBACPC,IAAI,EAAC;gBACP,CAAC,EACD;kBACEV,SAAS,EAAC,IAAI;kBACdC,WAAW,EAAC,CAAC;kBACbC,SAAS,EAAC,EAAE;kBACZC,WAAW,EAAC,EAAE;kBACdC,YAAY,EAAC,EAAE;kBACfC,WAAW,EAAC,EAAE;kBACdC,cAAc,EAAC,MAAM;kBACrBC,WAAW,EAAC,EAAE;kBACdC,cAAc,EAAC,IAAI;kBACnBC,KAAK,EAAC,CAAC;kBACPC,IAAI,EAAC;gBACP,CAAC,EACD;kBACEV,SAAS,EAAC,IAAI;kBACdC,WAAW,EAAC,CAAC;kBACbC,SAAS,EAAC,EAAE;kBACZC,WAAW,EAAC,EAAE;kBACdC,YAAY,EAAC,EAAE;kBACfC,WAAW,EAAC,EAAE;kBACdC,cAAc,EAAC,MAAM;kBACrBC,WAAW,EAAC,EAAE;kBACdC,cAAc,EAAC,IAAI;kBACnBC,KAAK,EAAC,CAAC;kBACPC,IAAI,EAAC,EAAE;kBACPE,QAAQ,EAAC;gBACX,CAAC,CACF;cACH,CAAC;cACDC,QAAQ,EAAE,IAAI;cACdC,qBAAqB,EAAE,IAAI;cAAC;cAC5BC,0BAA0B,EAAE,IAAI;cAAC;cACjCC,mBAAmB,EAAE,IAAI;cAAC;cAC1BC,eAAe,EAAE,IAAI;cAAE;cACvBC,mBAAmB,EAAE,IAAI;cAAC;cAC1BC,iBAAiB,EAAE,IAAI;cAAE;cACzBC,mBAAmB,EAAE,IAAI;cAAE;cAC3BC,iBAAiB,EAAE,IAAI;cAAE;cACzBC,iBAAiB,EAAE,IAAI;cAAE;cACzBC,oBAAoB,EAAE,IAAI;cAAE;cAC5BC,oBAAoB,EAAE,IAAI;cAAE;cAC5BC,iBAAiB,EAAE,IAAI;cAAE;cACzBC,OAAO,EAAE,CACP,CACE;gBAAC9C,KAAK,EAAE,IAAI;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,OAAO;gBAAEE,KAAK,EAAE,EAAE;gBAAE0C,OAAO,EAAE,KAAK;gBAAEC,IAAI,EAAC,KAAK;gBAAEC,MAAM,EAAC;cAAK,CAAC,EACnG;gBAAClD,KAAK,EAAE,QAAQ;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,WAAW;gBAAEE,KAAK,EAAE,GAAG;gBAAE0C,OAAO,EAAE,IAAI;gBAAEC,IAAI,EAAC,KAAK;gBAAEC,MAAM,EAAC;cAAK,CAAC,EAC3G;gBAAClD,KAAK,EAAE,IAAI;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,WAAW;gBAAEE,KAAK,EAAE,GAAG;gBAAE0C,OAAO,EAAE,IAAI;gBAAEE,MAAM,EAAC;cAAK,CAAC,EAC3F;gBAAClD,KAAK,EAAE,IAAI;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,aAAa;gBAAEE,KAAK,EAAE,EAAE;gBAAE0C,OAAO,EAAE;cAAI,CAAC,EAC9E;gBAAChD,KAAK,EAAE,IAAI;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,aAAa;gBAAEE,KAAK,EAAE,EAAE;gBAAE0C,OAAO,EAAE;cAAI,CAAC,EAC9E;gBAAChD,KAAK,EAAE,QAAQ;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,aAAa;gBAAEE,KAAK,EAAE,GAAG;gBAAE0C,OAAO,EAAE;cAAI,CAAC,EACnF;gBAAChD,KAAK,EAAE,QAAQ;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,gBAAgB;gBAAEE,KAAK,EAAE,GAAG;gBAAE0C,OAAO,EAAE;cAAI,CAAC,EACtF;gBAAChD,KAAK,EAAE,SAAS;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,aAAa;gBAAEE,KAAK,EAAE,GAAG;gBAAE0C,OAAO,EAAE;cAAK,CAAC,EACrF;gBAAChD,KAAK,EAAE,SAAS;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,gBAAgB;gBAAEE,KAAK,EAAE,GAAG;gBAAE0C,OAAO,EAAE;cAAK,CAAC,EACxF;gBAAChD,KAAK,EAAE,cAAc;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,kBAAkB;gBAAEE,KAAK,EAAE,GAAG;gBAAE0C,OAAO,EAAE;cAAI,CAAC,EAC9F;gBAAChD,KAAK,EAAE,UAAU;gBAAE+C,KAAK,EAAE,QAAQ;gBAAE3C,KAAK,EAAE,cAAc;gBAAEE,KAAK,EAAE,GAAG;gBAAE0C,OAAO,EAAE;cAAI,CAAC,CACvF,CACF;cACD;cACAG,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAYlD,IAAI,EAAEmD,GAAG,EAAEC,KAAK,EAAE;gBAC5C;gBACA;gBACA,IAAGD,GAAG,CAAChD,KAAK,KAAK,WAAW,IAAIH,IAAI,CAAC8B,SAAS,EAAC;kBAC7C,OAAO,CAAC,CAAC,EAAC,GAAG,CAAC;gBAChB;gBACA,IAAIqB,GAAG,CAAChD,KAAK,KAAK,OAAO,IAAIgD,GAAG,CAAChD,KAAK,KAAK,WAAW,IAAMH,IAAI,CAAC8B,SAAS,EAAC;kBACzE,OAAO,CAAC,CAAC,EAAC,CAAC,CAAC;gBACd;gBACA;gBACA,IAAGsB,KAAK,KAAG,CAAC,IAAIpD,IAAI,CAAC+B,QAAQ,EAAC;kBAC5B,OAAO,CAAC,CAAC,EAAC,GAAG,CAAC;gBAChB;gBACA,IAAGqB,KAAK,KAAG,CAAC,IAAIpD,IAAI,CAAC+B,QAAQ,EAAC;kBAC5B,OAAO,CAAC,CAAC,EAAC,CAAC,CAAC;gBACd;cACF;YAEF,CAAC,CACF,CAAC,EACF,IAAI1D,OAAO,CAACwB,qBAAqB,CAAC,IAAI,EAAE,CACtC;cACEC,GAAG,EAAE,gBAAgB;cACrBC,KAAK,EAAE,IAAI;cACXE,IAAI,EAAE,OAAO;cACbC,OAAO,EAAC;gBACNC,KAAK,EAAC;cACR;YACF,CAAC,EACD;cACEA,KAAK,EAAE,OAAO;cACdL,GAAG,EAAE,gBAAgB;cACrBC,KAAK,EAAE,IAAI;cACXE,IAAI,EAAE,OAAO;cACbC,OAAO,EAAC;gBACNC,KAAK,EAAC;cACR;YACF,CAAC,EACD;cACEA,KAAK,EAAE,MAAM;cACbL,GAAG,EAAE,eAAe;cACpBC,KAAK,EAAE,IAAI;cACXE,IAAI,EAAE,MAAM;cACZC,OAAO,EAAC;gBACNC,KAAK,EAAC;cACR;YACF,CAAC,EACD;cACEA,KAAK,EAAE,MAAM;cACbL,GAAG,EAAE,eAAe;cACpBC,KAAK,EAAE,IAAI;cACXE,IAAI,EAAE,MAAM;cACZC,OAAO,EAAC;gBACNC,KAAK,EAAC;cACR;YACF,CAAC,CACF,CAAC,CAEN,CAAC;UAAC;UAAA;YAAA,OAAAd,QAAA,CAAAgE,IAAA;QAAA;MAAA,GAAAtE,OAAA;IAAA,CACH;IAAA,gBApYGL,eAAeA,CAAA4E,EAAA;MAAA,OAAA3E,IAAA,CAAA4E,KAAA,OAAAC,SAAA;IAAA;EAAA,GAoYlB;EACD,OAAO;IACL9E,eAAe,EAAEA;EACnB,CAAC;AACH,CAAC;AAED,eAAe,CAAC;EACd+E,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,CAAC;EACRC,CAAC,EAAEnF,oBAAoB,CAAC;AAC1B,CAAC,CAAC", "ignoreList": []}]}
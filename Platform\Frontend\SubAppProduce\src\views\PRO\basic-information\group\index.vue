<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div class="cs-z-page-main-content">
      <top-header padding="0">
        <template #right>
          <el-form label-width="80px" :inline="true" @submit.native.prevent>
            <el-form-item label="班组名称">
              <el-input v-model="keywords" placeholder="请输入" clearable @keyup.enter.native="handleSearch" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
        </template>
        <template #left>
          <el-button type="primary" @click="handleAdd">新增</el-button>
          <el-button
            type="danger"
            :disabled="!selectList.length"
            @click="handleDelete(true)"
          >删除</el-button>
        </template>
      </top-header>
      <div v-loading="tbLoading" class="fff cs-z-tb-wrapper">
        <vxe-table
          ref="xTable"
          v-loading="tbLoading"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          element-loading-spinner="el-icon-loading"
          element-loading-text="拼命加载中"
          empty-text="暂无数据"
          height="100%"
          :data="tbData"
          stripe
          resizable
          :auto-resize="true"
          class="cs-vxe-table"
          :tooltip-config="{ enterable: true }"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange"
        >
          <vxe-column fixed="left" type="checkbox" width="60" />
          <vxe-column
            v-for="(item, index) in columns"
            :key="index"
            :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
            show-overflow="tooltip"
            sortable
            :align="item.Align"
            :field="item.Code"
            :title="item.Display_Name"
            :visible="item.Is_Display"
          >
            <template #default="{ row }">
              <span v-if="item.Code === 'Manager_UserName'">
                <div>{{ row.Manager_UserName || "-" }}</div>
              </span>
              <span v-else-if="item.Code === 'Load_Unit'">
                <div>{{ row.Load_Unit || "-" }}</div>
              </span>
              <span v-else-if="item.Code === 'Workshop_Name'">
                <div>{{ row.Workshop_Name || "-" }}</div>
              </span>
              <span v-else-if="item.Code === 'Is_Outsource'">
                <el-tag v-if="row.Is_Outsource" type="success">是</el-tag><el-tag v-else type="info">否</el-tag>
              </span>
              <span v-else-if="item.Code === 'Is_Enabled'">
                <el-tag v-if="row.Is_Enabled" type="success">是</el-tag><el-tag v-else type="info">否</el-tag>
              </span>
              <span v-else-if="item.Code === 'Sort'">
                <span>{{ item.Sort || item.Sort === 0 ? row[item.Code] : '-' }}</span>
              </span>
              <span v-else>{{ row[item.Code] || "-" }}</span>
            </template>
          </vxe-column>
          <vxe-column fixed="right" title="操作" width="160" show-overflow align="center">
            <template #default="{ row }">
              <el-button type="text" @click="handleDetail(row)">查看</el-button>
              <el-button type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button
                type="text"
                style="color: red"
                @click="handleDelete(false, row)"
              >删除</el-button>
            </template>
          </vxe-column>
        </vxe-table>
        <!-- <dynamic-data-table
          ref="dyTable"
          :columns="columns"
          :config="tbConfig"
          :data="tbData"
          :page="queryInfo.Page"
          :total="total"
          border
          stripe
          class="cs-plm-dy-table"
          @multiSelectedChange="handleSelectionChange"
          @gridPageChange="handlePageChange"
          @gridSizeChange="handlePageChange"
          @tableSearch="tableSearch"
        >
          <template slot="Manager_UserName" slot-scope="{ row }">
            <div>{{ row.Manager_UserName || "-" }}</div>
          </template>
          <template slot="Load_Unit" slot-scope="{ row }">
            <div>{{ row.Load_Unit || "-" }}</div>
          </template>
          <template slot="Workshop_Name" slot-scope="{ row }">
            <div>{{ row.Workshop_Name || "-" }}</div>
          </template>
          <template slot="Is_Outsource" slot-scope="{ row }">
            <div><el-tag v-if="row.Is_Outsource" type="success">是</el-tag><el-tag v-else type="info">否</el-tag></div>
          </template>
          <template slot="Is_Enabled" slot-scope="{ row }">
            <div><el-tag v-if="row.Is_Enabled" type="success">是</el-tag><el-tag v-else type="info">否</el-tag></div>
          </template>
          <template slot="op" slot-scope="{ row }">
            <el-button type="text" @click="handleDetail(row)">查看</el-button>
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button
              type="text"
              style="color: red"
              @click="handleDelete(false, row)"
            >删除</el-button>
          </template>
        </dynamic-data-table> -->
      </div>
    </div>

    <el-dialog
      v-dialogDrag
      class="cs-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      custom-class="dialogCustomClass"
      width="900px"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        ref="content"
        :dialog-visible="dialogVisible"
        @close="handleClose"
        @refresh="fetchData"
      />
    </el-dialog>
  </div>
</template>

<script>
import getTbInfo from '@/mixins/PRO/get-table-info'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import TopHeader from '@/components/TopHeader'
import { DeleteWorkingTeams, GetWorkingTeams, GetWorkingTeamsPageList } from '@/api/PRO/technology-lib'
import detail from './component/detail'
import info from './component/info'
import getCommonData from '@/mixins/PRO/get-common-data'

export default {
  name: 'PROGroup',
  components: {
    DynamicDataTable,
    TopHeader,
    info,
    detail
  },
  mixins: [getTbInfo, getCommonData],
  data() {
    return {
      tbConfig: {
        Pager_Align: 'center'
      },
      queryInfo: {
        Page: 1,
        PageSize: 10,
        SortName: 'Sort',
        SortOrder: 'asc',
        ParameterJson: []
      },
      currentComponent: '',
      title: '',
      columns: [],
      tbData: [],
      total: 0,
      tbLoading: false,
      dialogVisible: false,
      selectList: [],
      keywords: ''
    }
  },
  watch: {
    columns(e) {
      // 车间未开启不显示所属车间信息
      if (!this.FactoryDetailData.Is_Workshop_Enabled) {
        e.map((item) => {
          if (item.Code === 'Workshop_Name') {
            item.Is_Display = false
          }
        })
      }
    }
  },
  async created() {
    // 获取工厂详情
    await this.getCurFactory()
    this.tbLoading = true
    await this.getTableConfig('pro_group_list')
    await this.fetchData()
  },
  methods: {
    fetchData() {
      this.tbLoading = true
      GetWorkingTeamsPageList({ keywords: this.keywords, pageInfo: this.queryInfo }).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data
          this.total = res.Data.TotalCount
          console.log(this.total)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.tbLoading = false
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleSelectionChange(list) {
      console.log('list', list)
      this.selectList = list.records
    },
    handleAdd() {
      this.currentComponent = 'detail'
      this.title = '新增'
      this.dialogVisible = true
      this.$nextTick((_) => {
        this.$refs['content'].initData('', this.FactoryDetailData.Is_Workshop_Enabled)
      })
    },
    handleEdit(row) {
      this.currentComponent = 'detail'
      this.title = '编辑'
      this.dialogVisible = true
      this.$nextTick((_) => {
        this.$refs['content'].initData(row.Id, this.FactoryDetailData.Is_Workshop_Enabled)
      })
    },
    handleDetail(row) {
      this.currentComponent = 'info'
      this.title = '查看'
      this.dialogVisible = true
      this.$nextTick((_) => {
        this.$refs['content'].initData(row, this.FactoryDetailData.Is_Workshop_Enabled)
      })
    },
    handleDelete(isAll, row) {
      const ids = !isAll ? row.Id : this.selectList.map((i) => i.Id).toString()
      this.$confirm('是否删除选中班组?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          console.log('id', ids)
          DeleteWorkingTeams({
            ids: ids.toString()
          }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.$refs.xTable.clearCheckboxRow()
              this.selectList = []
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleSearch() {
      this.$refs.xTable.clearCheckboxRow()
      this.selectList = []
      this.fetchData()
    },
    reset() {
      this.keywords = ''
      this.$refs.xTable.clearCheckboxRow()
      this.selectList = []
      this.fetchData()
    }
  }
}
</script>

<style scoped lang="scss">
.cs-dialog {
  ::v-deep {
    .el-dialog__body {
      padding-top: 0;
    }
  }
}
::v-deep {
  .cs-top-header-box {
    line-height: 0px;
  }
}

::v-deep .pagination {
    justify-content: flex-end !important;
    margin-top: 12px !important;
    .el-input--small .el-input__inner {
        height: 28px;
        line-height: 28px;
    }
}
</style>

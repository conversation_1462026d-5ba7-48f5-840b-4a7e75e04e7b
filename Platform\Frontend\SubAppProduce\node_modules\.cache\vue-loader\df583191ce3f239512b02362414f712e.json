{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\components\\OverallControlPlanContent.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\components\\OverallControlPlanContent.vue", "mtime": 1757926768460}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBFeHBvcnRUb3RhbENvbnRyb2xQbGFuLCBHZW5lcmF0ZVByb2plY3RQbGFuQnVzaW5lc3NEYXRhLA0KICBHZXRDb25maWdzLA0KICBHZXRNb25vbWVyTGlzdEJ5UHJvamVjdElkLA0KICBHZXRUb3RhbENvbnRyb2xQbGFuRW50aXR5LA0KICBHZXRUb3RhbENvbnRyb2xQbGFuTGlzdCwNCiAgU2F2ZVRvdGFsQ29udHJvbFBsYW5FbnRpdHkNCn0gZnJvbSAnQC9hcGkvcGxtL3Byb2plY3RzJw0KaW1wb3J0IFNlbGVjdEFyZWEgZnJvbSAnQC9jb21wb25lbnRzL1NlbGVjdC9TZWxlY3RBcmVhL2luZGV4LnZ1ZScNCmltcG9ydCBtb21lbnQgZnJvbSAnbW9tZW50Jw0KaW1wb3J0IHsgY29tYmluZVVSTCB9IGZyb20gJ0AvdXRpbHMnDQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gJ0AvdXRpbHMvYXV0aCcNCmltcG9ydCB7IGlzUm91dGVOYW1lRXhpc3RzIH0gZnJvbSAnQC91dGlscy9yb3V0ZXInDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ092ZXJhbGxDb250cm9sUGxhbkNvbnRlbnQnLA0KICBjb21wb25lbnRzOiB7IFNlbGVjdEFyZWEgfSwNCiAgcHJvcHM6IHsNCiAgICBjdXJQcm9qZWN0OiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBxdWVyeU1vZGVsOiB7DQogICAgICAgIEFyZWFzOiBbXSwNCiAgICAgICAgUmFuZ2U6IFtdDQogICAgICB9LA0KICAgICAgLy8g5oC75bel5pyf5ZKM5Ymp5L2Z5bel5pyfDQogICAgICB0b3RhbFByb2plY3REdXJhdGlvbjogMCwNCiAgICAgIHJlbWFpbmluZ0R1cmF0aW9uOiAwLA0KICAgICAgY29uZmlnOiB7DQogICAgICAgIHBhZ2VTaXplT3B0aW9uczogWzEwLCAyMCwgNTAsIDEwMF0sDQogICAgICAgIHBhZ2VTaXplOiAyMCwNCiAgICAgICAgdG90YWw6IDAsDQogICAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICBjdXJyZW50UGFnZTogMSwNCiAgICAgICAgdGFibGVDb2x1bW5zOiBbXSwNCiAgICAgICAgdGFibGVEYXRhOiBbXSwNCiAgICAgICAga2V5RmllbGQ6ICdBcmVhX0lkJw0KICAgICAgfSwNCiAgICAgIG1vbm9tZXJMaXN0OiBbXSwNCiAgICAgIG5vZGVMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueeql+ebuOWFs+aVsOaNrg0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBjdXJyZW50Um93OiB7fSwNCiAgICAgIHBsYW5Gb3JtOiB7DQogICAgICAgIEFyZWFfSWQ6ICcnLA0KICAgICAgICBEZWVwZW5fQmVnaW5fRGF0ZTogJycsDQogICAgICAgIERlZXBlbl9FbmRfRGF0ZTogJycsDQogICAgICAgIERlZXBlbl9EYXRlX1JhbmdlOiBbXSwNCiAgICAgICAgRGVlcGVuX0R1cmF0aW9uOiAwLA0KICAgICAgICBEZWVwZW5fRW5naW5lZXJfUXVhbnRpdHk6IDAsDQogICAgICAgIFB1cmNoYXNlX0JlZ2luX0RhdGU6ICcnLA0KICAgICAgICBQdXJjaGFzZV9FbmRfRGF0ZTogJycsDQogICAgICAgIFB1cmNoYXNlX0RhdGVfUmFuZ2U6IFtdLA0KICAgICAgICBQdXJjaGFzZV9EdXJhdGlvbjogMCwNCiAgICAgICAgUHVyY2hhc2VfRW5naW5lZXJfUXVhbnRpdHk6IDAsDQogICAgICAgIFByb2R1Y3RfQmVnaW5fRGF0ZTogJycsDQogICAgICAgIFByb2R1Y3RfRW5kX0RhdGU6ICcnLA0KICAgICAgICBQcm9kdWN0X0RhdGVfUmFuZ2U6IFtdLA0KICAgICAgICBQcm9kdWN0X0R1cmF0aW9uOiAwLA0KICAgICAgICBQcm9kdWN0X0VuZ2luZWVyX1F1YW50aXR5OiAwLA0KICAgICAgICBJbnN0YWxsX0JlZ2luX0RhdGU6ICcnLA0KICAgICAgICBJbnN0YWxsX0VuZF9EYXRlOiAnJywNCiAgICAgICAgSW5zdGFsbF9EYXRlX1JhbmdlOiBbXSwNCiAgICAgICAgSW5zdGFsbF9EdXJhdGlvbjogMCwNCiAgICAgICAgSW5zdGFsbF9FbmdpbmVlcl9RdWFudGl0eTogMA0KICAgICAgfSwNCiAgICAgIGNsb25lVGFibGVEYXRhOiBbXSwNCiAgICAgIGV4cG9ydExvYWRpbmc6IGZhbHNlLA0KICAgICAgdXBkYXRlTG9hZGluZzogZmFsc2UsDQogICAgICBzYXZlTG9hZGluZzogZmFsc2UsDQogICAgICBoZWFkZXJzOiB7DQogICAgICAgIEF1dGhvcml6YXRpb246IGdldFRva2VuKCksDQogICAgICAgIExhc3RfV29ya2luZ19PYmplY3RfSWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJykNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgdXBsb2FkRGF0YSgpIHsNCiAgICAgIHJldHVybiB7IGRhdGE6IEpTT04uc3RyaW5naWZ5KHsgUHJvamVjdElkOiB0aGlzLmN1clByb2plY3Q/LlN5c19Qcm9qZWN0X0lkLCBDb21wYW55SWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpIH0pIH0NCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgY3VyUHJvamVjdDogew0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uKCkgew0KICAgICAgICBpZiAodGhpcy5jdXJQcm9qZWN0ICYmIHRoaXMuY3VyUHJvamVjdC5TeXNfUHJvamVjdF9JZCkgew0KICAgICAgICAgIHRoaXMucXVlcnlNb2RlbC5BcmVhcyA9IFtdDQogICAgICAgICAgdGhpcy5nZXRNb25vbWVyTGlzdCgpDQogICAgICAgICAgdGhpcy5nZXRUYWJsZURhdGEoKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIGF3YWl0IHRoaXMuZ2V0Q29uZmlnKCkNCiAgICBpZiAodGhpcy5jdXJQcm9qZWN0ICYmIHRoaXMuY3VyUHJvamVjdC5TeXNfUHJvamVjdF9JZCkgew0KICAgICAgdGhpcy5nZXRNb25vbWVyTGlzdCgpDQogICAgICB0aGlzLmdldFRhYmxlRGF0YSgpDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgY2VsbFN0eWxlKHsgY29sdW1uIH0pIHsNCiAgICAgIGNvbnN0IHN0eWxlID0ge30NCiAgICAgIGlmIChjb2x1bW4uZmllbGQgJiYgY29sdW1uLmZpZWxkLnN0YXJ0c1dpdGgoJ0RlZXBlbicpKSB7DQogICAgICAgIHN0eWxlLmJhY2tncm91bmRDb2xvciA9IGByZ2JhKDQxLCAxNDEsIDI1NSwgMC4xKSFpbXBvcnRhbnRgDQogICAgICB9DQogICAgICBpZiAoY29sdW1uLmZpZWxkICYmIGNvbHVtbi5maWVsZC5zdGFydHNXaXRoKCdQdXJjaGFzZScpKSB7DQogICAgICAgIHN0eWxlLmJhY2tncm91bmRDb2xvciA9IGByZ2JhKDE4OSwgMTA5LCAyNDYsIDAuMSkhaW1wb3J0YW50YA0KICAgICAgfQ0KICAgICAgaWYgKGNvbHVtbi5maWVsZCAmJiBjb2x1bW4uZmllbGQuc3RhcnRzV2l0aCgnUHJvZHVjdCcpKSB7DQogICAgICAgIHN0eWxlLmJhY2tncm91bmRDb2xvciA9IGByZ2JhKDAsIDE5NSwgOTcsIDAuMSkhaW1wb3J0YW50YA0KICAgICAgfQ0KICAgICAgaWYgKGNvbHVtbi5maWVsZCAmJiBjb2x1bW4uZmllbGQuc3RhcnRzV2l0aCgnSW5zdGFsbCcpKSB7DQogICAgICAgIHN0eWxlLmJhY2tncm91bmRDb2xvciA9IGByZ2JhKDAsIDE3NywgMTkxLCAwLjEpIWltcG9ydGFudGANCiAgICAgIH0NCiAgICAgIHJldHVybiBzdHlsZQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0Q29uZmlnKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0Q29uZmlncyh7DQogICAgICAgIENvbXBhbnlJZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJykNCiAgICAgIH0pDQogICAgICB0aGlzLm5vZGVMaXN0ID0gcmVzLkRhdGEubWFwKGl0ZW0gPT4gew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIC4uLml0ZW0sDQogICAgICAgICAgbmFtZTogaXRlbS5QbGFuX05hbWUsDQogICAgICAgICAgY29kZTogWydEZWVwZW4nLCAnUHVyY2hhc2UnLCAnUHJvZHVjdCcsICdJbnN0YWxsJ11baXRlbS5QbGFuX1R5cGUgLSAxXSwNCiAgICAgICAgICBpY29uOiByZXF1aXJlKGBAL2Fzc2V0cy9QTE0vcGxhbl9zdW1tYXJ5XyR7aXRlbS5QbGFuX1R5cGV9LnBuZ2ApLA0KICAgICAgICAgIGNvbG9yOiBbJyMyOThERkYnLCAnI0JENkRGNicsICcjMDBDMzYxJywgJyMwMEIxQkYnXVtpdGVtLlBsYW5fVHlwZSAtIDFdLA0KICAgICAgICAgIHJvdXRlOiBbJ1BST0RlZXBlblBsYW5UcmFja0NvbScsICdQUk9QdXJjaGFzZVBsYW5UcmFja0NvbScsICdQUk9Qcm9kdWNlUGxhblRyYWNrQ29tJywgJ1BST0NvbnN0cnVjdGlvblBsYW5UcmFja0NvbSddW2l0ZW0uUGxhbl9UeXBlIC0gMV0sDQogICAgICAgICAgdmlzaWJsZTogdHJ1ZQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgdGhpcy5nZXRSZW5kZXJDb2x1bW5zKCkNCiAgICB9LA0KICAgIGdldFJlbmRlckNvbHVtbnMoKSB7DQogICAgICBjb25zdCBjb2x1bW5zID0gdGhpcy5ub2RlTGlzdC5maWx0ZXIoaSA9PiBpLnZpc2libGUpLm1hcChpdGVtID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICB3aWR0aDogMTUwLA0KICAgICAgICAgIGxhYmVsOiBpdGVtLm5hbWUsDQogICAgICAgICAgb3RoZXJPcHRpb25zOiB7IGFsaWduOiAnY2VudGVyJywgZml4ZWQ6ICcnIH0sDQogICAgICAgICAga2V5OiBpdGVtLmNvZGUsDQogICAgICAgICAgY2hpbGRyZW46IFsNCiAgICAgICAgICAgIHsga2V5OiBpdGVtLmNvZGUgKyAnX0JlZ2luX0RhdGUnLCB3aWR0aDogJzE2MCcsDQogICAgICAgICAgICAgIGxhYmVsOiAn5byA5aeL5pe26Ze0Jywgb3RoZXJPcHRpb25zOiB7IGFsaWduOiAnY2VudGVyJyB9LA0KICAgICAgICAgICAgICByZW5kZXI6IChyb3cpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAoIXJvd1tpdGVtLmNvZGUgKyAnX0JlZ2luX0RhdGUnXSkgcmV0dXJuICctJw0KICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gbW9tZW50KHJvd1tpdGVtLmNvZGUgKyAnX0JlZ2luX0RhdGUnXSkuZm9ybWF0KCdZWVlZLU1NLUREJykNCiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2Jywge30sIHZhbHVlKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgeyBrZXk6IGl0ZW0uY29kZSArICdfRW5kX0RhdGUnLCB3aWR0aDogJzE2MCcsDQogICAgICAgICAgICAgIGxhYmVsOiAn57uT5p2f5pe26Ze0Jywgb3RoZXJPcHRpb25zOiB7IGFsaWduOiAnY2VudGVyJyB9LA0KICAgICAgICAgICAgICByZW5kZXI6IChyb3cpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAoIXJvd1tpdGVtLmNvZGUgKyAnX0VuZF9EYXRlJ10pIHJldHVybiAnLScNCiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZSA9IG1vbWVudChyb3dbaXRlbS5jb2RlICsgJ19FbmRfRGF0ZSddKS5mb3JtYXQoJ1lZWVktTU0tREQnKQ0KICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLiRjcmVhdGVFbGVtZW50KCdkaXYnLCB7IH0sIHZhbHVlKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgeyBrZXk6IGl0ZW0uY29kZSArICdfRHVyYXRpb24nLCB3aWR0aDogJzE2MCcsDQogICAgICAgICAgICAgIGxhYmVsOiAn5oC75bel5pyfKOWkqSknLCBvdGhlck9wdGlvbnM6IHsgYWxpZ246ICdjZW50ZXInIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7IGtleTogaXRlbS5jb2RlICsgJ19FbmdpbmVlcl9RdWFudGl0eScsIHdpZHRoOiAnMTYwJywNCiAgICAgICAgICAgICAgbGFiZWw6ICforqHliJLlt6XnqIvph48odCknLCBvdGhlck9wdGlvbnM6IHsgYWxpZ246ICdjZW50ZXInIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7IGtleTogaXRlbS5jb2RlICsgJ19GaW5pc2hfUXVhbnRpdHknLCB3aWR0aDogJzE2MCcsDQogICAgICAgICAgICAgIGxhYmVsOiAn5a6e6ZmF6YePKHQpJywgb3RoZXJPcHRpb25zOiB7IGFsaWduOiAnY2VudGVyJyB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgeyBrZXk6IGl0ZW0uY29kZSArICdfRmluaXNoX1N0YXRlJywgd2lkdGg6ICcxNjAnLA0KICAgICAgICAgICAgICBsYWJlbDogJ+WujOaIkOeKtuaAgScsIG90aGVyT3B0aW9uczogeyBhbGlnbjogJ2NlbnRlcicgfSwNCiAgICAgICAgICAgICAgcmVuZGVyOiAocm93KSA9PiB7DQogICAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSByb3dbaXRlbS5jb2RlICsgJ19GaW5pc2hfU3RhdGUnXQ0KICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLiRjcmVhdGVFbGVtZW50KCdkaXYnLCB7DQogICAgICAgICAgICAgICAgICBzdHlsZTogew0KICAgICAgICAgICAgICAgICAgICBjb2xvcjogdmFsdWUgPT09ICflt7LlrozmiJAnID8gJyM2N0MyM0EnIDogdmFsdWUgPT09ICfmnKrlrozmiJAnID8gJyNGNTZDNkMnIDogJycNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9LCB2YWx1ZSkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMuY29uZmlnLnRhYmxlQ29sdW1ucyA9IFt7DQogICAgICAgIHdpZHRoOiAxNTAsIGxhYmVsOiAn5Y2V5L2TL+WIhuWMuicsIGtleTogJ0Z1bGxBcmVhTmFtZScsIG90aGVyT3B0aW9uczogeyBhbGlnbjogJ2xlZnQnLCBmaXhlZDogJ2xlZnQnIH0NCiAgICAgIH0sIC4uLmNvbHVtbnNdDQogICAgfSwNCiAgICBzZWFyY2goKSB7DQogICAgICAvLyDov4fmu6TmuLLmn5PliJcNCiAgICAgIHRoaXMubm9kZUxpc3QuZm9yRWFjaChpID0+IGkudmlzaWJsZSA9IHRydWUpDQogICAgICBpZiAodGhpcy5xdWVyeU1vZGVsLlJhbmdlICYmIHRoaXMucXVlcnlNb2RlbC5SYW5nZS5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMucXVlcnlNb2RlbC5SYW5nZS5mb3JFYWNoKGkgPT4gew0KICAgICAgICAgIHRoaXMubm9kZUxpc3QuZm9yRWFjaChqID0+IHsNCiAgICAgICAgICAgIGlmIChpID09PSBqLmNvZGUpIHsNCiAgICAgICAgICAgICAgai52aXNpYmxlID0gZmFsc2UNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgdGhpcy5nZXRSZW5kZXJDb2x1bW5zKCkNCiAgICAgIC8vIOi/h+a7pOWMuuWfnw0KICAgICAgaWYgKHRoaXMucXVlcnlNb2RlbC5BcmVhcyAmJiB0aGlzLnF1ZXJ5TW9kZWwuQXJlYXMubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLmNvbmZpZy50YWJsZURhdGEgPSB0aGlzLmNsb25lVGFibGVEYXRhLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gdGhpcy5xdWVyeU1vZGVsLkFyZWFzLmluY2x1ZGVzKGl0ZW0uQXJlYV9JZCkNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuY29uZmlnLnRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5jbG9uZVRhYmxlRGF0YSkpDQogICAgICB9DQogICAgICB0aGlzLmhhbmRsZVRhYmxlRGF0YSgpDQogICAgfSwNCiAgICBnZXRUYWJsZURhdGEoKSB7DQogICAgICBpZiAoIXRoaXMuY3VyUHJvamVjdCB8fCAhdGhpcy5jdXJQcm9qZWN0LlN5c19Qcm9qZWN0X0lkKSByZXR1cm4NCiAgICAgIHRoaXMuY29uZmlnLmxvYWRpbmcgPSB0cnVlDQogICAgICBHZXRUb3RhbENvbnRyb2xQbGFuTGlzdCh7DQogICAgICAgIFByb2plY3RJZDogdGhpcy5jdXJQcm9qZWN0LlN5c19Qcm9qZWN0X0lkLA0KICAgICAgICBDb21wYW55SWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuY29uZmlnLnRhYmxlRGF0YSA9IHJlcy5EYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLmlkID0gaXRlbS5BcmVhX0lkDQogICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5jbG9uZVRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocmVzLkRhdGEpKQ0KICAgICAgICB0aGlzLmhhbmRsZVRhYmxlRGF0YSgpDQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5jb25maWcubG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlVGFibGVEYXRhKCkgew0KICAgICAgLy8g5pS26ZuG5omA5pyJ5pel5pyf55So5LqO6K6h566X5oC75bel5pyf5ZKM5Ymp5L2Z5bel5pyfDQogICAgICBjb25zdCBhbGxCZWdpbkRhdGVzID0gW10NCiAgICAgIGNvbnN0IGFsbEVuZERhdGVzID0gW10NCiAgICAgIHRoaXMudG90YWxQcm9qZWN0RHVyYXRpb24gPSAwDQogICAgICB0aGlzLnJlbWFpbmluZ0R1cmF0aW9uID0gMA0KICAgICAgdGhpcy5ub2RlTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBjb25zdCBiZWdpbkRhdGVzID0gdGhpcy5jb25maWcudGFibGVEYXRhDQogICAgICAgICAgLm1hcChyb3cgPT4gcm93W2l0ZW0uY29kZSArICdfQmVnaW5fRGF0ZSddKQ0KICAgICAgICAgIC5maWx0ZXIoZGF0ZSA9PiBkYXRlICYmIGRhdGUudHJpbSgpICE9PSAnJykNCiAgICAgICAgY29uc3QgZW5kRGF0ZXMgPSB0aGlzLmNvbmZpZy50YWJsZURhdGENCiAgICAgICAgICAubWFwKHJvdyA9PiByb3dbaXRlbS5jb2RlICsgJ19FbmRfRGF0ZSddKQ0KICAgICAgICAgIC5maWx0ZXIoZGF0ZSA9PiBkYXRlICYmIGRhdGUudHJpbSgpICE9PSAnJykNCg0KICAgICAgICBhbGxCZWdpbkRhdGVzLnB1c2goLi4uYmVnaW5EYXRlcykNCiAgICAgICAgYWxsRW5kRGF0ZXMucHVzaCguLi5lbmREYXRlcykNCiAgICAgIH0pDQogICAgICAvLyDorqHnrpfpobnnm67mgLvlt6XmnJ/vvJrmnIDmmZrml6XmnJ8gLSDmnIDml6nml6XmnJ8gKyAxDQogICAgICBpZiAoYWxsQmVnaW5EYXRlcy5sZW5ndGggPiAwICYmIGFsbEVuZERhdGVzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgY29uc3QgZWFybGllc3REYXRlID0gYWxsQmVnaW5EYXRlcy5zb3J0KClbMF0NCiAgICAgICAgY29uc3QgbGF0ZXN0RGF0ZSA9IGFsbEVuZERhdGVzLnNvcnQoKGEsIGIpID0+IG5ldyBEYXRlKGIpIC0gbmV3IERhdGUoYSkpWzBdDQoNCiAgICAgICAgaWYgKGVhcmxpZXN0RGF0ZSAmJiBsYXRlc3REYXRlKSB7DQogICAgICAgICAgY29uc3QgYmVnaW4gPSBuZXcgRGF0ZShlYXJsaWVzdERhdGUpDQogICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUobGF0ZXN0RGF0ZSkNCiAgICAgICAgICB0aGlzLnRvdGFsUHJvamVjdER1cmF0aW9uID0gTWF0aC5mbG9vcigoZW5kIC0gYmVnaW4pIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKSArIDENCiAgICAgICAgICAvLyDorqHnrpfliankvZnlt6XmnJ/vvJrmnIDmmZrml6XmnJ8gLSDlvZPliY3ml6XmnJ8gKyAxDQogICAgICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgY29uc3QgcmVtYWluaW5nRGF5cyA9IE1hdGguZmxvb3IoKGVuZCAtIHRvZGF5KSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSkgKyAxDQogICAgICAgICAgdGhpcy5yZW1haW5pbmdEdXJhdGlvbiA9IHJlbWFpbmluZ0RheXMgPiAwID8gcmVtYWluaW5nRGF5cyA6IDANCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICB0aGlzLm5vZGVMaXN0ID0gdGhpcy5ub2RlTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgIC8vIOiuoeeul+iuoeWIkuW3peeoi+mHj+axh+aAuw0KICAgICAgICBjb25zdCBwbGFuU3VtbWFyeSA9IHRoaXMuY29uZmlnLnRhYmxlRGF0YS5yZWR1Y2UoKHN1bSwgcm93KSA9PiB7DQogICAgICAgICAgY29uc3QgdmFsdWUgPSBwYXJzZUZsb2F0KHJvd1tpdGVtLmNvZGUgKyAnX0VuZ2luZWVyX1F1YW50aXR5J10pIHx8IDANCiAgICAgICAgICByZXR1cm4gc3VtICsgdmFsdWUNCiAgICAgICAgfSwgMCkNCg0KICAgICAgICAvLyDorqHnrpflrp7pmYXlrozmiJDph4/msYfmgLsNCiAgICAgICAgY29uc3QgZmluaXNoU3VtbWFyeSA9IHRoaXMuY29uZmlnLnRhYmxlRGF0YS5yZWR1Y2UoKHN1bSwgcm93KSA9PiB7DQogICAgICAgICAgY29uc3QgdmFsdWUgPSBwYXJzZUZsb2F0KHJvd1tpdGVtLmNvZGUgKyAnX0ZpbmlzaF9RdWFudGl0eSddKSB8fCAwDQogICAgICAgICAgcmV0dXJuIHN1bSArIHZhbHVlDQogICAgICAgIH0sIDApDQoNCiAgICAgICAgLy8g6K6h566X5a6M5oiQ55m+5YiG5q+UDQogICAgICAgIGNvbnN0IGZpbmlzaFBlcmNlbnQgPSBwbGFuU3VtbWFyeSA+IDAgPyAoZmluaXNoU3VtbWFyeSAvIHBsYW5TdW1tYXJ5ICogMTAwKSA6IDANCg0KICAgICAgICAvLyDojrflj5bmnIDml6nlvIDlp4vml6XmnJ/vvIjmjpLpmaTnqbrlgLzvvIkNCiAgICAgICAgY29uc3QgYmVnaW5EYXRlcyA9IHRoaXMuY29uZmlnLnRhYmxlRGF0YQ0KICAgICAgICAgIC5tYXAocm93ID0+IHJvd1tpdGVtLmNvZGUgKyAnX0JlZ2luX0RhdGUnXSkNCiAgICAgICAgICAuZmlsdGVyKGRhdGUgPT4gZGF0ZSAmJiBkYXRlLnRyaW0oKSAhPT0gJycpDQogICAgICAgICAgLnNvcnQoKQ0KICAgICAgICBjb25zdCBiZWdpbkRhdGUgPSBiZWdpbkRhdGVzLmxlbmd0aCA+IDAgPyBtb21lbnQoYmVnaW5EYXRlc1swXSkuZm9ybWF0KCdZWVlZL01NL0REJykgOiAnJw0KDQogICAgICAgIC8vIOiOt+WPluacgOaZmue7k+adn+aXpeacn++8iOaOkumZpOepuuWAvO+8iQ0KICAgICAgICBjb25zdCBlbmREYXRlcyA9IHRoaXMuY29uZmlnLnRhYmxlRGF0YQ0KICAgICAgICAgIC5tYXAocm93ID0+IHJvd1tpdGVtLmNvZGUgKyAnX0VuZF9EYXRlJ10pDQogICAgICAgICAgLmZpbHRlcihkYXRlID0+IGRhdGUgJiYgZGF0ZS50cmltKCkgIT09ICcnKQ0KICAgICAgICAgIC5zb3J0KChhLCBiKSA9PiBuZXcgRGF0ZShiKSAtIG5ldyBEYXRlKGEpKQ0KICAgICAgICBjb25zdCBlbmREYXRlID0gZW5kRGF0ZXMubGVuZ3RoID4gMCA/IG1vbWVudChlbmREYXRlc1swXSkuZm9ybWF0KCdZWVlZL01NL0REJykgOiAnJw0KDQogICAgICAgIC8vIOiuoeeul+aAu+W3peacnw0KICAgICAgICBsZXQgZHVyYXRpb24gPSAwDQogICAgICAgIGlmIChiZWdpbkRhdGUgJiYgZW5kRGF0ZSkgew0KICAgICAgICAgIGNvbnN0IGJlZ2luID0gbmV3IERhdGUoYmVnaW5EYXRlKQ0KICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKGVuZERhdGUpDQogICAgICAgICAgZHVyYXRpb24gPSBNYXRoLmZsb29yKChlbmQgLSBiZWdpbikgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpICsgMQ0KICAgICAgICB9DQoNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAuLi5pdGVtLA0KICAgICAgICAgIHBsYW5TdW1tYXJ5OiBwbGFuU3VtbWFyeS50b0ZpeGVkKDIpLA0KICAgICAgICAgIGZpbmlzaFN1bW1hcnk6IGZpbmlzaFN1bW1hcnkudG9GaXhlZCgyKSwNCiAgICAgICAgICBmaW5pc2hQZXJjZW50OiBmaW5pc2hQZXJjZW50LnRvRml4ZWQoMikgKyAnJScsDQogICAgICAgICAgYmVnaW5EYXRlOiBiZWdpbkRhdGUsDQogICAgICAgICAgZW5kRGF0ZTogZW5kRGF0ZSwNCiAgICAgICAgICBkdXJhdGlvbjogZHVyYXRpb24gPiAwID8gZHVyYXRpb24gKyAn5aSpJyA6ICcnDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRNb25vbWVyTGlzdCgpIHsNCiAgICAgIGlmICghdGhpcy5jdXJQcm9qZWN0IHx8ICF0aGlzLmN1clByb2plY3QuU3lzX1Byb2plY3RfSWQpIHJldHVybg0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0TW9ub21lckxpc3RCeVByb2plY3RJZCh7DQogICAgICAgIHByb2plY3RJZDogdGhpcy5jdXJQcm9qZWN0LlN5c19Qcm9qZWN0X0lkDQogICAgICB9KQ0KICAgICAgdGhpcy5tb25vbWVyTGlzdCA9IHJlcy5EYXRhDQogICAgfSwNCiAgICBvcGVuRGlhbG9nKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Um93ID0gcm93DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICBHZXRUb3RhbENvbnRyb2xQbGFuRW50aXR5KHsNCiAgICAgICAgQXJlYV9JZDogcm93LkFyZWFfSWQNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5EYXRhKSB7DQogICAgICAgICAgdGhpcy5wbGFuRm9ybSA9IHsgLi4ucmVzLkRhdGEgfQ0KICAgICAgICAgIC8vIOWwhuW8gOWni+WSjOe7k+adn+aXpeacn+e7hOWQiOaIkOaXpeacn+iMg+WbtA0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnBsYW5Gb3JtLCAnRGVlcGVuX0RhdGVfUmFuZ2UnLCBbcmVzLkRhdGEuRGVlcGVuX0JlZ2luX0RhdGUgfHwgJycsIHJlcy5EYXRhLkRlZXBlbl9FbmRfRGF0ZSB8fCAnJ10pDQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMucGxhbkZvcm0sICdQdXJjaGFzZV9EYXRlX1JhbmdlJywgW3Jlcy5EYXRhLlB1cmNoYXNlX0JlZ2luX0RhdGUgfHwgJycsIHJlcy5EYXRhLlB1cmNoYXNlX0VuZF9EYXRlIHx8ICcnXSkNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5wbGFuRm9ybSwgJ1Byb2R1Y3RfRGF0ZV9SYW5nZScsIFtyZXMuRGF0YS5Qcm9kdWN0X0JlZ2luX0RhdGUgfHwgJycsIHJlcy5EYXRhLlByb2R1Y3RfRW5kX0RhdGUgfHwgJyddKQ0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnBsYW5Gb3JtLCAnSW5zdGFsbF9EYXRlX1JhbmdlJywgW3Jlcy5EYXRhLkluc3RhbGxfQmVnaW5fRGF0ZSB8fCAnJywgcmVzLkRhdGEuSW5zdGFsbF9FbmRfRGF0ZSB8fCAnJ10pDQogICAgICAgICAgLy8g6YeN5paw6K6h566X5ZCE6Zi25q615bel5pyfDQogICAgICAgICAgdGhpcy5jYWxjdWxhdGVEdXJhdGlvbignRGVlcGVuJykNCiAgICAgICAgICB0aGlzLmNhbGN1bGF0ZUR1cmF0aW9uKCdQdXJjaGFzZScpDQogICAgICAgICAgdGhpcy5jYWxjdWxhdGVEdXJhdGlvbignUHJvZHVjdCcpDQogICAgICAgICAgdGhpcy5jYWxjdWxhdGVEdXJhdGlvbignSW5zdGFsbCcpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzYXZlQXJlYVBsYW4oKSB7DQogICAgICB0aGlzLnNhdmVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgU2F2ZVRvdGFsQ29udHJvbFBsYW5FbnRpdHkoew0KICAgICAgICAuLi50aGlzLnBsYW5Gb3JtLA0KICAgICAgICBBcmVhX0lkOiB0aGlzLmN1cnJlbnRSb3cuQXJlYV9JZCwNCiAgICAgICAgQ29tcGFueV9JZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJyksDQogICAgICAgIFByb2plY3RfSWQ6IHRoaXMuY3VyUHJvamVjdC5TeXNfUHJvamVjdF9JZA0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpDQogICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgIHRoaXMuZ2V0VGFibGVEYXRhKCkNCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLnNhdmVMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVEYXRlUmFuZ2VDaGFuZ2UodHlwZSwgZGF0ZVJhbmdlKSB7DQogICAgICBpZiAoZGF0ZVJhbmdlICYmIGRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMucGxhbkZvcm0sIGAke3R5cGV9X0JlZ2luX0RhdGVgLCBkYXRlUmFuZ2VbMF0pDQogICAgICAgIHRoaXMuJHNldCh0aGlzLnBsYW5Gb3JtLCBgJHt0eXBlfV9FbmRfRGF0ZWAsIGRhdGVSYW5nZVsxXSkNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMucGxhbkZvcm0sIGAke3R5cGV9X0RhdGVfUmFuZ2VgLCBkYXRlUmFuZ2UpDQogICAgICAgIHRoaXMuY2FsY3VsYXRlRHVyYXRpb24odHlwZSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLnBsYW5Gb3JtLCBgJHt0eXBlfV9CZWdpbl9EYXRlYCwgJycpDQogICAgICAgIHRoaXMuJHNldCh0aGlzLnBsYW5Gb3JtLCBgJHt0eXBlfV9FbmRfRGF0ZWAsICcnKQ0KICAgICAgICB0aGlzLiRzZXQodGhpcy5wbGFuRm9ybSwgYCR7dHlwZX1fRGF0ZV9SYW5nZWAsIFtdKQ0KICAgICAgICB0aGlzLiRzZXQodGhpcy5wbGFuRm9ybSwgYCR7dHlwZX1fRHVyYXRpb25gLCAwKQ0KICAgICAgfQ0KICAgICAgLy8g5by65Yi25pu05paw6KeG5Zu+DQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpDQogICAgfSwNCiAgICBjYWxjdWxhdGVEdXJhdGlvbih0eXBlKSB7DQogICAgICBjb25zdCBiZWdpbkRhdGUgPSB0aGlzLnBsYW5Gb3JtW2Ake3R5cGV9X0JlZ2luX0RhdGVgXQ0KICAgICAgY29uc3QgZW5kRGF0ZSA9IHRoaXMucGxhbkZvcm1bYCR7dHlwZX1fRW5kX0RhdGVgXQ0KICAgICAgaWYgKGJlZ2luRGF0ZSAmJiBlbmREYXRlKSB7DQogICAgICAgIGNvbnN0IGJlZ2luID0gbmV3IERhdGUoYmVnaW5EYXRlKQ0KICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZShlbmREYXRlKQ0KICAgICAgICAvLyDorqHnrpflpKnmlbDlt67lubbliqAx77yI5YyF5ZCr5byA5aeL5ZKM57uT5p2f5b2T5aSp77yJDQogICAgICAgIGNvbnN0IGR1cmF0aW9uID0gTWF0aC5mbG9vcigoZW5kIC0gYmVnaW4pIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKSArIDENCiAgICAgICAgdGhpcy4kc2V0KHRoaXMucGxhbkZvcm0sIGAke3R5cGV9X0R1cmF0aW9uYCwgZHVyYXRpb24gPiAwID8gZHVyYXRpb24gOiAwKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMucGxhbkZvcm0sIGAke3R5cGV9X0R1cmF0aW9uYCwgMCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgIHRoaXMucGxhbkZvcm0gPSB7DQogICAgICAgIEFyZWFfSWQ6ICcnLA0KICAgICAgICBEZWVwZW5fQmVnaW5fRGF0ZTogJycsDQogICAgICAgIERlZXBlbl9FbmRfRGF0ZTogJycsDQogICAgICAgIERlZXBlbl9EYXRlX1JhbmdlOiBbXSwNCiAgICAgICAgRGVlcGVuX0R1cmF0aW9uOiAwLA0KICAgICAgICBEZWVwZW5fRW5naW5lZXJfUXVhbnRpdHk6IDAsDQogICAgICAgIFB1cmNoYXNlX0JlZ2luX0RhdGU6ICcnLA0KICAgICAgICBQdXJjaGFzZV9FbmRfRGF0ZTogJycsDQogICAgICAgIFB1cmNoYXNlX0RhdGVfUmFuZ2U6IFtdLA0KICAgICAgICBQdXJjaGFzZV9EdXJhdGlvbjogMCwNCiAgICAgICAgUHVyY2hhc2VfRW5naW5lZXJfUXVhbnRpdHk6IDAsDQogICAgICAgIFByb2R1Y3RfQmVnaW5fRGF0ZTogJycsDQogICAgICAgIFByb2R1Y3RfRW5kX0RhdGU6ICcnLA0KICAgICAgICBQcm9kdWN0X0RhdGVfUmFuZ2U6IFtdLA0KICAgICAgICBQcm9kdWN0X0R1cmF0aW9uOiAwLA0KICAgICAgICBQcm9kdWN0X0VuZ2luZWVyX1F1YW50aXR5OiAwLA0KICAgICAgICBJbnN0YWxsX0JlZ2luX0RhdGU6ICcnLA0KICAgICAgICBJbnN0YWxsX0VuZF9EYXRlOiAnJywNCiAgICAgICAgSW5zdGFsbF9EYXRlX1JhbmdlOiBbXSwNCiAgICAgICAgSW5zdGFsbF9EdXJhdGlvbjogMCwNCiAgICAgICAgSW5zdGFsbF9FbmdpbmVlcl9RdWFudGl0eTogMA0KICAgICAgfQ0KICAgIH0sDQogICAgdXBsb2FkU3VjY2VzcyhyZXMpIHsNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a+85YWl5oiQ5YqfJykNCiAgICAgICAgdGhpcy5nZXRUYWJsZURhdGEoKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSkNCiAgICAgICAgaWYgKHJlcy5EYXRhKSB7DQogICAgICAgICAgd2luZG93Lm9wZW4oY29tYmluZVVSTCh0aGlzLiRiYXNlVXJsLCByZXMuRGF0YSksICdfYmxhbmsnKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBleHBvcnRGaWxlKCkgew0KICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gdHJ1ZQ0KICAgICAgRXhwb3J0VG90YWxDb250cm9sUGxhbih7DQogICAgICAgIENvbXBhbnlJZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJyksDQogICAgICAgIFByb2plY3RJZDogdGhpcy5jdXJQcm9qZWN0LlN5c19Qcm9qZWN0X0lkLA0KICAgICAgICBGaWx0ZXJUeXBlczogdGhpcy5xdWVyeU1vZGVsLlJhbmdlLm1hcChpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gWydEZWVwZW4nLCAnUHVyY2hhc2UnLCAnUHJvZHVjdCcsICdJbnN0YWxsJ10uaW5kZXhPZihpdGVtKSArIDENCiAgICAgICAgfSksDQogICAgICAgIEFyZWFJZHM6IHRoaXMucXVlcnlNb2RlbC5BcmVhcw0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHdpbmRvdy5vcGVuKGNvbWJpbmVVUkwodGhpcy4kYmFzZVVybCwgcmVzLkRhdGEpLCAnX2JsYW5rJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQ0KICAgICAgICB9DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICB1cGRhdGVEYXRhKCkgew0KICAgICAgdGhpcy51cGRhdGVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgR2VuZXJhdGVQcm9qZWN0UGxhbkJ1c2luZXNzRGF0YSh7DQogICAgICAgIHRlbmFudElkczogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3RlbmFudCcpDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmm7TmlrDmiJDlip8nKQ0KICAgICAgICAgIHRoaXMuZ2V0VGFibGVEYXRhKCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQ0KICAgICAgICB9DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy51cGRhdGVMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICB0b0RldGFpbChpdGVtKSB7DQogICAgICBpZiAoaXNSb3V0ZU5hbWVFeGlzdHMoaXRlbS5yb3V0ZSkpIHsNCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgIG5hbWU6IGl0ZW0ucm91dGUNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+W9k+WJjei0puaIt+aXoOiuv+mXruadg+mZkCcpDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["OverallControlPlanContent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2QA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OverallControlPlanContent.vue", "sourceRoot": "src/views/plan/components", "sourcesContent": ["<template>\r\n  <el-card class=\"card\">\r\n    <div class=\"content\">\r\n      <div class=\"header\">\r\n        <span class=\"project-name\">{{ curProject && curProject.Short_Name }}</span>\r\n        <span>\r\n          <i class=\"el-icon-time\" />\r\n          <span class=\"label\">总工期</span>\r\n          <span class=\"value\">{{ totalProjectDuration }}天</span>\r\n        </span>\r\n        <span>\r\n          <i class=\"el-icon-time\" />\r\n          <span class=\"label\">剩余工期</span>\r\n          <span class=\"value\">{{ remainingDuration }}天</span>\r\n        </span>\r\n      </div>\r\n      <div class=\"summary\">\r\n        <div v-for=\"(item) in nodeList\" :key=\"item.Id\" class=\"block\" @click=\"toDetail(item)\">\r\n          <div class=\"top\" :style=\"{background: `linear-gradient( 135deg, ${item.color}33 0%, #D6EAFF33 100%)`}\">\r\n            <div class=\"block-name-wrap\">\r\n              <img :src=\"item.icon\" class=\"icon\">\r\n              <div class=\"block-name\">{{ item.name }}</div>\r\n            </div>\r\n            <div style=\"flex:1;height: 100%;display: flex;flex-direction: column;justify-content: center;margin-top: 8px\">\r\n              <div class=\"progress-container\">\r\n                <div class=\"progress-bar\">\r\n                  <div class=\"progress-bg\">\r\n                    <div\r\n                      class=\"progress-fill\"\r\n                      :style=\"{\r\n                        width: parseFloat(item.finishPercent) > 100 ? '100%' : item.finishPercent,\r\n                        backgroundColor: item.color\r\n                      }\"\r\n                    >\r\n                      <div\r\n                        class=\"progress-dot\"\r\n                        :style=\"{\r\n                          backgroundColor: item.color\r\n                        }\"\r\n                      />\r\n                    </div>\r\n                    <div\r\n                      class=\"progress-data\"\r\n                      :style=\"{\r\n                        left: parseFloat(item.finishPercent) > 100 ? '100%' : item.finishPercent,\r\n                        color: item.color,\r\n                        transform: parseFloat(item.finishPercent) > 80 ? 'translateX(-100%)' : 'translateX(0%)'\r\n                      }\"\r\n                    >\r\n                      <span class=\"finish-value\">完成:{{ item.finishSummary }}t</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"plan-percent\">\r\n                <div class=\"plan\">\r\n                  计划：{{ item.planSummary }}t\r\n                </div>\r\n                <div :style=\"{color:item.color,fontWeight:'bold'}\">\r\n                  {{ item.finishPercent }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"bottom\">\r\n            <span>总：{{ item.duration }}</span>\r\n            <span style=\"margin-left: 14px\"> <i class=\"el-icon-date\" /> {{ item.beginDate }} - {{ item.endDate }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <el-form ref=\"form\" inline style=\"display: flex;align-items: center\">\r\n        <el-form-item>\r\n          <el-upload\r\n            :action=\"$baseUrl + 'PRO/ControlPlan/ImportTotalPlan'\"\r\n            :show-file-list=\"false\"\r\n            :on-success=\"uploadSuccess\"\r\n            :headers=\"headers\"\r\n            :data=\"uploadData\"\r\n          >\r\n            <el-button type=\"primary\">导入总控计划</el-button>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" :loading=\"exportLoading\" @click=\"exportFile()\">导出总控计划</el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"margin-right: auto\">\r\n          <el-button type=\"primary\" :loading=\"updateLoading\" @click=\"updateData()\">手动更新计划</el-button>\r\n        </el-form-item>\r\n        <el-form-item label=\"区域\">\r\n          <SelectArea v-model=\"queryModel.Areas\" :project-id=\"curProject.Sys_Project_Id\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"显示范围\">\r\n          <el-select v-model=\"queryModel.Range\" multiple clearable collapse-tags>\r\n            <el-option v-for=\"item in nodeList\" :key=\"item.code\" :label=\"`过滤${item.name}完成`\" :value=\"item.code\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"search\">搜索</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <bt-table\r\n        v-if=\"nodeList && nodeList.length\"\r\n        class=\"bt-table\"\r\n        :config=\"config\"\r\n        :header-cell-style=\"cellStyle\"\r\n      >\r\n        <template #actions=\"{row}\">\r\n          <el-button type=\"text\" @click=\"openDialog(row)\">编辑</el-button>\r\n        </template>\r\n      </bt-table>\r\n    </div>\r\n\r\n    <!-- 编辑计划弹窗 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"编辑计划\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"1200px\"\r\n      :before-close=\"handleClose\"\r\n    >\r\n      <el-form ref=\"planForm\" :model=\"planForm\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"单体/分区\">\r\n              <span>{{ currentRow.FullAreaName }}</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 深化设计 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[0] && nodeList[0].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Deepen_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Deepen', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Deepen_Engineer_Quantity\" type=\"number\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Deepen_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 材料采购 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[1] && nodeList[1].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Purchase_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Purchase', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Purchase_Engineer_Quantity\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Purchase_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 生产加工 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[2] && nodeList[2].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Product_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Product', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Product_Engineer_Quantity\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Product_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 施工安装 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[3] && nodeList[3].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Install_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Install', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Install_Engineer_Quantity\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Install_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" :loading=\"saveLoading\" @click=\"saveAreaPlan\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  ExportTotalControlPlan, GenerateProjectPlanBusinessData,\r\n  GetConfigs,\r\n  GetMonomerListByProjectId,\r\n  GetTotalControlPlanEntity,\r\n  GetTotalControlPlanList,\r\n  SaveTotalControlPlanEntity\r\n} from '@/api/plm/projects'\r\nimport SelectArea from '@/components/Select/SelectArea/index.vue'\r\nimport moment from 'moment'\r\nimport { combineURL } from '@/utils'\r\nimport { getToken } from '@/utils/auth'\r\nimport { isRouteNameExists } from '@/utils/router'\r\n\r\nexport default {\r\n  name: 'OverallControlPlanContent',\r\n  components: { SelectArea },\r\n  props: {\r\n    curProject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      queryModel: {\r\n        Areas: [],\r\n        Range: []\r\n      },\r\n      // 总工期和剩余工期\r\n      totalProjectDuration: 0,\r\n      remainingDuration: 0,\r\n      config: {\r\n        pageSizeOptions: [10, 20, 50, 100],\r\n        pageSize: 20,\r\n        total: 0,\r\n        loading: false,\r\n        currentPage: 1,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        keyField: 'Area_Id'\r\n      },\r\n      monomerList: [],\r\n      nodeList: [],\r\n      // 弹窗相关数据\r\n      dialogVisible: false,\r\n      currentRow: {},\r\n      planForm: {\r\n        Area_Id: '',\r\n        Deepen_Begin_Date: '',\r\n        Deepen_End_Date: '',\r\n        Deepen_Date_Range: [],\r\n        Deepen_Duration: 0,\r\n        Deepen_Engineer_Quantity: 0,\r\n        Purchase_Begin_Date: '',\r\n        Purchase_End_Date: '',\r\n        Purchase_Date_Range: [],\r\n        Purchase_Duration: 0,\r\n        Purchase_Engineer_Quantity: 0,\r\n        Product_Begin_Date: '',\r\n        Product_End_Date: '',\r\n        Product_Date_Range: [],\r\n        Product_Duration: 0,\r\n        Product_Engineer_Quantity: 0,\r\n        Install_Begin_Date: '',\r\n        Install_End_Date: '',\r\n        Install_Date_Range: [],\r\n        Install_Duration: 0,\r\n        Install_Engineer_Quantity: 0\r\n      },\r\n      cloneTableData: [],\r\n      exportLoading: false,\r\n      updateLoading: false,\r\n      saveLoading: false,\r\n      headers: {\r\n        Authorization: getToken(),\r\n        Last_Working_Object_Id: localStorage.getItem('Last_Working_Object_Id')\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    uploadData() {\r\n      return { data: JSON.stringify({ ProjectId: this.curProject?.Sys_Project_Id, CompanyId: localStorage.getItem('CurReferenceId') }) }\r\n    }\r\n  },\r\n  watch: {\r\n    curProject: {\r\n      deep: true,\r\n      handler: function() {\r\n        if (this.curProject && this.curProject.Sys_Project_Id) {\r\n          this.queryModel.Areas = []\r\n          this.getMonomerList()\r\n          this.getTableData()\r\n        }\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getConfig()\r\n    if (this.curProject && this.curProject.Sys_Project_Id) {\r\n      this.getMonomerList()\r\n      this.getTableData()\r\n    }\r\n  },\r\n  methods: {\r\n    cellStyle({ column }) {\r\n      const style = {}\r\n      if (column.field && column.field.startsWith('Deepen')) {\r\n        style.backgroundColor = `rgba(41, 141, 255, 0.1)!important`\r\n      }\r\n      if (column.field && column.field.startsWith('Purchase')) {\r\n        style.backgroundColor = `rgba(189, 109, 246, 0.1)!important`\r\n      }\r\n      if (column.field && column.field.startsWith('Product')) {\r\n        style.backgroundColor = `rgba(0, 195, 97, 0.1)!important`\r\n      }\r\n      if (column.field && column.field.startsWith('Install')) {\r\n        style.backgroundColor = `rgba(0, 177, 191, 0.1)!important`\r\n      }\r\n      return style\r\n    },\r\n    async getConfig() {\r\n      const res = await GetConfigs({\r\n        CompanyId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      this.nodeList = res.Data.map(item => {\r\n        return {\r\n          ...item,\r\n          name: item.Plan_Name,\r\n          code: ['Deepen', 'Purchase', 'Product', 'Install'][item.Plan_Type - 1],\r\n          icon: require(`@/assets/PLM/plan_summary_${item.Plan_Type}.png`),\r\n          color: ['#298DFF', '#BD6DF6', '#00C361', '#00B1BF'][item.Plan_Type - 1],\r\n          route: ['PRODeepenPlanTrackCom', 'PROPurchasePlanTrackCom', 'PROProducePlanTrackCom', 'PROConstructionPlanTrackCom'][item.Plan_Type - 1],\r\n          visible: true\r\n        }\r\n      })\r\n      this.getRenderColumns()\r\n    },\r\n    getRenderColumns() {\r\n      const columns = this.nodeList.filter(i => i.visible).map(item => {\r\n        return {\r\n          width: 150,\r\n          label: item.name,\r\n          otherOptions: { align: 'center', fixed: '' },\r\n          key: item.code,\r\n          children: [\r\n            { key: item.code + '_Begin_Date', width: '160',\r\n              label: '开始时间', otherOptions: { align: 'center' },\r\n              render: (row) => {\r\n                if (!row[item.code + '_Begin_Date']) return '-'\r\n                const value = moment(row[item.code + '_Begin_Date']).format('YYYY-MM-DD')\r\n                return this.$createElement('div', {}, value)\r\n              }\r\n            },\r\n            { key: item.code + '_End_Date', width: '160',\r\n              label: '结束时间', otherOptions: { align: 'center' },\r\n              render: (row) => {\r\n                if (!row[item.code + '_End_Date']) return '-'\r\n                const value = moment(row[item.code + '_End_Date']).format('YYYY-MM-DD')\r\n                return this.$createElement('div', { }, value)\r\n              }\r\n            },\r\n            { key: item.code + '_Duration', width: '160',\r\n              label: '总工期(天)', otherOptions: { align: 'center' }\r\n            },\r\n            { key: item.code + '_Engineer_Quantity', width: '160',\r\n              label: '计划工程量(t)', otherOptions: { align: 'center' }\r\n            },\r\n            { key: item.code + '_Finish_Quantity', width: '160',\r\n              label: '实际量(t)', otherOptions: { align: 'center' }\r\n            },\r\n            { key: item.code + '_Finish_State', width: '160',\r\n              label: '完成状态', otherOptions: { align: 'center' },\r\n              render: (row) => {\r\n                const value = row[item.code + '_Finish_State']\r\n                return this.$createElement('div', {\r\n                  style: {\r\n                    color: value === '已完成' ? '#67C23A' : value === '未完成' ? '#F56C6C' : ''\r\n                  }\r\n                }, value)\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      })\r\n      this.config.tableColumns = [{\r\n        width: 150, label: '单体/分区', key: 'FullAreaName', otherOptions: { align: 'left', fixed: 'left' }\r\n      }, ...columns]\r\n    },\r\n    search() {\r\n      // 过滤渲染列\r\n      this.nodeList.forEach(i => i.visible = true)\r\n      if (this.queryModel.Range && this.queryModel.Range.length > 0) {\r\n        this.queryModel.Range.forEach(i => {\r\n          this.nodeList.forEach(j => {\r\n            if (i === j.code) {\r\n              j.visible = false\r\n            }\r\n          })\r\n        })\r\n      }\r\n      this.getRenderColumns()\r\n      // 过滤区域\r\n      if (this.queryModel.Areas && this.queryModel.Areas.length > 0) {\r\n        this.config.tableData = this.cloneTableData.filter(item => {\r\n          return this.queryModel.Areas.includes(item.Area_Id)\r\n        })\r\n      } else {\r\n        this.config.tableData = JSON.parse(JSON.stringify(this.cloneTableData))\r\n      }\r\n      this.handleTableData()\r\n    },\r\n    getTableData() {\r\n      if (!this.curProject || !this.curProject.Sys_Project_Id) return\r\n      this.config.loading = true\r\n      GetTotalControlPlanList({\r\n        ProjectId: this.curProject.Sys_Project_Id,\r\n        CompanyId: localStorage.getItem('CurReferenceId')\r\n      }).then(res => {\r\n        this.config.tableData = res.Data.map(item => {\r\n          item.id = item.Area_Id\r\n          return item\r\n        })\r\n        this.cloneTableData = JSON.parse(JSON.stringify(res.Data))\r\n        this.handleTableData()\r\n      }).finally(() => {\r\n        this.config.loading = false\r\n      })\r\n    },\r\n    handleTableData() {\r\n      // 收集所有日期用于计算总工期和剩余工期\r\n      const allBeginDates = []\r\n      const allEndDates = []\r\n      this.totalProjectDuration = 0\r\n      this.remainingDuration = 0\r\n      this.nodeList.forEach(item => {\r\n        const beginDates = this.config.tableData\r\n          .map(row => row[item.code + '_Begin_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n        const endDates = this.config.tableData\r\n          .map(row => row[item.code + '_End_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n\r\n        allBeginDates.push(...beginDates)\r\n        allEndDates.push(...endDates)\r\n      })\r\n      // 计算项目总工期：最晚日期 - 最早日期 + 1\r\n      if (allBeginDates.length > 0 && allEndDates.length > 0) {\r\n        const earliestDate = allBeginDates.sort()[0]\r\n        const latestDate = allEndDates.sort((a, b) => new Date(b) - new Date(a))[0]\r\n\r\n        if (earliestDate && latestDate) {\r\n          const begin = new Date(earliestDate)\r\n          const end = new Date(latestDate)\r\n          this.totalProjectDuration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1\r\n          // 计算剩余工期：最晚日期 - 当前日期 + 1\r\n          const today = new Date()\r\n          const remainingDays = Math.floor((end - today) / (1000 * 60 * 60 * 24)) + 1\r\n          this.remainingDuration = remainingDays > 0 ? remainingDays : 0\r\n        }\r\n      }\r\n\r\n      this.nodeList = this.nodeList.map(item => {\r\n        // 计算计划工程量汇总\r\n        const planSummary = this.config.tableData.reduce((sum, row) => {\r\n          const value = parseFloat(row[item.code + '_Engineer_Quantity']) || 0\r\n          return sum + value\r\n        }, 0)\r\n\r\n        // 计算实际完成量汇总\r\n        const finishSummary = this.config.tableData.reduce((sum, row) => {\r\n          const value = parseFloat(row[item.code + '_Finish_Quantity']) || 0\r\n          return sum + value\r\n        }, 0)\r\n\r\n        // 计算完成百分比\r\n        const finishPercent = planSummary > 0 ? (finishSummary / planSummary * 100) : 0\r\n\r\n        // 获取最早开始日期（排除空值）\r\n        const beginDates = this.config.tableData\r\n          .map(row => row[item.code + '_Begin_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n          .sort()\r\n        const beginDate = beginDates.length > 0 ? moment(beginDates[0]).format('YYYY/MM/DD') : ''\r\n\r\n        // 获取最晚结束日期（排除空值）\r\n        const endDates = this.config.tableData\r\n          .map(row => row[item.code + '_End_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n          .sort((a, b) => new Date(b) - new Date(a))\r\n        const endDate = endDates.length > 0 ? moment(endDates[0]).format('YYYY/MM/DD') : ''\r\n\r\n        // 计算总工期\r\n        let duration = 0\r\n        if (beginDate && endDate) {\r\n          const begin = new Date(beginDate)\r\n          const end = new Date(endDate)\r\n          duration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1\r\n        }\r\n\r\n        return {\r\n          ...item,\r\n          planSummary: planSummary.toFixed(2),\r\n          finishSummary: finishSummary.toFixed(2),\r\n          finishPercent: finishPercent.toFixed(2) + '%',\r\n          beginDate: beginDate,\r\n          endDate: endDate,\r\n          duration: duration > 0 ? duration + '天' : ''\r\n        }\r\n      })\r\n    },\r\n    async getMonomerList() {\r\n      if (!this.curProject || !this.curProject.Sys_Project_Id) return\r\n      const res = await GetMonomerListByProjectId({\r\n        projectId: this.curProject.Sys_Project_Id\r\n      })\r\n      this.monomerList = res.Data\r\n    },\r\n    openDialog(row) {\r\n      this.currentRow = row\r\n      this.dialogVisible = true\r\n      GetTotalControlPlanEntity({\r\n        Area_Id: row.Area_Id\r\n      }).then(res => {\r\n        if (res.Data) {\r\n          this.planForm = { ...res.Data }\r\n          // 将开始和结束日期组合成日期范围\r\n          this.$set(this.planForm, 'Deepen_Date_Range', [res.Data.Deepen_Begin_Date || '', res.Data.Deepen_End_Date || ''])\r\n          this.$set(this.planForm, 'Purchase_Date_Range', [res.Data.Purchase_Begin_Date || '', res.Data.Purchase_End_Date || ''])\r\n          this.$set(this.planForm, 'Product_Date_Range', [res.Data.Product_Begin_Date || '', res.Data.Product_End_Date || ''])\r\n          this.$set(this.planForm, 'Install_Date_Range', [res.Data.Install_Begin_Date || '', res.Data.Install_End_Date || ''])\r\n          // 重新计算各阶段工期\r\n          this.calculateDuration('Deepen')\r\n          this.calculateDuration('Purchase')\r\n          this.calculateDuration('Product')\r\n          this.calculateDuration('Install')\r\n        }\r\n      })\r\n    },\r\n    saveAreaPlan() {\r\n      this.saveLoading = true\r\n      SaveTotalControlPlanEntity({\r\n        ...this.planForm,\r\n        Area_Id: this.currentRow.Area_Id,\r\n        Company_Id: localStorage.getItem('CurReferenceId'),\r\n        Project_Id: this.curProject.Sys_Project_Id\r\n      }).then(res => {\r\n        this.$message.success('保存成功')\r\n        this.dialogVisible = false\r\n        this.getTableData()\r\n      }).finally(() => {\r\n        this.saveLoading = false\r\n      })\r\n    },\r\n    handleDateRangeChange(type, dateRange) {\r\n      if (dateRange && dateRange.length === 2) {\r\n        this.$set(this.planForm, `${type}_Begin_Date`, dateRange[0])\r\n        this.$set(this.planForm, `${type}_End_Date`, dateRange[1])\r\n        this.$set(this.planForm, `${type}_Date_Range`, dateRange)\r\n        this.calculateDuration(type)\r\n      } else {\r\n        this.$set(this.planForm, `${type}_Begin_Date`, '')\r\n        this.$set(this.planForm, `${type}_End_Date`, '')\r\n        this.$set(this.planForm, `${type}_Date_Range`, [])\r\n        this.$set(this.planForm, `${type}_Duration`, 0)\r\n      }\r\n      // 强制更新视图\r\n      this.$forceUpdate()\r\n    },\r\n    calculateDuration(type) {\r\n      const beginDate = this.planForm[`${type}_Begin_Date`]\r\n      const endDate = this.planForm[`${type}_End_Date`]\r\n      if (beginDate && endDate) {\r\n        const begin = new Date(beginDate)\r\n        const end = new Date(endDate)\r\n        // 计算天数差并加1（包含开始和结束当天）\r\n        const duration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1\r\n        this.$set(this.planForm, `${type}_Duration`, duration > 0 ? duration : 0)\r\n      } else {\r\n        this.$set(this.planForm, `${type}_Duration`, 0)\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.planForm = {\r\n        Area_Id: '',\r\n        Deepen_Begin_Date: '',\r\n        Deepen_End_Date: '',\r\n        Deepen_Date_Range: [],\r\n        Deepen_Duration: 0,\r\n        Deepen_Engineer_Quantity: 0,\r\n        Purchase_Begin_Date: '',\r\n        Purchase_End_Date: '',\r\n        Purchase_Date_Range: [],\r\n        Purchase_Duration: 0,\r\n        Purchase_Engineer_Quantity: 0,\r\n        Product_Begin_Date: '',\r\n        Product_End_Date: '',\r\n        Product_Date_Range: [],\r\n        Product_Duration: 0,\r\n        Product_Engineer_Quantity: 0,\r\n        Install_Begin_Date: '',\r\n        Install_End_Date: '',\r\n        Install_Date_Range: [],\r\n        Install_Duration: 0,\r\n        Install_Engineer_Quantity: 0\r\n      }\r\n    },\r\n    uploadSuccess(res) {\r\n      if (res.IsSucceed) {\r\n        this.$message.success('导入成功')\r\n        this.getTableData()\r\n      } else {\r\n        this.$message.error(res.Message)\r\n        if (res.Data) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        }\r\n      }\r\n    },\r\n    exportFile() {\r\n      this.exportLoading = true\r\n      ExportTotalControlPlan({\r\n        CompanyId: localStorage.getItem('CurReferenceId'),\r\n        ProjectId: this.curProject.Sys_Project_Id,\r\n        FilterTypes: this.queryModel.Range.map(item => {\r\n          return ['Deepen', 'Purchase', 'Product', 'Install'].indexOf(item) + 1\r\n        }),\r\n        AreaIds: this.queryModel.Areas\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.exportLoading = false\r\n      })\r\n    },\r\n    updateData() {\r\n      this.updateLoading = true\r\n      GenerateProjectPlanBusinessData({\r\n        tenantIds: localStorage.getItem('tenant')\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('更新成功')\r\n          this.getTableData()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.updateLoading = false\r\n      })\r\n    },\r\n    toDetail(item) {\r\n      if (isRouteNameExists(item.route)) {\r\n        this.$router.push({\r\n          name: item.route\r\n        })\r\n      } else {\r\n        this.$message.error('当前账户无访问权限')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.card{\r\n  flex:1;\r\n  height: 100%;\r\n  margin-left: 16px;\r\n  .content{\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100%;\r\n  }\r\n  .bt-table{\r\n    flex:1;\r\n  }\r\n}\r\n.header{\r\n  display: flex;\r\n  align-items: flex-end;\r\n  .project-name{\r\n    font-size: 16px;\r\n    color: #333333;\r\n    font-weight: bold;\r\n    margin-right: 8px;\r\n  }\r\n  .el-icon-time{\r\n    margin-left: 8px;\r\n    margin-right: 4px;\r\n    font-size: 14px;\r\n  }\r\n  .label{\r\n    color: #333333;\r\n    font-size: 12px;\r\n  }\r\n  .value{\r\n    font-size: 14px;\r\n    color: #333333;\r\n    font-weight: bold;\r\n    margin-left: 7px;\r\n  }\r\n}\r\n\r\n.phase-label {\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 40px;\r\n  text-align: center;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.el-row {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n.summary{\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  column-gap: 12px;\r\n  margin: 12px 0;\r\n  .block{\r\n    height: 105px;\r\n    background: #FFFFFF;\r\n    border-radius: 4px 4px 4px 4px;\r\n    border: 1px solid #E2E4E9;\r\n    flex: 1;\r\n    cursor: pointer;\r\n    .icon{\r\n      width: 48px;\r\n      height: 48px;\r\n    }\r\n    .finish{\r\n      font-weight: bold;\r\n      font-size: 12px;\r\n      text-align: center;\r\n    }\r\n    .progress-container {\r\n       margin-bottom: 8px;\r\n     }\r\n    .progress-bar {\r\n       width: 100%;\r\n       height: 2px;\r\n     }\r\n     .progress-bg {\r\n       width: 100%;\r\n       height: 100%;\r\n       background-color: #f0f0f0;\r\n       border-radius: 1px;\r\n       overflow: visible;\r\n       position: relative;\r\n     }\r\n     .progress-fill {\r\n       height: 100%;\r\n       border-radius: 1px;\r\n       transition: width 0.3s ease;\r\n       min-width: 2px;\r\n       position: relative;\r\n     }\r\n     .progress-dot {\r\n        position: absolute;\r\n        right: -3px;\r\n        top: -2px;\r\n        width: 6px;\r\n        height: 6px;\r\n        border-radius: 50%;\r\n        transition: all 0.3s ease;\r\n      }\r\n      .progress-data {\r\n        position: absolute;\r\n        top: -20px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        white-space: nowrap;\r\n        transition: all 0.3s ease;\r\n        pointer-events: none;\r\n      }\r\n    .top{\r\n      height: 76px;\r\n      display: flex;\r\n      padding: 0 15px 0 8px;\r\n      .block-name-wrap{\r\n        width: 72px;\r\n        text-align: center;\r\n        margin-right: 8px;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n      .block-name{\r\n        color: #666666;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        position: relative;\r\n        top: -6px;\r\n        // 超出省略号\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n      .plan-percent{\r\n        font-size: 14px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n      }\r\n      .plan{\r\n        color: #666666;\r\n      }\r\n    }\r\n    .bottom{\r\n      height: 29px;\r\n      line-height: 29px;\r\n      color: #666666;\r\n      font-size: 14px;\r\n      padding: 0 12px;\r\n    }\r\n  }\r\n}\r\n</style>"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue?vue&type=style&index=0&id=4d274f86&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue", "mtime": 1757473454985}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCkBpbXBvcnQgIn5AL3N0eWxlcy9taXhpbi5zY3NzIjsKLmZvcm0tcmVjb2duaXRpb24td3JhcHBlciB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIG92ZXJmbG93OiBoaWRkZW47CiAgbWF4LWhlaWdodDogNzB2aDsKICAuZm9ybS1yZWNvZ25pdGlvbi10YWJzIHsKCiAgfQp9Cg=="}, {"version": 3, "sources": ["RecognitionConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "RecognitionConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-recognition-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\">\n        <el-tab-pane v-for=\"(item, index) in bomLevel.list\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div>\n      <comp-recognition-config v-if=\"bomActiveName === '-1'\" @close=\"handleClose\" />\n      <part-recognition-config v-else-if=\"bomActiveName === '0'\" @close=\"handleClose\" />\n      <unit-part-recognition-config v-else :level=\"number(bomActiveName)\" @close=\"handleClose\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport compRecognitionConfig from './compRecognitionConfig'\nimport partRecognitionConfig from './partRecognitionConfig'\nimport unitPartRecognitionConfig from './unitPartRecognitionConfig'\nimport { number } from 'echarts'\n\nexport default {\n  components: {\n    compRecognitionConfig,\n    partRecognitionConfig,\n    unitPartRecognitionConfig\n  },\n  data() {\n    return {\n      bomLevel: [],\n      bomActiveName: '',\n      btnLoading: false\n    }\n  },\n  async mounted() {\n    this.bomLevel = await GetBOMInfo()\n    this.bomActiveName = this.bomLevel.list[0].Code\n    console.log('this.bomLevel', this.bomLevel)\n  },\n  methods: {\n    handleClose() {\n      this.$emit('close')\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-recognition-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  max-height: 70vh;\n  .form-recognition-tabs {\n\n  }\n}\n</style>\n"]}]}
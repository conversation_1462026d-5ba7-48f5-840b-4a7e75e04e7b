{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory-attribute\\index.vue?vue&type=style&index=0&id=6aa3162e&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory-attribute\\index.vue", "mtime": 1757468112116}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCmhlYWRlciB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGZvbnQtc2l6ZTogMjBweDsNCn0NCg0KLmNzLXotcGFnZS1tYWluLWNvbnRlbnR7DQogIHBhZGRpbmctYm90dG9tOiA2MnB4Ow0KfQ0KDQoubWFuYWdlLWN5Y2xlLWlucHV0ew0KICA6OnYtZGVlcHsNCiAgICAuZWwtc2VsZWN0IC5lbC1pbnB1dCB7DQogICAgICB3aWR0aDogMTIwcHggIWltcG9ydGFudDsNCiAgICB9DQogIH0NCn0NCg0KLmJhc2ljLWluZm9ybWF0aW9uIHsNCiAgOjp2LWRlZXAgew0KICAgIC5lbC1mb3JtLWl0ZW1fX2NvbnRlbnQgew0KICAgICAgbWluLXdpZHRoOiAyNTBweDsNCiAgICAgIG1hcmdpbi1yaWdodDogMzBweDsNCiAgICAgIC5jcy1pbnB1dHsNCg0KICAgICAgICAuZWwtaW5wdXQgew0KICAgICAgICAgIHdpZHRoOiB1bnNldDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLmVsLWlucHV0IHsNCiAgICAgICAgd2lkdGg6IDI1MHB4Ow0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5lbC1zd2l0Y2guaXMtZGlzYWJsZWQgLmVsLXN3aXRjaF9fY29yZSwNCiAgICAuZWwtc3dpdGNoLmlzLWRpc2FibGVkIC5lbC1zd2l0Y2hfX2xhYmVsIHsNCiAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICB9DQogICAgLmlzLXdvcmtzaG9wIHsNCiAgICAgIC5lbC1zd2l0Y2guaXMtZGlzYWJsZWQgLmVsLXN3aXRjaF9fY29yZSwNCiAgICAgIC5lbC1zd2l0Y2guaXMtZGlzYWJsZWQgLmVsLXN3aXRjaF9fbGFiZWwgew0KICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkOw0KICAgICAgfQ0KICAgIH0NCiAgICAuZWwtc3dpdGNoLmlzLWRpc2FibGVkIHsNCiAgICAgIG9wYWNpdHk6IDE7DQogICAgICAuZWwtc3dpdGNoLmlzLWNoZWNrZWQgLmVsLXN3aXRjaF9fY29yZSB7DQogICAgICAgIGJvcmRlci1jb2xvcjogIzI5OGRmZjsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzI5OGRmZjsNCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLm1hbmFnZS1jeWNsZXsNCiAgICBkaXNwbGF5OiBibG9jazsNCiAgICAudGV4dCB7DQogICAgICBtYXJnaW46IDEycHggMDsNCiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICB9DQogICAgOjp2LWRlZXAgew0KICAgICAgLm1hbmFnZS1jeWNsZS1zZWxlY3Qgew0KICAgICAgICAuZWwtaW5wdXR7DQogICAgICAgICAgd2lkdGg6IDgwcHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLmZhY3RvcnktaW1nIHsNCiAgICAvL2Rpc3BsYXk6IGJsb2NrOw0KICB9DQoNCiAgLmltZy1pdGVtLWljb24gew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICB0b3A6IDA7DQogICAgcmlnaHQ6IDA7DQogICAgY29sb3I6ICMzMzM7DQogICAgZm9udC1zaXplOiAyMHB4Ow0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgfQ0KfQ0KDQoueWVhci1iYXRjaC1wcm9kdWN0aW9uIHsNCiAgLnJhZGlvLWJveCB7DQogICAgLmVsLXJhZGlvLWdyb3VwIHsNCiAgICAgIHBhZGRpbmctbGVmdDogMzBweDsNCiAgICB9DQogICAgLmVsLXJhZGlvLWJ1dHRvbiB7DQogICAgICBmb250LXNpemU6IDIwcHg7DQogICAgfQ0KICAgIDo6di1kZWVwIC5lbC1pbnB1dC0tc21hbGwgLmVsLWlucHV0X19pbm5lciB7DQogICAgICBoZWlnaHQ6IDMwcHg7DQogICAgICBsaW5lLWhlaWdodDogMzBweDsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDAgNHB4IDRweCAwOw0KICAgICAgYm9yZGVyLWxlZnQ6IG5vbmU7DQogICAgfQ0KICAgIDo6di1kZWVwIC5lbC1pY29uLWNpcmNsZS1jbG9zZSB7DQogICAgICBjb2xvcjogI2QwZDNkYjsNCiAgICB9DQogIH0NCg0KICBwIHsNCiAgICBwYWRkaW5nLWxlZnQ6IDMwcHg7DQogIH0NCn0NCi5zdWJtaXQtYnRuIHsNCiAgcGFkZGluZzogMTZweCAwIDE2cHggMzJweDsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB6LWluZGV4OiA5OTsNCiAgYm90dG9tOiAxNnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICB3aWR0aDogY2FsYygxMDAlIC0gNDhweCk7DQp9DQoNCjo6di1kZWVwIC5pbnB1dC1udW1iZXJ7DQogICAgICBpbnB1dHsNCiAgICAgICAgcGFkZGluZy1yaWdodDogMnB4Ow0KICAgICAgfQ0KICAgIH0NCg0KLm1sLTh7DQogIG1hcmdpbjogMCA4cHg7DQp9DQoudzgwew0KICA6OnYtZGVlcCAuZWwtaW5wdXR7DQogICAgd2lkdGg6IDgwJSAhaW1wb3J0YW50Ow0KDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs8BA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/basic-information/factory-attribute", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <div class=\"fff cs-z-tb-wrapper\">\r\n        <div class=\"basic-information\">\r\n          <header>基本信息</header>\r\n          <el-form\r\n            ref=\"form\"\r\n            :inline=\"true\"\r\n            :model=\"form\"\r\n            class=\"demo-form-inline\"\r\n            :rules=\"rules\"\r\n            style=\"padding-left: 20px\"\r\n            label-width=\"140px\"\r\n          >\r\n            <el-form-item label=\"工厂名称\" prop=\"Short_Name\">\r\n              <el-input\r\n                v-model=\"form.Short_Name\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"工厂全称\" prop=\"Name\">\r\n              <el-input v-model=\"form.Name\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"厂长\" prop=\"Manager\">\r\n              <el-autocomplete\r\n                ref=\"autocomplete\"\r\n                v-model=\"form.Manager\"\r\n                :fetch-suggestions=\"querySearchAsync\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n                @clear=\"clear\"\r\n                @select=\"handleSelect\"\r\n                @blur=\"blur\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"工厂位置\" prop=\"Address\">\r\n              <el-input v-model=\"form.Address\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"工厂专业类别\" prop=\"Category\">\r\n              <!-- <el-radio-group\r\n                v-model=\"form.Category\"\r\n                style=\"width: 250px; margin-right: 30px\"\r\n              >\r\n                <el-radio\r\n                  v-for=\"item in comType\"\r\n                  :key=\"item.Id\"\r\n                  @change=\"changeCategory(item.Code)\"\r\n                  :label=\"item.Name\"\r\n                />\r\n              </el-radio-group> -->\r\n              <el-select\r\n                v-model=\"form.Category\"\r\n                placeholder=\"请选择\"\r\n                disabled\r\n                @change=\"changeCategory\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in comType\"\r\n                  :key=\"item.Id\"\r\n                  :value=\"item.Name\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"产能\" prop=\"Productivity\">\r\n              <el-input\r\n                v-model=\"form.Productivity\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"工厂图片\" class=\"factory-img\">\r\n              <OSSUpload\r\n                class=\"upload-demo\"\r\n                action=\"alioss\"\r\n                accept=\"image/*\"\r\n                :on-success=\"\r\n                  (response, file, fileList) => {\r\n                    uploadSuccess(response, file, fileList);\r\n                  }\r\n                \"\r\n                :on-remove=\"uploadRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                :show-file-list=\"false\"\r\n              >\r\n                <el-button type=\"primary\">上传图片</el-button>\r\n              </OSSUpload>\r\n              <div style=\"position: relative; width:200px\">\r\n                <el-image\r\n                  style=\"\r\n                    width: 200px;\r\n                    height: 120px;\r\n                    background-color: #eee;\r\n                    color: #999;\r\n                    font-size: 40px;\r\n                    margin-top: 10px;\r\n                    display: flex;\r\n                    justify-content: center;\r\n                    align-items: center;\r\n                  \"\r\n                  :src=\"src\"\r\n                  :preview-src-list=\"srcList\"\r\n                >\r\n                  <div slot=\"error\" class=\"image-slot\">\r\n                    <i class=\"el-icon-picture\" />\r\n                  </div>\r\n                </el-image>\r\n                <i\r\n                  v-show=\"src\"\r\n                  class=\"el-icon-circle-close img-item-icon\"\r\n                  @click=\"deleteItem()\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n            <!--          </el-form>-->\r\n\r\n            <el-divider />\r\n            <div class=\"year-batch-production\">\r\n              <header>工厂年度产量目标</header>\r\n              <div class=\"radio-box\">\r\n                <el-radio-group v-model=\"AllYear\" size=\"small\" @change=\"changeYear\">\r\n                  <el-radio-button :label=\"NewYear\">{{ NewYear }}</el-radio-button>\r\n                  <el-radio-button :label=\"NewYear - 1\">{{\r\n                    NewYear - 1\r\n                  }}</el-radio-button>\r\n                  <el-date-picker\r\n                    v-model=\"AllYearPicker\"\r\n                    type=\"year\"\r\n                    placeholder=\"其他年份\"\r\n                    :editable=\"false\"\r\n                    size=\"small\"\r\n                    style=\"width: 120px\"\r\n                    value-format=\"yyyy\"\r\n                    @change=\"changeYear\"\r\n                  />\r\n                </el-radio-group>\r\n              </div>\r\n              <div style=\"margin: 20px\" />\r\n              <p>年度产量目标：{{ AllTargetValue }}</p>\r\n              <el-form\r\n                ref=\"form2\"\r\n                :inline=\"true\"\r\n                class=\"demo-form-inline\"\r\n                style=\"padding-left: 20px\"\r\n                label-width=\"70px\"\r\n                :model=\"form2\"\r\n              >\r\n                <el-form-item\r\n                  v-for=\"(item, index) of form2.formArr\"\r\n                  :key=\"index\"\r\n                  :label=\"`${item.Month}月目标`\"\r\n                  :prop=\"`formArr[${index}].Target_value`\"\r\n                  :rules=\"[\r\n                    {\r\n                      validator: (rule, value, callback) => {\r\n                        targetValueStatus(rule, value, callback);\r\n                      }, //后面的这几个是传的自定义参数\r\n                      trigger: 'blur',\r\n                      required: false,\r\n                    },\r\n                  ]\"\r\n                >\r\n                  <el-input\r\n                    v-model=\"item.Target_value\"\r\n                    class=\"input-number\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    @input=\"TargetValueInput(item.Target_value, index)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n            <div class=\"year-batch-production\">\r\n              <header>工厂月均产能</header>\r\n              <div v-if=\"Ablity_List.length !== 0\">\r\n                <el-form\r\n                  v-for=\"(item, index) of Ablity_List\"\r\n                  :key=\"index\"\r\n                  ref=\"form3\"\r\n                  :inline=\"true\"\r\n                  class=\"demo-form-inline\"\r\n                  style=\"padding-left: 20px; display: inline-block;\"\r\n                  label-width=\"100px\"\r\n                  :model=\"item\"\r\n                >\r\n                  <el-form-item\r\n                    :label=\"item.Component_Type_Name + '(t)'\"\r\n                    prop=\"Production_Capacity\"\r\n                  >\r\n                    <el-input\r\n                      v-model=\"item.Production_Capacity\"\r\n                      class=\"input-number\"\r\n                      type=\"number\"\r\n                      min=\"0\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-form>\r\n              </div>\r\n              <div v-else style=\"padding-left: 40px;\">暂无数据</div>\r\n            </div>\r\n\r\n            <el-divider />\r\n\r\n            <!--        <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">-->\r\n            <el-form-item\r\n              label=\"财务结算组织\"\r\n              prop=\"Financial_Settlement_Organization\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.Financial_Settlement_Organization\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item\r\n              label=\"财务结算组织编号\"\r\n              prop=\"Financial_Settlement_Organization_Code\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.Financial_Settlement_Organization_Code\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"过磅预警阈值\" prop=\"Weigh_Warning_Threshold\">\r\n              <el-input-number v-model.number=\"form.Weigh_Warning_Threshold\" :min=\"0\" style=\"width: 80%\" class=\"cs-number-btn-hidden cs-input\" placeholder=\"\" clearable />\r\n              <span class=\"ml-8\">kg</span>\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"当货物磅重与理重上下浮动超过该值时进行预警提示\" placement=\"top-start\">\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </el-form-item>\r\n            <el-form-item label=\"发货员\" prop=\"Shipper\">\r\n              <el-select\r\n                v-model=\"form.Shipper\"\r\n                placeholder=\"请选择\"\r\n                clearable=\"\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in factoryPeoplelist\"\r\n                  :key=\"item.Id\"\r\n                  :value=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item\r\n              v-if=\"form.Component_Shipping_Approval\"\r\n              label=\"项目发货总量大于\"\r\n              prop=\"Shipping_Approval_LowerLimit\"\r\n            >\r\n\r\n              <el-input-number v-model=\"form.Shipping_Approval_LowerLimit\" class=\"cs-number-btn-hidden w80\" placeholder=\"请输入\" clearable=\"\" />t\r\n            </el-form-item>\r\n            <el-divider />\r\n            <el-form-item label=\"是否开启车间管理\" prop=\"Is_Workshop_Enabled\">\r\n              <div :class=\"form.Is_Workshop_Enabled ? 'is-workshop' : ''\">\r\n                <el-switch\r\n                  v-model=\"form.Is_Workshop_Enabled\"\r\n                  disabled\r\n                  active-color=\"#13ce66\"\r\n                  inactive-color=\"#ff4949\"\r\n                  @click.native=\"changeWorkshop(form.Is_Workshop_Enabled)\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item label=\"物料重复判定\" prop=\"Is_Mat_Duplicate\">\r\n              <el-switch\r\n                v-model=\"form.Is_Mat_Duplicate\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"原料、辅料入库、退库时相同属性的原料、辅料数量自动合并\" placement=\"top-start\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 8px;\" />\r\n              </el-tooltip>\r\n            </el-form-item>\r\n            <el-form-item label=\"末道工序直接入库\" prop=\"Is_Skip_Warehousing_Operation\">\r\n              <el-switch\r\n                v-model=\"form.Is_Skip_Warehousing_Operation\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"是否唯一码管理\" prop=\"Is_Workshop_Enabled\">\r\n              <el-switch\r\n                v-model=\"form.Scan_UniqueCode_Enabled\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"统计周期\" prop=\"Manage_Cycle_Enabled\">\r\n              <div :class=\"form.Manage_Cycle_Enabled ? 'is-workshop' : ''\">\r\n                <el-switch\r\n                  v-model=\"form.Manage_Cycle_Enabled\"\r\n                  disabled\r\n                  @click.native=\"changeManageCycleEnabled(form.Manage_Cycle_Enabled)\"\r\n                />\r\n                <template v-if=\"form.Manage_Cycle_Enabled\">\r\n                  <el-input v-model.number=\"form.Manage_Cycle_Begin_Date\" type=\"number\" :min=\"1\" :max=\"31\" class=\"manage-cycle-input input-number\" step=\"any\" placeholder=\"请输入日期\" @change=\"changeCheckNum($event,1)\">\r\n                    <el-select slot=\"prepend\" v-model=\"form.Manage_Cycle_Begin_Type\" class=\"manage-cycle-select\" placeholder=\"请选择\">\r\n                      <el-option label=\"上月\" :value=\"1\" />\r\n                      <el-option label=\"当月\" :value=\"2\" />\r\n                      <el-option label=\"下月\" :value=\"3\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                  <div class=\"text\">至</div>\r\n                  <el-input v-model.number=\"form.Manage_Cycle_End_Date\" type=\"number\" :min=\"1\" :max=\"31\" class=\"manage-cycle-input input-number\" step=\"any\" placeholder=\"请输入日期\" @change=\"changeCheckNum($event,2)\">\r\n                    <el-select slot=\"prepend\" v-model=\"form.Manage_Cycle_End_Type\" class=\"manage-cycle-select\" placeholder=\"请选择\">\r\n                      <el-option label=\"上月\" :value=\"1\" />\r\n                      <el-option label=\"当月\" :value=\"2\" />\r\n                      <el-option label=\"下月\" :value=\"3\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </template>\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item label=\"发货单审核\" prop=\"Component_Shipping_Approval\">\r\n              <el-switch\r\n                v-model=\"form.Component_Shipping_Approval\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"零件齐套管理\" prop=\"Is_Part_Prepare\">\r\n              <div :class=\"form.Is_Part_Prepare ? '' : 'is-workshop'\">\r\n                <el-switch\r\n                  v-model=\"form.Is_Part_Prepare\"\r\n                  disabled\r\n                  active-color=\"#13ce66\"\r\n                  inactive-color=\"#ff4949\"\r\n                  @click.native=\"changePartPrepareEnabled(form.Is_Part_Prepare)\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item label=\"喷码直接入库\" prop=\"Is_Part_Prepare\">\r\n              <el-switch\r\n                v-model=\"form.Is_Spraying_With_StockIn\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"清单导入构件重量\" prop=\"Comp_Compute_With_Part\">\r\n              <el-radio-group v-model=\"form.Comp_Compute_With_Part\" size=\"small\" @change=\"computedChange\">\r\n                <el-radio :label=\"false\">按清单导入</el-radio>\r\n                <el-radio :label=\"true\">按零件导入</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item label=\"发货过磅\" prop=\"Shipping_Weigh_Enabled\">\r\n              <el-switch\r\n                v-model=\"form.Shipping_Weigh_Enabled\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"原料入库是否质检\" prop=\"Is_Raw_Instore_Check\">\r\n              <el-switch\r\n                v-model=\"form.Is_Raw_Instore_Check\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"辅料入库是否质检\" prop=\"Is_Aux_Instore_Check\">\r\n              <el-switch\r\n                v-model=\"form.Is_Aux_Instore_Check\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"发货单号自动生成\" prop=\"Shipping_Order_Number_Auto_Generate\">\r\n              <el-switch\r\n                v-model=\"form.Shipping_Order_Number_Auto_Generate\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"唯一码管理\" prop=\"Materiel_Unique_Types\">\r\n              <el-checkbox-group v-model=\"form.Materiel_Unique_Types\">\r\n                <el-checkbox label=\"1\">板材</el-checkbox>\r\n                <el-checkbox label=\"2\">型材</el-checkbox>\r\n                <el-checkbox label=\"3\">钢卷</el-checkbox>\r\n                <el-checkbox label=\"99\">其他原料</el-checkbox>\r\n                <el-checkbox label=\"100\">辅料</el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n            <el-form-item label-width=\"160px\" label=\"套料工序允许直接报工\" prop=\"allowDirectReportingAfterNesting\">\r\n              <el-switch\r\n                v-model=\"form.allowDirectReportingAfterNesting\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label-width=\"220px\" label=\"是否发货计划通过审批才能发货\" prop=\"Is_Shipping_Plan_Approval\">\r\n              <el-switch\r\n                v-model=\"form.Is_Shipping_Plan_Approval\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"submit-btn\">\r\n      <el-button\r\n        type=\"primary\"\r\n        :loading=\"btnLoading\"\r\n        @click=\"handleSubmit('form', 'form2')\"\r\n      >保存</el-button>\r\n      <el-button plain @click=\"resetForm()\">重置</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetUserPage } from '@/api/sys'\r\nimport { GetFactoryEntity, SupplyFactoryInfo } from '@/api/PRO/factory'\r\nimport { GetProfessionalType } from '@/api/plm/material'\r\nimport OSSUpload from '@/views/plm/components/ossupload'\r\nimport getCommonData from '@/mixins/PRO/get-common-data'\r\nexport default {\r\n  components: {\r\n    OSSUpload\r\n  },\r\n  mixins: [getCommonData],\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      userOptions: [],\r\n      src: '',\r\n      srcList: [''],\r\n      num: 12,\r\n      form: {\r\n        Scan_UniqueCode_Enabled: false,\r\n        Is_Mat_Duplicate: false,\r\n        Component_Shipping_Approval: false,\r\n        Shipping_Approval_LowerLimit: undefined,\r\n        Short_Name: '',\r\n        Name: '',\r\n        Manager: '',\r\n        Manager_Id: '',\r\n        Address: '',\r\n        Financial_Settlement_Organization_Code: '',\r\n        Financial_Settlement_Organization: '',\r\n        Productivity: '',\r\n        Category: '',\r\n        Professional_Codes: [''],\r\n        Pic_Path: '',\r\n        Id: '',\r\n        Company_Id: '',\r\n        Weigh_Warning_Threshold: undefined,\r\n        Is_Spraying_With_StockIn: false,\r\n        Is_Workshop_Enabled: false, // 是否开启车间管理\r\n        Manage_Cycle_Enabled: false, // 是否开启统计周期\r\n        Is_Part_Prepare: true, // 是否开启零件齐套管理\r\n        Shipping_Weigh_Enabled: true,\r\n        allowDirectReportingAfterNesting: true,\r\n        Is_Shipping_Plan_Approval: true, // 是否发货计划通过审批才能发货\r\n        Manage_Cycle_Begin_Type: '',\r\n        Manage_Cycle_Begin_Date: '',\r\n        Manage_Cycle_End_Type: '',\r\n        Manage_Cycle_End_Date: '',\r\n        Is_Skip_Warehousing_Operation: false,\r\n        Shipper: '',\r\n        Comp_Compute_With_Part: false,\r\n        Is_Raw_Instore_Check: false,\r\n        Is_Aux_Instore_Check: false,\r\n        Shipping_Order_Number_Auto_Generate: true,\r\n        Materiel_Unique_Types: []\r\n      },\r\n      form2: {\r\n        formArr: []\r\n      },\r\n      formArrCopy: [],\r\n      changeForm2: [],\r\n      AllTargetValue: 0,\r\n      AllYear: '',\r\n      AllYearPicker: '',\r\n      NewYear: new Date().getFullYear(),\r\n      rules: {\r\n        Short_Name: [\r\n          { required: true, message: '请输入工厂名称', trigger: 'blur' }\r\n        ],\r\n        Shipping_Approval_LowerLimit: [\r\n          { required: true, message: '请输入', trigger: 'blur' }\r\n        ],\r\n        Category: [{ required: true, message: '请选择', trigger: 'change' }],\r\n        Productivity: [\r\n          {\r\n            required: true,\r\n            validator: this.productivityStatus,\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      },\r\n      rules2: {\r\n        Target_value: [\r\n          {\r\n            required: false,\r\n            validator: this.targetValueStatus,\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      },\r\n      comType: '',\r\n      Ablity_List: [] // 产能平衡数据\r\n    }\r\n  },\r\n  created() {\r\n    this.getFactoryEntityForm()\r\n    this.getFactoryPeoplelist()\r\n  },\r\n  methods: {\r\n    // 统计周期开关\r\n    changeManageCycleEnabled(e) {\r\n      // this.form.Manage_Cycle_Enabled = !e\r\n      if (!e) {\r\n        this.$confirm(\r\n          '统计周期开启后无法关闭，请确认后开启',\r\n          {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }\r\n        )\r\n          .then(() => {\r\n            this.form.Manage_Cycle_Enabled = true\r\n          })\r\n          .catch(() => {\r\n            this.form.Manage_Cycle_Enabled = false\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消'\r\n            })\r\n          })\r\n      }\r\n    },\r\n    // 零件齐套管理开关\r\n    changePartPrepareEnabled(e) {\r\n      if (e) {\r\n        this.$confirm(\r\n          '零件齐套管理按钮关闭后不可再开启，请确认后关闭',\r\n          {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }\r\n        )\r\n          .then(() => {\r\n            this.form.Is_Part_Prepare = false\r\n          })\r\n          .catch(() => {\r\n            this.form.Is_Part_Prepare = true\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消'\r\n            })\r\n          })\r\n      }\r\n    },\r\n    // 统计日期校验\r\n    changeCheckNum(e, type) {\r\n      if (Number(e) < 1 || Number(e) > 31) {\r\n        if (type === 1) {\r\n          this.form.Manage_Cycle_Begin_Date = ''\r\n        } else if (type === 2) {\r\n          this.form.Manage_Cycle_End_Date = ''\r\n        }\r\n      }\r\n    },\r\n    // 车间管理开关\r\n    changeWorkshop(e) {\r\n      // console.log(e, \"eee\");\r\n      if (!e) {\r\n        this.$confirm(\r\n          '车间管理开启后无法关闭，请确认您的业务管理方式中涉及车间层级',\r\n          {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }\r\n        )\r\n          .then(() => {\r\n            this.form.Is_Workshop_Enabled = true\r\n          })\r\n          .catch(() => {\r\n            this.form.Is_Workshop_Enabled = false\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消'\r\n            })\r\n          })\r\n      }\r\n      // console.log(this.form.Is_Workshop_Enabled, \"this.form.Is_Workshop_Enabled\");\r\n    },\r\n    productivityStatus(rule, value, callback) {\r\n      if (value === '') {\r\n        callback(new Error('请输入'))\r\n      } else if (!Number(value) && Number(value) !== 0) {\r\n        callback(new Error('只能为数字'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    targetValueStatus(rule, value, callback) {\r\n      if (!value) {\r\n        callback()\r\n      } else if (!Number(value) && Number(value) !== 0) {\r\n        callback(new Error('只能为数字'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    // targetValueStatus2(rule, value, callback) {\r\n    //   if (!value) {\r\n    //     callback();\r\n    //   } else if (!Boolean(Number(value)) && Number(value) !== 0) {\r\n    //     callback(new Error(\"只能为数字\"));\r\n    //   } else {\r\n    //     callback();\r\n    //   }\r\n    // },\r\n\r\n    getFactoryEntityForm() {\r\n      GetFactoryEntity({\r\n        id: localStorage.getItem('CurReferenceId')\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const {\r\n            Name,\r\n            Short_Name,\r\n            Category,\r\n            Professional_Codes,\r\n            Id,\r\n            Company_Id,\r\n            Manager,\r\n            Manager_Id,\r\n            Address,\r\n            Financial_Settlement_Organization_Code,\r\n            Financial_Settlement_Organization,\r\n            Productivity,\r\n            Pic_Path,\r\n            Weigh_Warning_Threshold,\r\n            Is_Skip_Warehousing_Operation,\r\n            Is_Workshop_Enabled,\r\n            Nested_Must_Before_Processing,\r\n            Manage_Cycle_Enabled,\r\n            Is_Part_Prepare,\r\n            Is_Spraying_With_StockIn,\r\n            Manage_Cycle_Begin_Type,\r\n            Manage_Cycle_Begin_Date,\r\n            Manage_Cycle_End_Type,\r\n            Manage_Cycle_End_Date,\r\n            Component_Shipping_Approval,\r\n            Is_Mat_Duplicate,\r\n            Scan_UniqueCode_Enabled,\r\n            Shipping_Weigh_Enabled,\r\n            Shipping_Approval_LowerLimit,\r\n            Shipper,\r\n            Comp_Compute_With_Part,\r\n            Is_Raw_Instore_Check,\r\n            Is_Aux_Instore_Check,\r\n            Shipping_Order_Number_Auto_Generate,\r\n            Materiel_Unique_Types,\r\n            Is_Shipping_Plan_Approval\r\n          } = res.Data.entity\r\n          this.form.Is_Mat_Duplicate = Is_Mat_Duplicate\r\n          this.form.Scan_UniqueCode_Enabled = Scan_UniqueCode_Enabled\r\n          this.form.Shipping_Weigh_Enabled = Shipping_Weigh_Enabled\r\n          this.form.Component_Shipping_Approval = Component_Shipping_Approval\r\n          this.form.Shipping_Approval_LowerLimit = Shipping_Approval_LowerLimit\r\n          this.form.Short_Name = Short_Name\r\n          this.form.Name = Name\r\n          this.form.Category = Category\r\n          this.form.Professional_Codes = Professional_Codes\r\n          this.form.Id = Id\r\n          this.form.Company_Id = Company_Id\r\n          this.form.Manager = Manager\r\n          this.form.Manager_Id = Manager_Id\r\n          this.form.Address = Address\r\n          this.form.Financial_Settlement_Organization_Code =\r\n            Financial_Settlement_Organization_Code\r\n          this.form.Financial_Settlement_Organization =\r\n            Financial_Settlement_Organization\r\n          this.form.Productivity = Productivity\r\n          this.form.Pic_Path = Pic_Path\r\n          this.form.Is_Workshop_Enabled = Is_Workshop_Enabled\r\n          this.form.allowDirectReportingAfterNesting = !Nested_Must_Before_Processing\r\n          this.form.Is_Shipping_Plan_Approval = Is_Shipping_Plan_Approval\r\n          this.form.Is_Skip_Warehousing_Operation = Is_Skip_Warehousing_Operation\r\n          this.form.Weigh_Warning_Threshold = Weigh_Warning_Threshold || undefined\r\n          this.form.Manage_Cycle_Enabled = Manage_Cycle_Enabled\r\n          this.form.Is_Part_Prepare = Is_Part_Prepare\r\n          this.form.Is_Spraying_With_StockIn = Is_Spraying_With_StockIn\r\n          this.form.Manage_Cycle_Begin_Type = Manage_Cycle_Begin_Type || ''\r\n          this.form.Manage_Cycle_Begin_Date = Manage_Cycle_Begin_Date || ''\r\n          this.form.Manage_Cycle_End_Type = Manage_Cycle_End_Type || ''\r\n          this.form.Manage_Cycle_End_Date = Manage_Cycle_End_Date || ''\r\n          this.form.Comp_Compute_With_Part = Comp_Compute_With_Part\r\n          this.form.Is_Raw_Instore_Check = Is_Raw_Instore_Check\r\n          this.form.Is_Aux_Instore_Check = Is_Aux_Instore_Check\r\n          this.form.Shipping_Order_Number_Auto_Generate = Shipping_Order_Number_Auto_Generate\r\n          this.form.Materiel_Unique_Types = Materiel_Unique_Types.split(',')\r\n          this.form.Shipper = Shipper || ''\r\n          this.src = Pic_Path\r\n          this.srcList[0] = Pic_Path\r\n          this.formArrCopy = res.Data.list\r\n          this.AllYear = this.NewYear\r\n          this.AllYearPicker = ''\r\n          this.AllTargetValue = 0\r\n          this.form2.formArr = res.Data.list.filter((item) => {\r\n            return item.Year == this.NewYear\r\n          })\r\n          this.form2.formArr.forEach((item) => {\r\n            this.AllTargetValue =\r\n              this.AllTargetValue + Number(item.Target_value)\r\n          })\r\n          if (this.form2.formArr.length < 12) {\r\n            // this.form2.formArr = res.Data.list;\r\n            for (let i = 1; i <= 12; i++) {\r\n              const temp = this.form2.formArr.find((item) => {\r\n                return item.Month == i\r\n              })\r\n              if (!temp) {\r\n                const month = {\r\n                  Factory_id: Id,\r\n                  Professional_Code: Professional_Codes[0],\r\n                  Year: this.NewYear,\r\n                  Month: i,\r\n                  Target_value: ''\r\n                }\r\n                this.form2.formArr.push(month)\r\n              }\r\n            }\r\n          }\r\n          this.form2.formArr.sort(function(a, b) {\r\n            return a.Month - b.Month\r\n          })\r\n          this.Ablity_List = res.Data.Ablity_List\r\n          this.getProfessionalTypeList()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getProfessionalTypeList() {\r\n      GetProfessionalType({\r\n        is_System: false,\r\n        pagesize: -1,\r\n        companyId: this.form.Company_Id\r\n      }).then((res) => {\r\n        this.comType = res.Data.Data\r\n      })\r\n    },\r\n    changeCategory(e) {\r\n      const temp = this.comType.find((item) => {\r\n        return item.Name == e\r\n      })\r\n      this.form.Professional_Codes[0] = temp.Code\r\n      this.form2.formArr.forEach((item) => {\r\n        item.Professional_Code = temp.Code\r\n      })\r\n      this.formArrCopy.forEach((item) => {\r\n        item.Professional_Code = temp.Code\r\n      })\r\n    },\r\n    changeYear(e) {\r\n      if (e) {\r\n        this.AllYear = e\r\n      } else {\r\n        this.AllYear = this.NewYear\r\n      }\r\n      if (this.AllYear == this.NewYear || this.AllYear == this.NewYear - 1) {\r\n        this.AllYearPicker = ''\r\n      }\r\n      this.AllTargetValue = 0\r\n      this.form2.formArr = this.formArrCopy.filter((item) => {\r\n        return item.Year == this.AllYear\r\n      })\r\n      if (this.form2.formArr.length < 12) {\r\n        for (let i = 1; i <= 12; i++) {\r\n          const temp = this.form2.formArr.find((item) => {\r\n            return item.Month == i\r\n          })\r\n          if (!temp) {\r\n            const month = {\r\n              Factory_id: this.form.Id,\r\n              Professional_Code: this.form.Professional_Codes[0],\r\n              Year: this.AllYear,\r\n              Month: i,\r\n              Target_value: ''\r\n            }\r\n            this.form2.formArr.push(month)\r\n          }\r\n        }\r\n      }\r\n      this.form2.formArr.sort(function(a, b) {\r\n        return a.Month - b.Month\r\n      })\r\n      this.form2.formArr.forEach((item) => {\r\n        this.AllTargetValue = this.AllTargetValue + Number(item.Target_value)\r\n      })\r\n    },\r\n    TargetValueInput(e, index) {\r\n      if (this.formArrCopy.length != 0) {\r\n        const temp = this.formArrCopy.find((item) => {\r\n          return (\r\n            item.Year == this.form2.formArr[index].Year &&\r\n            item.Month == this.form2.formArr[index].Month\r\n          )\r\n        })\r\n        if (temp) {\r\n          temp.Target_value = e\r\n        } else {\r\n          this.formArrCopy.push(this.form2.formArr[index])\r\n        }\r\n      } else {\r\n        this.formArrCopy.push(this.form2.formArr[index])\r\n      }\r\n      this.AllTargetValue = 0\r\n      this.form2.formArr.forEach((item) => {\r\n        this.AllTargetValue = this.AllTargetValue + Number(item.Target_value)\r\n      })\r\n    },\r\n    async querySearchAsync(queryString, cb) {\r\n      let results = []\r\n      results = await this.getUserList(queryString)\r\n      cb(results)\r\n    },\r\n    getUserList(queryString = '') {\r\n      return new Promise((resolve) => {\r\n        GetUserPage({\r\n          Page: 1,\r\n          pageSize: 20,\r\n          Search: queryString\r\n        }).then((res) => {\r\n          this.userOptions = res.Data.Data.map((v) => {\r\n            this.$set(v, 'value', v.Display_Name)\r\n            return v\r\n          })\r\n          resolve(this.userOptions)\r\n        })\r\n      })\r\n    },\r\n    blur() {\r\n      const temp = this.userOptions.find((item) => {\r\n        return item.Display_Name == this.form.Manager\r\n      })\r\n      if (temp) {\r\n        this.form.Manager_Id = temp.Id\r\n      } else {\r\n        this.form.Manager_Id = ''\r\n      }\r\n    },\r\n    clear() {\r\n      this.form.Manager_Id = ''\r\n      this.form.Manager = ''\r\n      this.$refs.autocomplete.activated = true\r\n    },\r\n    handleSelect(item) {\r\n      this.form.Manager_Id = item.Id\r\n      this.form.Manager = item.Display_Name\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      const imgObj = { File_Name: '', File_Url: '' }\r\n      if (file.hasOwnProperty('response')) {\r\n        imgObj.File_Url = file.response.Data.split('*')[0]\r\n        imgObj.File_Name = file.response.Data.split('*')[3]\r\n      } else {\r\n        imgObj.File_Url = file.url\r\n      }\r\n      this.form.Pic_Path = imgObj.File_Url\r\n      this.src = file.response.encryptionUrl\r\n      this.srcList[0] = file.response.encryptionUrl\r\n      // let temp = this.srcList;\r\n      // this.$set(temp, \"0\", this.srcList[0]);\r\n    },\r\n    uploadExceed(files, fileList) {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '已超过文件上传最大数量'\r\n      })\r\n    },\r\n    uploadRemove(file, fileList) {},\r\n    handlePreview(file) {},\r\n    deleteItem() {\r\n      this.form.Pic_Path = ''\r\n      this.src = ''\r\n      this.srcList[0] = ''\r\n    },\r\n    handleSubmit(formName1, formName2) {\r\n      this.$confirm('确认保存', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          const form = { ...this.form }\r\n          const form2 = [...this.formArrCopy]\r\n          const form3 = JSON.parse(JSON.stringify(this.Ablity_List))\r\n          console.log(this.Ablity_List, 'Ablity_List')\r\n          form3.map(item => {\r\n            delete item['Component_Type_Name']\r\n            item.Production_Capacity = item.Production_Capacity ? Number(item.Production_Capacity) : item.Production_Capacity\r\n          })\r\n          form.Materiel_Unique_Types = form.Materiel_Unique_Types.join(',')\r\n          form.Nested_Must_Before_Processing = !form.allowDirectReportingAfterNesting\r\n          console.log(form, 'form')\r\n          this.$refs[formName1].validate((valid) => {\r\n            if (valid) {\r\n              this.$refs[formName2].validate((valid) => {\r\n                if (valid) {\r\n                  const obj = {\r\n                    entity: form,\r\n                    list: form2,\r\n                    Ability_List: form3\r\n                  }\r\n                  if (form.Manage_Cycle_Enabled) {\r\n                    if (!form.Manage_Cycle_Begin_Type || !form.Manage_Cycle_Begin_Date || !form.Manage_Cycle_End_Type || !form.Manage_Cycle_End_Date) {\r\n                      this.$message.warning('请补全统计周期')\r\n                      return\r\n                    }\r\n                  }\r\n                  this.btnLoading = true\r\n                  SupplyFactoryInfo(obj).then((res) => {\r\n                    if (res.IsSucceed) {\r\n                      this.$message({\r\n                        message: '保存成功',\r\n                        type: 'success'\r\n                      })\r\n                      this.getFactoryEntityForm()\r\n                    } else {\r\n                      this.$message({\r\n                        message: res.Message,\r\n                        type: 'error'\r\n                      })\r\n                    }\r\n                  })\r\n                  this.btnLoading = false\r\n                } else {\r\n                  return false\r\n                }\r\n              })\r\n            } else {\r\n              return false\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    resetForm() {\r\n      this.getFactoryEntityForm()\r\n    },\r\n    computedChange(val) {\r\n      console.log(val)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\nheader {\r\n  padding: 20px;\r\n  font-size: 20px;\r\n}\r\n\r\n.cs-z-page-main-content{\r\n  padding-bottom: 62px;\r\n}\r\n\r\n.manage-cycle-input{\r\n  ::v-deep{\r\n    .el-select .el-input {\r\n      width: 120px !important;\r\n    }\r\n  }\r\n}\r\n\r\n.basic-information {\r\n  ::v-deep {\r\n    .el-form-item__content {\r\n      min-width: 250px;\r\n      margin-right: 30px;\r\n      .cs-input{\r\n\r\n        .el-input {\r\n          width: unset;\r\n        }\r\n      }\r\n      .el-input {\r\n        width: 250px;\r\n      }\r\n    }\r\n\r\n    .el-switch.is-disabled .el-switch__core,\r\n    .el-switch.is-disabled .el-switch__label {\r\n      cursor: pointer;\r\n    }\r\n    .is-workshop {\r\n      .el-switch.is-disabled .el-switch__core,\r\n      .el-switch.is-disabled .el-switch__label {\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n    .el-switch.is-disabled {\r\n      opacity: 1;\r\n      .el-switch.is-checked .el-switch__core {\r\n        border-color: #298dff;\r\n        background-color: #298dff;\r\n      }\r\n    }\r\n  }\r\n  .manage-cycle{\r\n    display: block;\r\n    .text {\r\n      margin: 12px 0;\r\n      text-align: center;\r\n    }\r\n    ::v-deep {\r\n      .manage-cycle-select {\r\n        .el-input{\r\n          width: 80px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .factory-img {\r\n    //display: block;\r\n  }\r\n\r\n  .img-item-icon {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    color: #333;\r\n    font-size: 20px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.year-batch-production {\r\n  .radio-box {\r\n    .el-radio-group {\r\n      padding-left: 30px;\r\n    }\r\n    .el-radio-button {\r\n      font-size: 20px;\r\n    }\r\n    ::v-deep .el-input--small .el-input__inner {\r\n      height: 30px;\r\n      line-height: 30px;\r\n      border-radius: 0 4px 4px 0;\r\n      border-left: none;\r\n    }\r\n    ::v-deep .el-icon-circle-close {\r\n      color: #d0d3db;\r\n    }\r\n  }\r\n\r\n  p {\r\n    padding-left: 30px;\r\n  }\r\n}\r\n.submit-btn {\r\n  padding: 16px 0 16px 32px;\r\n  position: absolute;\r\n  z-index: 99;\r\n  bottom: 16px;\r\n  background-color: #fff;\r\n  width: calc(100% - 48px);\r\n}\r\n\r\n::v-deep .input-number{\r\n      input{\r\n        padding-right: 2px;\r\n      }\r\n    }\r\n\r\n.ml-8{\r\n  margin: 0 8px;\r\n}\r\n.w80{\r\n  ::v-deep .el-input{\r\n    width: 80% !important;\r\n\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\index.vue", "mtime": 1758509456111}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getTbInfo", "DynamicDataTable", "TopHeader", "DeleteWorkingTeams", "GetWorkingTeams", "GetWorkingTeamsPageList", "detail", "info", "getCommonData", "name", "components", "mixins", "data", "tbConfig", "Pager_<PERSON>gn", "queryInfo", "Page", "PageSize", "SortName", "SortOrder", "Parameter<PERSON>son", "currentComponent", "title", "columns", "tbData", "total", "tbLoading", "dialogVisible", "selectList", "keywords", "watch", "e", "FactoryDetailData", "Is_Workshop_Enabled", "map", "item", "Code", "Is_Display", "created", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getCurFactory", "getTableConfig", "fetchData", "stop", "methods", "_this2", "pageInfo", "then", "res", "IsSucceed", "Data", "TotalCount", "console", "log", "$message", "message", "Message", "type", "handleClose", "handleSelectionChange", "list", "records", "handleAdd", "_this3", "$nextTick", "_", "$refs", "initData", "handleEdit", "row", "_this4", "Id", "handleDetail", "_this5", "handleDelete", "isAll", "_this6", "ids", "i", "toString", "$confirm", "confirmButtonText", "cancelButtonText", "xTable", "clearCheckboxRow", "catch", "handleSearch", "reset"], "sources": ["src/views/PRO/basic-information/group/index.vue"], "sourcesContent": ["<template>\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div class=\"cs-z-page-main-content\">\n      <top-header padding=\"0\">\n        <template #right>\n          <el-form label-width=\"80px\" :inline=\"true\" @submit.native.prevent>\n            <el-form-item label=\"班组名称\">\n              <el-input v-model=\"keywords\" placeholder=\"请输入\" clearable @keyup.enter.native=\"handleSearch\" />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\n              <el-button @click=\"reset\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </template>\n        <template #left>\n          <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\n          <el-button\n            type=\"danger\"\n            :disabled=\"!selectList.length\"\n            @click=\"handleDelete(true)\"\n          >删除</el-button>\n        </template>\n      </top-header>\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\n        <vxe-table\n          ref=\"xTable\"\n          v-loading=\"tbLoading\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          element-loading-spinner=\"el-icon-loading\"\n          element-loading-text=\"拼命加载中\"\n          empty-text=\"暂无数据\"\n          height=\"100%\"\n          :data=\"tbData\"\n          stripe\n          resizable\n          :auto-resize=\"true\"\n          class=\"cs-vxe-table\"\n          :tooltip-config=\"{ enterable: true }\"\n          @checkbox-all=\"handleSelectionChange\"\n          @checkbox-change=\"handleSelectionChange\"\n        >\n          <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\n          <vxe-column\n            v-for=\"(item, index) in columns\"\n            :key=\"index\"\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n            show-overflow=\"tooltip\"\n            sortable\n            :align=\"item.Align\"\n            :field=\"item.Code\"\n            :title=\"item.Display_Name\"\n            :visible=\"item.Is_Display\"\n          >\n            <template #default=\"{ row }\">\n              <span v-if=\"item.Code === 'Manager_UserName'\">\n                <div>{{ row.Manager_UserName || \"-\" }}</div>\n              </span>\n              <span v-else-if=\"item.Code === 'Load_Unit'\">\n                <div>{{ row.Load_Unit || \"-\" }}</div>\n              </span>\n              <span v-else-if=\"item.Code === 'Workshop_Name'\">\n                <div>{{ row.Workshop_Name || \"-\" }}</div>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Outsource'\">\n                <el-tag v-if=\"row.Is_Outsource\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Enabled'\">\n                <el-tag v-if=\"row.Is_Enabled\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Sort'\">\n                <span>{{ item.Sort || item.Sort === 0 ? row[item.Code] : '-' }}</span>\n              </span>\n              <span v-else>{{ row[item.Code] || \"-\" }}</span>\n            </template>\n          </vxe-column>\n          <vxe-column fixed=\"right\" title=\"操作\" width=\"160\" show-overflow align=\"center\">\n            <template #default=\"{ row }\">\n              <el-button type=\"text\" @click=\"handleDetail(row)\">查看</el-button>\n              <el-button type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\n              <el-button\n                type=\"text\"\n                style=\"color: red\"\n                @click=\"handleDelete(false, row)\"\n              >删除</el-button>\n            </template>\n          </vxe-column>\n        </vxe-table>\n        <!-- <dynamic-data-table\n          ref=\"dyTable\"\n          :columns=\"columns\"\n          :config=\"tbConfig\"\n          :data=\"tbData\"\n          :page=\"queryInfo.Page\"\n          :total=\"total\"\n          border\n          stripe\n          class=\"cs-plm-dy-table\"\n          @multiSelectedChange=\"handleSelectionChange\"\n          @gridPageChange=\"handlePageChange\"\n          @gridSizeChange=\"handlePageChange\"\n          @tableSearch=\"tableSearch\"\n        >\n          <template slot=\"Manager_UserName\" slot-scope=\"{ row }\">\n            <div>{{ row.Manager_UserName || \"-\" }}</div>\n          </template>\n          <template slot=\"Load_Unit\" slot-scope=\"{ row }\">\n            <div>{{ row.Load_Unit || \"-\" }}</div>\n          </template>\n          <template slot=\"Workshop_Name\" slot-scope=\"{ row }\">\n            <div>{{ row.Workshop_Name || \"-\" }}</div>\n          </template>\n          <template slot=\"Is_Outsource\" slot-scope=\"{ row }\">\n            <div><el-tag v-if=\"row.Is_Outsource\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag></div>\n          </template>\n          <template slot=\"Is_Enabled\" slot-scope=\"{ row }\">\n            <div><el-tag v-if=\"row.Is_Enabled\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag></div>\n          </template>\n          <template slot=\"op\" slot-scope=\"{ row }\">\n            <el-button type=\"text\" @click=\"handleDetail(row)\">查看</el-button>\n            <el-button type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\n            <el-button\n              type=\"text\"\n              style=\"color: red\"\n              @click=\"handleDelete(false, row)\"\n            >删除</el-button>\n          </template>\n        </dynamic-data-table> -->\n      </div>\n    </div>\n\n    <el-dialog\n      v-dialogDrag\n      class=\"cs-dialog\"\n      :title=\"title\"\n      :visible.sync=\"dialogVisible\"\n      :close-on-click-modal=\"false\"\n      custom-class=\"dialogCustomClass\"\n      width=\"900px\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        v-if=\"dialogVisible\"\n        ref=\"content\"\n        :dialog-visible=\"dialogVisible\"\n        @close=\"handleClose\"\n        @refresh=\"fetchData\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\nimport TopHeader from '@/components/TopHeader'\nimport { DeleteWorkingTeams, GetWorkingTeams, GetWorkingTeamsPageList } from '@/api/PRO/technology-lib'\nimport detail from './component/detail'\nimport info from './component/info'\nimport getCommonData from '@/mixins/PRO/get-common-data'\n\nexport default {\n  name: 'PROGroup',\n  components: {\n    DynamicDataTable,\n    TopHeader,\n    info,\n    detail\n  },\n  mixins: [getTbInfo, getCommonData],\n  data() {\n    return {\n      tbConfig: {\n        Pager_Align: 'center'\n      },\n      queryInfo: {\n        Page: 1,\n        PageSize: 10,\n        SortName: 'Sort',\n        SortOrder: 'asc',\n        ParameterJson: []\n      },\n      currentComponent: '',\n      title: '',\n      columns: [],\n      tbData: [],\n      total: 0,\n      tbLoading: false,\n      dialogVisible: false,\n      selectList: [],\n      keywords: ''\n    }\n  },\n  watch: {\n    columns(e) {\n      // 车间未开启不显示所属车间信息\n      if (!this.FactoryDetailData.Is_Workshop_Enabled) {\n        e.map((item) => {\n          if (item.Code === 'Workshop_Name') {\n            item.Is_Display = false\n          }\n        })\n      }\n    }\n  },\n  async created() {\n    // 获取工厂详情\n    await this.getCurFactory()\n    this.tbLoading = true\n    await this.getTableConfig('pro_group_list')\n    await this.fetchData()\n  },\n  methods: {\n    fetchData() {\n      this.tbLoading = true\n      GetWorkingTeamsPageList({ keywords: this.keywords, pageInfo: this.queryInfo }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.Data\n          this.total = res.Data.TotalCount\n          console.log(this.total)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n        this.tbLoading = false\n      })\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    handleSelectionChange(list) {\n      console.log('list', list)\n      this.selectList = list.records\n    },\n    handleAdd() {\n      this.currentComponent = 'detail'\n      this.title = '新增'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs['content'].initData('', this.FactoryDetailData.Is_Workshop_Enabled)\n      })\n    },\n    handleEdit(row) {\n      this.currentComponent = 'detail'\n      this.title = '编辑'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs['content'].initData(row.Id, this.FactoryDetailData.Is_Workshop_Enabled)\n      })\n    },\n    handleDetail(row) {\n      this.currentComponent = 'info'\n      this.title = '查看'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs['content'].initData(row, this.FactoryDetailData.Is_Workshop_Enabled)\n      })\n    },\n    handleDelete(isAll, row) {\n      const ids = !isAll ? row.Id : this.selectList.map((i) => i.Id).toString()\n      this.$confirm('是否删除选中班组?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          console.log('id', ids)\n          DeleteWorkingTeams({\n            ids: ids.toString()\n          }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.$refs.xTable.clearCheckboxRow()\n              this.selectList = []\n              this.fetchData()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    handleSearch() {\n      this.$refs.xTable.clearCheckboxRow()\n      this.selectList = []\n      this.fetchData()\n    },\n    reset() {\n      this.keywords = ''\n      this.$refs.xTable.clearCheckboxRow()\n      this.selectList = []\n      this.fetchData()\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.cs-dialog {\n  ::v-deep {\n    .el-dialog__body {\n      padding-top: 0;\n    }\n  }\n}\n::v-deep {\n  .cs-top-header-box {\n    line-height: 0px;\n  }\n}\n\n.cs-z-tb-wrapper {\n  height: 0;\n}\n\n::v-deep .pagination {\n    justify-content: flex-end !important;\n    margin-top: 12px !important;\n    .el-input--small .el-input__inner {\n        height: 28px;\n        line-height: 28px;\n    }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2JA,OAAAA,SAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,SAAA;AACA,SAAAC,kBAAA,EAAAC,eAAA,EAAAC,uBAAA;AACA,OAAAC,MAAA;AACA,OAAAC,IAAA;AACA,OAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAT,gBAAA,EAAAA,gBAAA;IACAC,SAAA,EAAAA,SAAA;IACAK,IAAA,EAAAA,IAAA;IACAD,MAAA,EAAAA;EACA;EACAK,MAAA,GAAAX,SAAA,EAAAQ,aAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,WAAA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,aAAA;MACA;MACAC,gBAAA;MACAC,KAAA;MACAC,OAAA;MACAC,MAAA;MACAC,KAAA;MACAC,SAAA;MACAC,aAAA;MACAC,UAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACAP,OAAA,WAAAA,QAAAQ,CAAA;MACA;MACA,UAAAC,iBAAA,CAAAC,mBAAA;QACAF,CAAA,CAAAG,GAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACAD,IAAA,CAAAE,UAAA;UACA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OAEAT,KAAA,CAAAU,aAAA;UAAA;YACAV,KAAA,CAAAb,SAAA;YAAAoB,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAW,cAAA;UAAA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAY,SAAA;UAAA;UAAA;YAAA,OAAAL,QAAA,CAAAM,IAAA;QAAA;MAAA,GAAAT,OAAA;IAAA;EACA;EACAU,OAAA;IACAF,SAAA,WAAAA,UAAA;MAAA,IAAAG,MAAA;MACA,KAAA5B,SAAA;MACArB,uBAAA;QAAAwB,QAAA,OAAAA,QAAA;QAAA0B,QAAA,OAAAxC;MAAA,GAAAyC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAJ,MAAA,CAAA9B,MAAA,GAAAiC,GAAA,CAAAE,IAAA,CAAAA,IAAA;UACAL,MAAA,CAAA7B,KAAA,GAAAgC,GAAA,CAAAE,IAAA,CAAAC,UAAA;UACAC,OAAA,CAAAC,GAAA,CAAAR,MAAA,CAAA7B,KAAA;QACA;UACA6B,MAAA,CAAAS,QAAA;YACAC,OAAA,EAAAP,GAAA,CAAAQ,OAAA;YACAC,IAAA;UACA;QACA;QACAZ,MAAA,CAAA5B,SAAA;MACA;IACA;IACAyC,WAAA,WAAAA,YAAA;MACA,KAAAxC,aAAA;IACA;IACAyC,qBAAA,WAAAA,sBAAAC,IAAA;MACAR,OAAA,CAAAC,GAAA,SAAAO,IAAA;MACA,KAAAzC,UAAA,GAAAyC,IAAA,CAAAC,OAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAnD,gBAAA;MACA,KAAAC,KAAA;MACA,KAAAK,aAAA;MACA,KAAA8C,SAAA,WAAAC,CAAA;QACAF,MAAA,CAAAG,KAAA,YAAAC,QAAA,KAAAJ,MAAA,CAAAxC,iBAAA,CAAAC,mBAAA;MACA;IACA;IACA4C,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA1D,gBAAA;MACA,KAAAC,KAAA;MACA,KAAAK,aAAA;MACA,KAAA8C,SAAA,WAAAC,CAAA;QACAK,MAAA,CAAAJ,KAAA,YAAAC,QAAA,CAAAE,GAAA,CAAAE,EAAA,EAAAD,MAAA,CAAA/C,iBAAA,CAAAC,mBAAA;MACA;IACA;IACAgD,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,MAAA;MACA,KAAA7D,gBAAA;MACA,KAAAC,KAAA;MACA,KAAAK,aAAA;MACA,KAAA8C,SAAA,WAAAC,CAAA;QACAQ,MAAA,CAAAP,KAAA,YAAAC,QAAA,CAAAE,GAAA,EAAAI,MAAA,CAAAlD,iBAAA,CAAAC,mBAAA;MACA;IACA;IACAkD,YAAA,WAAAA,aAAAC,KAAA,EAAAN,GAAA;MAAA,IAAAO,MAAA;MACA,IAAAC,GAAA,IAAAF,KAAA,GAAAN,GAAA,CAAAE,EAAA,QAAApD,UAAA,CAAAM,GAAA,WAAAqD,CAAA;QAAA,OAAAA,CAAA,CAAAP,EAAA;MAAA,GAAAQ,QAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzB,IAAA;MACA,GACAV,IAAA;QACAK,OAAA,CAAAC,GAAA,OAAAwB,GAAA;QACAnF,kBAAA;UACAmF,GAAA,EAAAA,GAAA,CAAAE,QAAA;QACA,GAAAhC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA2B,MAAA,CAAAtB,QAAA;cACAG,IAAA;cACAF,OAAA;YACA;YACAqB,MAAA,CAAAV,KAAA,CAAAiB,MAAA,CAAAC,gBAAA;YACAR,MAAA,CAAAzD,UAAA;YACAyD,MAAA,CAAAlC,SAAA;UACA;YACAkC,MAAA,CAAAtB,QAAA;cACAC,OAAA,EAAAP,GAAA,CAAAQ,OAAA;cACAC,IAAA;YACA;UACA;QACA;MACA,GACA4B,KAAA;QACAT,MAAA,CAAAtB,QAAA;UACAG,IAAA;UACAF,OAAA;QACA;MACA;IACA;IACA+B,YAAA,WAAAA,aAAA;MACA,KAAApB,KAAA,CAAAiB,MAAA,CAAAC,gBAAA;MACA,KAAAjE,UAAA;MACA,KAAAuB,SAAA;IACA;IACA6C,KAAA,WAAAA,MAAA;MACA,KAAAnE,QAAA;MACA,KAAA8C,KAAA,CAAAiB,MAAA,CAAAC,gBAAA;MACA,KAAAjE,UAAA;MACA,KAAAuB,SAAA;IACA;EACA;AACA", "ignoreList": []}]}
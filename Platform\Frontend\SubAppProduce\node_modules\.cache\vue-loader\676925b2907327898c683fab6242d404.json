{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\production-execution\\new-report\\home.vue?vue&type=template&id=71c3a17e&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\production-execution\\new-report\\home.vue", "mtime": 1758266753126}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\half-part-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\half-part-config\\index.vue", "mtime": 1757468112264}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRUYWJsZVNldHRpbmdMaXN0LCBVcGRhdGVDb21wb25lbnRQYXJ0VGFibGVTZXR0aW5nLCBVcGRhdGVDb2x1bW5TZXR0aW5nIH0gZnJvbSAnQC9hcGkvUFJPL2NvbXBvbmVudC10eXBlJw0KaW1wb3J0IHsgR2V0Qk9NSW5mbyB9IGZyb20gJ0Avdmlld3MvUFJPL2JvbS1zZXR0aW5nL3V0aWxzJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQUk9IYWxmUGFydENvbmZpZycsDQogIGNvbXBvbmVudHM6IHsNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8gYWN0aXZlTmFtZUFwaTogJ3BsbV9wYXJ0c19maWVsZF9wYWdlX2xpc3QnLA0KICAgICAgYWN0aXZlTmFtZTogJycsDQogICAgICBjdXJyZW50Q29kZTogJycsDQogICAgICBwYXJ0TmFtZTogJycsDQogICAgICB0eXBlQ29kZTogJycsDQogICAgICBtYXRlcmlhbENvZGU6ICcnLA0KICAgICAgY3VycmVudEZpbmFsVHlwZUNvZGU6ICcnLA0KICAgICAgdGFiUG9zaXRpb246ICdsZWZ0JywNCiAgICAgIC8vICAgdGFiTGlzdDogWw0KICAgICAgLy8gICAgIHsNCiAgICAgIC8vICAgICAgIGxhYmVsOiAn6Zu25Lu25a2X5q6157u05oqkJywNCiAgICAgIC8vICAgICAgIHZhbHVlOiAncGxtX3BhcnRzX2ZpZWxkX3BhZ2VfbGlzdCcNCiAgICAgIC8vICAgICB9LA0KICAgICAgLy8gICAgIHsNCiAgICAgIC8vICAgICAgIGxhYmVsOiAn6Zu25Lu2566h55CG5YiX6KGoJywNCiAgICAgIC8vICAgICAgIHZhbHVlOiAncGxtX3BhcnRzX3BhZ2VfbGlzdCcNCiAgICAgIC8vICAgICB9LA0KICAgICAgLy8gICAgIHsNCiAgICAgIC8vICAgICAgIGxhYmVsOiAn6Zu25Lu25rex5YyW5riF5Y2VJywNCiAgICAgIC8vICAgICAgIHZhbHVlOiAncGxtX3BhcnRzX2RldGFpbEltcG9ydCcNCiAgICAgIC8vICAgICB9LA0KICAgICAgLy8gICAgIHsNCiAgICAgIC8vICAgICAgIGxhYmVsOiAn55Sf5Lqn6K+m5oOF5YiX6KGoJywNCiAgICAgIC8vICAgICAgIHZhbHVlOiAncGxtX3BhcnRzX21vZGVsSW1wb3J0Jw0KICAgICAgLy8gICAgIH0NCiAgICAgIC8vICAgXSwNCiAgICAgIHNlYXJjaFZhbDogJycsDQogICAgICBtYWpvck5hbWU6ICcnLA0KICAgICAgdW5pdDogJycsDQogICAgICBzdGVlbFVuaXQ6ICcnLA0KICAgICAgdGVtcGxhdGVMaXN0OiBbXSwNCiAgICAgIHRlbXBsYXRlTGlzdE5ldzogW10sDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHN5c3RlbUZpZWxkOiBmYWxzZSwNCiAgICAgIGV4cGFuZEZpZWxkOiBmYWxzZSwNCiAgICAgIGJ1c2luZXNzRmllbGQ6IGZhbHNlDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIHRhYkxpc3QoKSB7DQogICAgICByZXR1cm4gWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMucGFydE5hbWUgKyAn5a2X5q6157u05oqkJywNCiAgICAgICAgICB2YWx1ZTogYHBsbV9sZXZlbCR7dGhpcy5sZXZlbH1fZmllbGRfcGFnZV9saXN0YA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMucGFydE5hbWUgKyAn566h55CG5YiX6KGoJywNCiAgICAgICAgICB2YWx1ZTogYHBsbV9sZXZlbCR7dGhpcy5sZXZlbH1fcGFnZV9saXN0YA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMucGFydE5hbWUgKyAn5rex5YyW5riF5Y2VJywNCiAgICAgICAgICB2YWx1ZTogYHBsbV9sZXZlbCR7dGhpcy5sZXZlbH1fZGV0YWlsSW1wb3J0YA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfnlJ/kuqfor6bmg4XliJfooagnLA0KICAgICAgICAgIHZhbHVlOiBgcGxtX2xldmVsJHt0aGlzLmxldmVsfV9tb2RlbEltcG9ydGANCiAgICAgICAgfQ0KICAgICAgXQ0KICAgIH0sDQogICAgYWN0aXZlTmFtZUFwaSgpIHsNCiAgICAgIHJldHVybiBgcGxtX2xldmVsJHt0aGlzLmxldmVsfV9maWVsZF9wYWdlX2xpc3RgDQogICAgfQ0KDQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5sZXZlbCA9ICt0aGlzLiRyb3V0ZS5xdWVyeS5sZXZlbCB8fCAyDQogICAgdGhpcy50eXBlQ29kZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnR5cGVDb2RlIHx8ICdTdGVlbCcNCiAgICB0aGlzLm1hdGVyaWFsQ29kZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5Lm1hdGVyaWFsQ29kZSB8fCAnU3RydWN0dXJhbEFzJw0KICAgIHRoaXMuY3VycmVudEZpbmFsVHlwZUNvZGUgPSB0aGlzLnR5cGVDb2RlDQogICAgdGhpcy5jdXJyZW50Q29kZSA9IGBwbG1fbGV2ZWwke3RoaXMubGV2ZWx9X2ZpZWxkX3BhZ2VfbGlzdGANCiAgICB0aGlzLmFjdGl2ZU5hbWUgPSB0aGlzLmN1cnJlbnRDb2RlDQogICAgY29uc29sZS5sb2coJ2N1cnJlbnRDb2RlJywgdGhpcy5jdXJyZW50Q29kZSkNCiAgICBjb25zb2xlLmxvZygndGFiTGlzdCcsIEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy50YWJMaXN0KSkpDQogICAgY29uc29sZS5sb2coJ3RoaXMudGFiTGlzdCcsIHRoaXMudGFiTGlzdFswXS52YWx1ZSA9PT0gdGhpcy5jdXJyZW50Q29kZSkNCiAgfSwNCiAgYXN5bmMgbW91bnRlZCgpIHsNCiAgICB0aGlzLm1ham9yTmFtZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5Lm5hbWUgfHwgJ+mSoue7k+aehCcNCiAgICB0aGlzLnVuaXQgPSB0aGlzLiRyb3V0ZS5xdWVyeS51bml0IHx8ICd0Jw0KICAgIHRoaXMuc3RlZWxVbml0ID0gdGhpcy4kcm91dGUucXVlcnkuc3RlZWxfdW5pdCB8fCAna2cnDQoNCiAgICB0aGlzLkdldFRhYmxlU2V0dGluZ0xpc3RGbigpDQogIH0sDQogIG1ldGhvZHM6IHsNCg0KICAgIGNoYW5nZVN0YXR1cygkZXZlbnQsIGlkKSB7DQogICAgICBjb25zdCBkaXNwbGF5TmFtZSA9IHRoaXMudGVtcGxhdGVMaXN0LmZpbmQoKGl0ZW0pID0+IHsgcmV0dXJuIGl0ZW0uSWQgPT0gaWQgfSkuRGlzcGxheV9OYW1lDQogICAgICBpZiAoZGlzcGxheU5hbWUgPT0gJycgJiYgJGV2ZW50ID09IHRydWUpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IHR5cGU6ICdlcnJvcicsIG1lc3NhZ2U6ICfor7flhYjloavlhpnlrZfmrrXlkI0nIH0pDQogICAgICAgIHRoaXMudGVtcGxhdGVMaXN0Lm1hcCgoaXRlbSkgPT4gew0KICAgICAgICAgIGlmIChpdGVtLklkID09IGlkKSB7DQogICAgICAgICAgICBpdGVtLklzX0VuYWJsZWQgPSBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQ2xpY2sodGFiLCBldmVudCkgew0KICAgICAgY29uc29sZS5sb2coJ3RhYicsIHRhYi5uYW1lKQ0KICAgICAgdGhpcy5jdXJyZW50Q29kZSA9IHRhYi5uYW1lDQogICAgICB0aGlzLkdldFRhYmxlU2V0dGluZ0xpc3RGbigpDQogICAgfSwNCiAgICBzZWFyY2hWYWx1ZSgpIHsNCiAgICAgIGlmICghdGhpcy5zZWFyY2hWYWwpIHsNCiAgICAgICAgdGhpcy50ZW1wbGF0ZUxpc3ROZXcgPSB0aGlzLnRlbXBsYXRlTGlzdA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc3QgZmlsdGVyTGlzdCA9IFtdDQogICAgICAgIHRoaXMudGVtcGxhdGVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpZiAoaXRlbS5EaXNwbGF5X05hbWUuc2VhcmNoKG5ldyBSZWdFeHAodGhpcy5zZWFyY2hWYWwsICdpZycpKSAhPT0gLTEpIHsNCiAgICAgICAgICAgIGZpbHRlckxpc3QucHVzaChpdGVtKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy50ZW1wbGF0ZUxpc3ROZXcgPSBmaWx0ZXJMaXN0DQogICAgICB9DQogICAgfSwNCg0KICAgIHNhdmVNb2RpZnlDaGFuZ2VzRm4oKSB7DQogICAgICBpZiAodGhpcy5hY3RpdmVOYW1lID09IHRoaXMuYWN0aXZlTmFtZUFwaSkgew0KICAgICAgICB0aGlzLlVwZGF0ZUNvbXBvbmVudFBhcnRUYWJsZVNldHRpbmcoKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5VcGRhdGVDb2x1bW5TZXR0aW5nKCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgYXN5bmMgVXBkYXRlQ29sdW1uU2V0dGluZygpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IFVwZGF0ZUNvbHVtblNldHRpbmcodGhpcy50ZW1wbGF0ZUxpc3QpDQogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IHR5cGU6ICdzdWNjZXNzJywgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyB0eXBlOiAnZXJyb3InLCBtZXNzYWdlOiByZXMuTWVzc2FnZSB9KQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBhc3luYyBVcGRhdGVDb21wb25lbnRQYXJ0VGFibGVTZXR0aW5nKCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgVXBkYXRlQ29tcG9uZW50UGFydFRhYmxlU2V0dGluZyh0aGlzLnRlbXBsYXRlTGlzdCkNCiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgdHlwZTogJ3N1Y2Nlc3MnLCBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJyB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IHR5cGU6ICdlcnJvcicsIG1lc3NhZ2U6IHJlcy5NZXNzYWdlIH0pDQogICAgICB9DQogICAgfSwNCg0KICAgIGFzeW5jIEdldFRhYmxlU2V0dGluZ0xpc3RGbigpIHsNCiAgICAgIGxldCBkYXRhID0ge30NCiAgICAgIGlmICh0aGlzLmFjdGl2ZU5hbWUgPT0gdGhpcy5hY3RpdmVOYW1lQXBpKSB7DQogICAgICAgIGRhdGEgPSB7IElzQ29tcG9uZW50OiBmYWxzZSwgUHJvZmVzc2lvbmFsQ29kZTogdGhpcy5jdXJyZW50RmluYWxUeXBlQ29kZSB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICBkYXRhID0geyBJc0NvbXBvbmVudDogZmFsc2UsIFByb2Zlc3Npb25hbENvZGU6IHRoaXMuY3VycmVudEZpbmFsVHlwZUNvZGUsIFR5cGVDb2RlOiB0aGlzLmN1cnJlbnRDb2RlICsgJywnICsgdGhpcy5jdXJyZW50RmluYWxUeXBlQ29kZSB9DQogICAgICB9DQogICAgICBkYXRhLkxldmVsID0gdGhpcy5sZXZlbA0KICAgICAgY29uc3QgeyBsaXN0LCBjdXJyZW50UGFyZW50Qk9NSW5mbywgY3VycmVudEJPTUluZm8gfSA9IGF3YWl0IEdldEJPTUluZm8odGhpcy5sZXZlbCkNCiAgICAgIGlmICh0aGlzLmxldmVsIDwgMSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgdHlwZTogJ2Vycm9yJywgbWVzc2FnZTogJ+ezu+e7n+Wxgue6p+mFjee9rumUmeivrycgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBjb25zdCBwYXJ0TmFtZSA9IGN1cnJlbnRCT01JbmZvPy5EaXNwbGF5X05hbWUgfHwgJycNCiAgICAgIGNvbnN0IHBhcnRQYXJlbnQgPSBjdXJyZW50UGFyZW50Qk9NSW5mbz8uRGlzcGxheV9OYW1lIHx8ICcnDQogICAgICB0aGlzLnBhcnROYW1lID0gcGFydE5hbWUNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldFRhYmxlU2V0dGluZ0xpc3QoZGF0YSkNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIHRoaXMudGVtcGxhdGVMaXN0ID0gcmVzLkRhdGEgfHwgW10NCiAgICAgICAgaWYgKHRoaXMudGVtcGxhdGVMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLnRlbXBsYXRlTGlzdC5mb3JFYWNoKHYgPT4gew0KICAgICAgICAgICAgaWYgKHYuQ29kZSA9PT0gJ0NvZGUnKSB7DQogICAgICAgICAgICAgIHYuRGlzcGxheV9OYW1lID0gcGFydE5hbWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmICh2LkNvZGUgPT09ICdDb21wb25lbnRfQ29kZScpIHsNCiAgICAgICAgICAgICAgdi5EaXNwbGF5X05hbWUgPSBwYXJ0UGFyZW50DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnRlbXBsYXRlTGlzdE5ldyA9IHRoaXMudGVtcGxhdGVMaXN0DQogICAgICAgIHRoaXMuc3lzdGVtRmllbGQgPSB0aGlzLnRlbXBsYXRlTGlzdC5zb21lKGl0ZW0gPT4geyByZXR1cm4gaXRlbS5Db2x1bW5fVHlwZSA9PSAwIH0pDQogICAgICAgIHRoaXMuZXhwYW5kRmllbGQgPSB0aGlzLnRlbXBsYXRlTGlzdC5zb21lKGl0ZW0gPT4geyByZXR1cm4gaXRlbS5Db2x1bW5fVHlwZSA9PSAxIH0pDQogICAgICAgIHRoaXMuYnVzaW5lc3NGaWVsZCA9IHRoaXMudGVtcGxhdGVMaXN0LnNvbWUoaXRlbSA9PiB7IHJldHVybiBpdGVtLkNvbHVtbl9UeXBlID09IDIgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyB0eXBlOiAnZXJyb3InLCBtZXNzYWdlOiByZXMuTWVzc2FnZSB9KQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoKA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/half-part-config", "sourcesContent": ["<!-- 半构件 -->\r\n<template>\r\n  <div>\r\n    <div class=\"page-container\">\r\n      <div class=\"top-wrapper\">\r\n        <!-- <div class=\"title\">零件模板配置：</div> -->\r\n        <div class=\"info\">\r\n          <template v-if=\"!!majorName\">\r\n            <div class=\"title\">当前专业：</div>\r\n            <div class=\"value\">{{ majorName }}</div>\r\n          </template>\r\n          <template v-if=\"!!unit\">\r\n            <div class=\"title\">统计单位：</div>\r\n            <div class=\"value\">{{ unit }}</div>\r\n          </template>\r\n          <template v-if=\"!!steelUnit\">\r\n            <div class=\"title\">构件单位：</div>\r\n            <div class=\"value\">{{ steelUnit }}</div>\r\n          </template>\r\n          <template>\r\n            <div class=\"title\">单位统计字段：</div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='Num'\" :key=\"index\" style=\"display: flex;flex-direction: row\">\r\n              {{ item.Display_Name }}\r\n            </div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='Weight'\" :key=\"index+999\" style=\"display: flex;flex-direction: row\">\r\n              *{{ item.Display_Name }}\r\n            </div>\r\n          </template>\r\n        </div>\r\n        <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n          <el-tab-pane v-for=\"(item,index) in tabList\" :key=\"partName+index\" :label=\"item.label\" :name=\"item.value\" />\r\n\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"content-wrapper\" style=\"min-height: calc(100vh - 340px)\">\r\n        <div class=\"right-c\">\r\n          <el-row type=\"flex\" justify=\"space-between\">\r\n            <div class=\"right-c-title\">\r\n              <div class=\"setting-title\">{{ systemField==true ? '系统字段' : businessField==true ? '业务字段' : expandField==true ? '拓展字段' : '' }}</div>\r\n            </div>\r\n            <div style=\"display: flex;flex-direction: row\">\r\n              <span style=\"width:140px;font-size:12px;height:32px; line-height: 32px;display: inline-block;\">字段名称：</span>\r\n              <el-input v-model=\"searchVal\" placeholder=\"请输入字段名称\" clearable />\r\n              <el-button type=\"primary\" style=\"margin-left: 10px\" @click=\"searchValue\">查询</el-button>\r\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"saveModifyChangesFn\">保存设置</el-button>\r\n            </div>\r\n          </el-row>\r\n          <el-form label-width=\"120px\" style=\"margin-top: 24px\">\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==0\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" :disabled=\"false\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"businessField==true && systemField==true\" class=\"setting-title\">业务字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==2\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"expandField==true\" class=\"setting-title\">拓展字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==1\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetTableSettingList, UpdateComponentPartTableSetting, UpdateColumnSetting } from '@/api/PRO/component-type'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROHalfPartConfig',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // activeNameApi: 'plm_parts_field_page_list',\r\n      activeName: '',\r\n      currentCode: '',\r\n      partName: '',\r\n      typeCode: '',\r\n      materialCode: '',\r\n      currentFinalTypeCode: '',\r\n      tabPosition: 'left',\r\n      //   tabList: [\r\n      //     {\r\n      //       label: '零件字段维护',\r\n      //       value: 'plm_parts_field_page_list'\r\n      //     },\r\n      //     {\r\n      //       label: '零件管理列表',\r\n      //       value: 'plm_parts_page_list'\r\n      //     },\r\n      //     {\r\n      //       label: '零件深化清单',\r\n      //       value: 'plm_parts_detailImport'\r\n      //     },\r\n      //     {\r\n      //       label: '生产详情列表',\r\n      //       value: 'plm_parts_modelImport'\r\n      //     }\r\n      //   ],\r\n      searchVal: '',\r\n      majorName: '',\r\n      unit: '',\r\n      steelUnit: '',\r\n      templateList: [],\r\n      templateListNew: [],\r\n      loading: false,\r\n      systemField: false,\r\n      expandField: false,\r\n      businessField: false\r\n    }\r\n  },\r\n  computed: {\r\n    tabList() {\r\n      return [\r\n        {\r\n          label: this.partName + '字段维护',\r\n          value: `plm_level${this.level}_field_page_list`\r\n        },\r\n        {\r\n          label: this.partName + '管理列表',\r\n          value: `plm_level${this.level}_page_list`\r\n        },\r\n        {\r\n          label: this.partName + '深化清单',\r\n          value: `plm_level${this.level}_detailImport`\r\n        },\r\n        {\r\n          label: '生产详情列表',\r\n          value: `plm_level${this.level}_modelImport`\r\n        }\r\n      ]\r\n    },\r\n    activeNameApi() {\r\n      return `plm_level${this.level}_field_page_list`\r\n    }\r\n\r\n  },\r\n  created() {\r\n    this.level = +this.$route.query.level || 2\r\n    this.typeCode = this.$route.query.typeCode || 'Steel'\r\n    this.materialCode = this.$route.query.materialCode || 'StructuralAs'\r\n    this.currentFinalTypeCode = this.typeCode\r\n    this.currentCode = `plm_level${this.level}_field_page_list`\r\n    this.activeName = this.currentCode\r\n    console.log('currentCode', this.currentCode)\r\n    console.log('tabList', JSON.parse(JSON.stringify(this.tabList)))\r\n    console.log('this.tabList', this.tabList[0].value === this.currentCode)\r\n  },\r\n  async mounted() {\r\n    this.majorName = this.$route.query.name || '钢结构'\r\n    this.unit = this.$route.query.unit || 't'\r\n    this.steelUnit = this.$route.query.steel_unit || 'kg'\r\n\r\n    this.GetTableSettingListFn()\r\n  },\r\n  methods: {\r\n\r\n    changeStatus($event, id) {\r\n      const displayName = this.templateList.find((item) => { return item.Id == id }).Display_Name\r\n      if (displayName == '' && $event == true) {\r\n        this.$message({ type: 'error', message: '请先填写字段名' })\r\n        this.templateList.map((item) => {\r\n          if (item.Id == id) {\r\n            item.Is_Enabled = false\r\n          }\r\n          return item\r\n        })\r\n      }\r\n    },\r\n    handleClick(tab, event) {\r\n      console.log('tab', tab.name)\r\n      this.currentCode = tab.name\r\n      this.GetTableSettingListFn()\r\n    },\r\n    searchValue() {\r\n      if (!this.searchVal) {\r\n        this.templateListNew = this.templateList\r\n      } else {\r\n        const filterList = []\r\n        this.templateList.map(item => {\r\n          if (item.Display_Name.search(new RegExp(this.searchVal, 'ig')) !== -1) {\r\n            filterList.push(item)\r\n          }\r\n        })\r\n        this.templateListNew = filterList\r\n      }\r\n    },\r\n\r\n    saveModifyChangesFn() {\r\n      if (this.activeName == this.activeNameApi) {\r\n        this.UpdateComponentPartTableSetting()\r\n      } else {\r\n        this.UpdateColumnSetting()\r\n      }\r\n    },\r\n\r\n    async UpdateColumnSetting() {\r\n      this.loading = true\r\n      const res = await UpdateColumnSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async UpdateComponentPartTableSetting() {\r\n      this.loading = true\r\n      const res = await UpdateComponentPartTableSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async GetTableSettingListFn() {\r\n      let data = {}\r\n      if (this.activeName == this.activeNameApi) {\r\n        data = { IsComponent: false, ProfessionalCode: this.currentFinalTypeCode }\r\n      } else {\r\n        data = { IsComponent: false, ProfessionalCode: this.currentFinalTypeCode, TypeCode: this.currentCode + ',' + this.currentFinalTypeCode }\r\n      }\r\n      data.Level = this.level\r\n      const { list, currentParentBOMInfo, currentBOMInfo } = await GetBOMInfo(this.level)\r\n      if (this.level < 1) {\r\n        this.$message({ type: 'error', message: '系统层级配置错误' })\r\n        return\r\n      }\r\n      const partName = currentBOMInfo?.Display_Name || ''\r\n      const partParent = currentParentBOMInfo?.Display_Name || ''\r\n      this.partName = partName\r\n      const res = await GetTableSettingList(data)\r\n      if (res.IsSucceed) {\r\n        this.templateList = res.Data || []\r\n        if (this.templateList.length > 0) {\r\n          this.templateList.forEach(v => {\r\n            if (v.Code === 'Code') {\r\n              v.Display_Name = partName\r\n            }\r\n            if (v.Code === 'Component_Code') {\r\n              v.Display_Name = partParent\r\n            }\r\n          })\r\n        }\r\n        this.templateListNew = this.templateList\r\n        this.systemField = this.templateList.some(item => { return item.Column_Type == 0 })\r\n        this.expandField = this.templateList.some(item => { return item.Column_Type == 1 })\r\n        this.businessField = this.templateList.some(item => { return item.Column_Type == 2 })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n  <style lang=\"scss\" scoped>\r\n    .page-container{\r\n      margin:16px;\r\n      box-sizing: border-box;\r\n      .top-wrapper{\r\n        background: #fff;\r\n        padding:16px;\r\n        box-sizing: border-box;\r\n        .title{\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color:#333333;\r\n        }\r\n        .info{\r\n          font-size: 14px;\r\n          margin:8px 0 24px 0;\r\n          display: flex;\r\n          flex-direction: row;\r\n          .title{\r\n            font-size: 14px;\r\n            color: #999999;\r\n          }\r\n          .value{\r\n            color: #333333;\r\n            margin-right: 24px;\r\n          }\r\n        }\r\n      }\r\n      .content-wrapper{\r\n        margin-top:16px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        .left-c{\r\n          width: 160px;\r\n          background: #fff;\r\n          margin-right: 16px;\r\n        }\r\n        .right-c{\r\n          background: #fff;\r\n          width: 100%;\r\n          padding: 16px 24px;\r\n          box-sizing: border-box;\r\n        }\r\n      }\r\n    }\r\n    ::v-deep.el-tabs--left .el-tabs__nav-wrap.is-left::after{\r\n      left:0\r\n    }\r\n    ::v-deep.el-tabs--left .el-tabs__active-bar.is-left{\r\n      left: 0;\r\n    }\r\n    .setting-title {\r\n      font-weight: 400;\r\n      color: #1f2f3d;\r\n      margin: 30px 0 20px;\r\n      font-size: 22px;\r\n    }\r\n    .setting-title:first-child {\r\n      margin: 0;\r\n    }\r\n  </style>\r\n"]}]}
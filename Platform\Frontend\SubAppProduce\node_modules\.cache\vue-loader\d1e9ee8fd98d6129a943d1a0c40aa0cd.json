{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\components\\ProjectData.vue?vue&type=template&id=649f8757&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\components\\ProjectData.vue", "mtime": 1757468113454}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
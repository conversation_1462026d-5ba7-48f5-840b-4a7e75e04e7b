{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\plan\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\plan\\index.vue", "mtime": 1757468128085}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRQcm9qZWN0UGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvLXNjaGVkdWxlcycNCmltcG9ydCBhZGRSb3V0ZXJQYWdlIGZyb20gJ0AvbWl4aW5zL2FkZC1yb3V0ZXItcGFnZScNCmltcG9ydCB7DQogIENhbmNlbEZsb3csDQogIEdldFByb2plY3RTZW5kaW5nQWxsQ291bnQsDQogIFN1Ym1pdFByb2plY3RTZW5kaW5nLA0KICBXaXRoZHJhd0RyYWZ0DQp9IGZyb20gJ0AvYXBpL1BSTy9jb21wb25lbnQtc3RvY2stb3V0Jw0KaW1wb3J0IHsgR2V0R3JpZEJ5Q29kZSB9IGZyb20gJ0AvYXBpL3N5cycNCmltcG9ydCB7IHBhcnNlVGltZSB9IGZyb20gJ0AvdXRpbHMnDQppbXBvcnQgRHluYW1pY0RhdGFUYWJsZSBmcm9tICdAL2NvbXBvbmVudHMvRHluYW1pY0RhdGFUYWJsZS9EeW5hbWljRGF0YVRhYmxlJw0KaW1wb3J0IHsgRGVsZXRlT3V0UGxhbiwgRXhwb3J0T3V0UGxhbkxpc3QsIEdldE91dFBsYW5QYWdlTGlzdCwgU3VibWl0T3V0UGxhbiB9IGZyb20gJ0AvYXBpL1BSTy9zaGlwLXBsYW4nDQppbXBvcnQgRXhwb3J0Q3VzdG9tUmVwb3J0IGZyb20gJ0AvY29tcG9uZW50cy9FeHBvcnRDdXN0b21SZXBvcnQvaW5kZXgudnVlJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdTaGlwUGxhbicsDQogIGNvbXBvbmVudHM6IHsgRXhwb3J0Q3VzdG9tUmVwb3J0LCBEeW5hbWljRGF0YVRhYmxlIH0sDQogIG1peGluczogW2FkZFJvdXRlclBhZ2VdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBmb3JtOiB7DQogICAgICAgIGRhdGVSYW5nZTogW10sDQogICAgICAgIFN5c19Qcm9qZWN0X0lkOiAnJywNCiAgICAgICAgQ29kZTogJycsDQogICAgICAgIFN0YXR1czogJycNCiAgICAgIH0sDQogICAgICBwaWNrZXJPcHRpb25zOiB7DQogICAgICAgIHNob3J0Y3V0czogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRleHQ6ICfku4rlpKknLA0KICAgICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKQ0KICAgICAgICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCkNCiAgICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogMSkNCiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGV4dDogJ+acgOi/keS4gOWRqCcsDQogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKQ0KICAgICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KQ0KICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0ZXh0OiAn5pyA6L+R5LiA5Liq5pyIJywNCiAgICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCkNCiAgICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDMwKQ0KICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgcHJvamVjdHM6IFtdLA0KICAgICAgc3RhdHVzRGljdDogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfojYnnqL8nLA0KICAgICAgICAgIHZhbHVlOiAnMScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn6L+b6KGM5LitJywNCiAgICAgICAgICB2YWx1ZTogJzMnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+W3suWujOaIkCcsDQogICAgICAgICAgdmFsdWU6ICc0Jw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgZm9ybTI6IHsNCiAgICAgICAgUHJvamVjdElkOiAnJw0KICAgICAgfSwNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgUHJvamVjdElkOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqScsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dDQogICAgICB9LA0KICAgICAgYWRkUGFnZUFycmF5OiBbDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9hZGQnLA0KICAgICAgICAgIGhpZGRlbjogdHJ1ZSwNCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnQC92aWV3cy9QUk8vc2hpcG1lbnQvcGxhbi9hZGQnKSwNCiAgICAgICAgICBuYW1lOiAnUFJPU2hpcFBsYW5BZGQnLA0KICAgICAgICAgIG1ldGE6IHsgdGl0bGU6ICfmlrDlu7rlj5HotKforqHliJLljZUnIH0NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHBhdGg6IHRoaXMuJHJvdXRlLnBhdGggKyAnL2VkaXQnLA0KICAgICAgICAgIGhpZGRlbjogdHJ1ZSwNCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnQC92aWV3cy9QUk8vc2hpcG1lbnQvcGxhbi9hZGQnKSwNCiAgICAgICAgICBuYW1lOiAnUFJPU2hpcFBsYW5FZGl0JywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn57yW6L6R5Y+R6LSn6K6h5YiS5Y2VJyB9DQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9kZXRhaWwnLA0KICAgICAgICAgIGhpZGRlbjogdHJ1ZSwNCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnQC92aWV3cy9QUk8vc2hpcG1lbnQvcGxhbi9hZGQnKSwNCiAgICAgICAgICBuYW1lOiAnUFJPU2hpcFBsYW5EZXRhaWwnLA0KICAgICAgICAgIG1ldGE6IHsgdGl0bGU6ICflj5HotKforqHliJLor6bmg4UnIH0NCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIHRiRGF0YTogW10sDQogICAgICB0b3RhbDogMCwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBzZWxlY3Rpb25zOiBbXSwNCiAgICAgIHRvdGFsRGF0YTogew0KICAgICAgICBBbGxzdGVlbGFtb3VudDogMCwNCiAgICAgICAgQWxsc3RlZWx3ZWlnaHQ6IDANCiAgICAgIH0sDQogICAgICBwYWdlSW5mbzogew0KICAgICAgICBQYWdlOiAxLA0KICAgICAgICBQYWdlU2l6ZTogMjANCiAgICAgIH0sDQogICAgICB0YkNvbmZpZzogew0KICAgICAgICBQYWdlcl9BbGlnbjogJ3JpZ2h0JywNCiAgICAgICAgT3BfV2lkdGg6IDI0MA0KICAgICAgfSwNCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgZXhwb3J0TG9hZGluZzogZmFsc2UNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRQcm9qZWN0TGlzdCgpDQogICAgdGhpcy5nZXRUYWJsZUNvbmZpZygpDQogICAgdGhpcy5mZXRjaERhdGEoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc2VsZWN0Q2hhbmdlKHsgc2VsZWN0aW9uLCByb3cgfSkgew0KICAgICAgdGhpcy5zZWxlY3Rpb25zID0gc2VsZWN0aW9uDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3RBbGwoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLnNlbGVjdGlvbnMgPSBzZWxlY3Rpb24NCiAgICB9LA0KICAgIHNlYXJjaCgpIHsNCiAgICAgIHRoaXMucGFnZUluZm8uUGFnZSA9IDENCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICB9LA0KICAgIGV4cG9ydEV4Y2VsKCkgew0KICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgZm9ybSA9IHsNCiAgICAgICAgLi4udGhpcy5mb3JtLA0KICAgICAgICAuLi50aGlzLnBhZ2VJbmZvDQogICAgICB9DQogICAgICBkZWxldGUgZm9ybVsnZGF0ZVJhbmdlJ10NCiAgICAgIHRoaXMuZm9ybS5kYXRlUmFuZ2UgPSB0aGlzLmZvcm0uZGF0ZVJhbmdlIHx8IFtdDQogICAgICBmb3JtLlBsYW5fRGF0ZV9CZWdpbiA9IHBhcnNlVGltZSh0aGlzLmZvcm0uZGF0ZVJhbmdlWzBdKQ0KICAgICAgICA/IHBhcnNlVGltZSh0aGlzLmZvcm0uZGF0ZVJhbmdlWzBdKQ0KICAgICAgICA6ICcnDQogICAgICBmb3JtLlBsYW5fRGF0ZV9FbmQgPSBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVsxXSkNCiAgICAgICAgPyBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVsxXSkNCiAgICAgICAgOiAnJw0KICAgICAgRXhwb3J0T3V0UGxhbkxpc3QoZm9ybSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHdpbmRvdy5vcGVuKHRoaXMuJGJhc2VVcmwgKyByZXMuRGF0YSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLmV4cG9ydExvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGdldFByb2plY3RMaXN0KCkgew0KICAgICAgdGhpcy50cmVlTG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMudGFibGVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0UHJvamVjdFBhZ2VMaXN0KHsgUGFnZVNpemU6IC0xIH0pDQogICAgICB0aGlzLnByb2plY3RzID0gcmVzLkRhdGEuRGF0YQ0KICAgICAgaWYgKCFyZXMuRGF0YS5EYXRhLmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmmoLml6Dpobnnm64nKQ0KICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy50YWJsZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5wcm9qZWN0SWQgPSByZXMuRGF0YS5EYXRhWzBdLlN5c19Qcm9qZWN0X0lkDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVDbG9zZSgpIHsNCiAgICAgIHRoaXMuJHJlZnMuZm9ybTIucmVzZXRGaWVsZHMoKQ0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICB9LA0KICAgIHJlc2V0Rm9ybTIoZm9ybU5hbWUpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICB0aGlzLiRyZWZzW2Zvcm1OYW1lXS5yZXNldEZpZWxkcygpDQogICAgfSwNCiAgICBzdWJtaXRGb3JtMihmb3JtTmFtZSkgew0KICAgICAgdGhpcy4kcmVmc1tmb3JtTmFtZV0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGNvbnN0IHsgUHJvamVjdElkIH0gPSB0aGlzLmZvcm0yDQogICAgICAgICAgY29uc3Qgew0KICAgICAgICAgICAgTmFtZSwNCiAgICAgICAgICAgIElkLA0KICAgICAgICAgICAgQ29kZSwNCiAgICAgICAgICAgIEFkZHJlc3MsDQogICAgICAgICAgICBSZWNlaXZlciwNCiAgICAgICAgICAgIFJlY2VpdmVyX1RlbCwNCiAgICAgICAgICAgIFN5c19Qcm9qZWN0X0lkLA0KICAgICAgICAgICAgUmVjZWl2ZV9Vc2VyTmFtZQ0KICAgICAgICAgIH0gPSB0aGlzLnByb2plY3RzLmZpbmQoKHYpID0+IHYuSWQgPT09IHRoaXMuZm9ybTIuUHJvamVjdElkKQ0KICAgICAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgICAgICBQcm9qZWN0SWQsDQogICAgICAgICAgICBJZCwNCiAgICAgICAgICAgIE5hbWUsDQogICAgICAgICAgICBDb2RlLA0KICAgICAgICAgICAgQWRkcmVzcywNCiAgICAgICAgICAgIFJlY2VpdmVyLA0KICAgICAgICAgICAgUmVjZWl2ZXJfVGVsLA0KICAgICAgICAgICAgU3lzX1Byb2plY3RfSWQsDQogICAgICAgICAgICBSZWNlaXZlX1VzZXJOYW1lLA0KICAgICAgICAgICAgUHJvZmVzc2lvbmFsVHlwZTogdGhpcy5Qcm9mZXNzaW9uYWxUeXBlDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgICAgIG5hbWU6ICdQUk9TaGlwUGxhbkFkZCcsDQogICAgICAgICAgICBxdWVyeTogew0KICAgICAgICAgICAgICBwZ19yZWRpcmVjdDogdGhpcy4kcm91dGUubmFtZSwNCiAgICAgICAgICAgICAgcDogZW5jb2RlVVJJQ29tcG9uZW50KEpTT04uc3RyaW5naWZ5KGRhdGEpKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLiRyZWZzLmZvcm0yLnJlc2V0RmllbGRzKCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygnZXJyb3Igc3VibWl0ISEnKQ0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlRWRpdChpZCwgeyBQcm9qZWN0X05hbWUgfSkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBuYW1lOiAnUFJPU2hpcFBsYW5FZGl0JywNCiAgICAgICAgcXVlcnk6IHsgcGdfcmVkaXJlY3Q6IHRoaXMuJHJvdXRlLm5hbWUsIGlkLCB0eXBlOiAnZWRpdCcsIHA6IEpTT04uc3RyaW5naWZ5KHsgTmFtZTogUHJvamVjdF9OYW1lIH0pIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDmkqTlm57oh7PojYnnqL8NCiAgICBoYW5kbGVXaXRoZHJhdyhpZCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn5pKk5Zue6Iez6I2J56i/LCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgV2l0aGRyYXdEcmFmdCh7DQogICAgICAgICAgICBpZDogaWQNCiAgICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfmkqTplIDmiJDlip8nLA0KICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsgfSkNCiAgICB9LA0KICAgIGhhbmRsZVN1YihpZCkgew0KICAgICAgY29uc29sZS5sb2coaWQsICdpZCcpDQogICAgICB0aGlzLiRjb25maXJtKCfmj5DkuqTor6Xlj5HotKforqHliJIsIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICBTdWJtaXRPdXRQbGFuKHsNCiAgICAgICAgICAgIGlkDQogICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5o+Q5Lqk5oiQ5YqfJywNCiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7IH0pDQogICAgfSwNCiAgICBoYW5kbGVEZWwoaWQpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuWIoOmZpOivpeWPkei0p+iuoeWIkj8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBEZWxldGVPdXRQbGFuKHsNCiAgICAgICAgICBpZA0KICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8nLA0KICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJw0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUluZm8oaWQsIHsgUHJvamVjdF9OYW1lIH0pIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgbmFtZTogJ1BST1NoaXBQbGFuRGV0YWlsJywNCiAgICAgICAgcXVlcnk6IHsgcGdfcmVkaXJlY3Q6IHRoaXMuJHJvdXRlLm5hbWUsIGlkLCB0eXBlOiAndmlldycsIHA6IEpTT04uc3RyaW5naWZ5KHsgTmFtZTogUHJvamVjdF9OYW1lIH0pIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVDYW5jZWxGbG93KGluc3RhbmNlSWQpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuaSpOWbnj8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBDYW5jZWxGbG93KHsNCiAgICAgICAgICBpbnN0YW5jZUlkDQogICAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6ICfmk43kvZzmiJDlip8nLA0KICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGZldGNoRGF0YSgpIHsNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgZm9ybSA9IHsNCiAgICAgICAgLi4udGhpcy5mb3JtLA0KICAgICAgICAuLi50aGlzLnBhZ2VJbmZvDQogICAgICB9DQogICAgICBkZWxldGUgZm9ybVsnZGF0ZVJhbmdlJ10NCiAgICAgIHRoaXMuZm9ybS5kYXRlUmFuZ2UgPSB0aGlzLmZvcm0uZGF0ZVJhbmdlIHx8IFtdDQogICAgICBmb3JtLlBsYW5fRGF0ZV9CZWdpbiA9IHBhcnNlVGltZSh0aGlzLmZvcm0uZGF0ZVJhbmdlWzBdKQ0KICAgICAgICA/IHBhcnNlVGltZSh0aGlzLmZvcm0uZGF0ZVJhbmdlWzBdKQ0KICAgICAgICA6ICcnDQogICAgICBmb3JtLlBsYW5fRGF0ZV9FbmQgPSBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVsxXSkNCiAgICAgICAgPyBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVsxXSkNCiAgICAgICAgOiAnJw0KICAgICAgR2V0T3V0UGxhblBhZ2VMaXN0KGZvcm0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMudGJEYXRhID0gcmVzLkRhdGEuRGF0YS5tYXAoKHYpID0+IHsNCiAgICAgICAgICAgIHYuUGxhbl9EYXRlID0gdi5QbGFuX0RhdGUNCiAgICAgICAgICAgICAgPyBwYXJzZVRpbWUobmV3IERhdGUodi5QbGFuX0RhdGUpLCAne3l9LXttfS17ZH0nKQ0KICAgICAgICAgICAgICA6IHYuUGxhbl9EYXRlDQogICAgICAgICAgICByZXR1cm4gdg0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICAgIEdldFByb2plY3RTZW5kaW5nQWxsQ291bnQoeyAuLi5mb3JtIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIC8vIGNvbnNvbGUubG9nKHJlcy5EYXRhLCJyZXMuRGF0YSIpOw0KICAgICAgICAgIHRoaXMudG90YWxEYXRhID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRUYWJsZUNvbmZpZygpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gew0KICAgICAgICBHZXRHcmlkQnlDb2RlKHsNCiAgICAgICAgICBjb2RlOiAnUHJvU2hpcFBsYW5MaXN0Jw0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgY29uc3QgeyBJc1N1Y2NlZWQsIERhdGEsIE1lc3NhZ2UgfSA9IHJlcw0KICAgICAgICAgIGlmIChJc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGlmICghRGF0YSkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn6KGo5qC86YWN572u5LiN5a2Y5ZyoJywNCiAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy50YkNvbmZpZyA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMudGJDb25maWcsIERhdGEuR3JpZCkNCiAgICAgICAgICAgIHRoaXMuY29sdW1ucyA9IChEYXRhLkNvbHVtbkxpc3QuZmlsdGVyKHYgPT4gdi5Jc19EaXNwbGF5KSB8fCBbXSkubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpdGVtLklzX1Jlc2l6YWJsZSA9IHRydWUNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICBpZiAodGhpcy5wYWdlSW5mbykgew0KICAgICAgICAgICAgICB0aGlzLnBhZ2VJbmZvLlBhZ2VTaXplID0gK0RhdGEuR3JpZC5Sb3dfTnVtYmVyDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLmZvcm0uUGFnZVNpemUgPSArRGF0YS5HcmlkLlJvd19OdW1iZXINCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHJlc29sdmUodGhpcy5jb2x1bW5zKQ0KICAgICAgICAgICAgLy8gdGhpcy5mZXRjaERhdGEoKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IE1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVBhZ2VDaGFuZ2UoZSkgew0KICAgICAgaWYgKHRoaXMucGFnZUluZm8pIHsNCiAgICAgICAgdGhpcy5wYWdlSW5mby5QYWdlID0gZS5wYWdlDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmZvcm0uUGFnZSA9IGUucGFnZQ0KICAgICAgfQ0KICAgICAgLy8gY29uc29sZS5sb2codGhpcy5wYWdlSW5mby5QYWdlKTsNCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICB9LA0KICAgIGhhbmRsZVNpemVDaGFuZ2UoZSkgew0KICAgICAgaWYgKHRoaXMucGFnZUluZm8pIHsNCiAgICAgICAgdGhpcy5wYWdlSW5mby5QYWdlID0gMQ0KICAgICAgICB0aGlzLnBhZ2VJbmZvLlBhZ2VTaXplID0gZS5zaXplDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmZvcm0uUGFnZSA9IDENCiAgICAgICAgdGhpcy5mb3JtLlBhZ2VTaXplID0gZS5zaXplDQogICAgICB9DQogICAgICAvLyBjb25zb2xlLmxvZyh0aGlzLnBhZ2VJbmZvKTsNCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/shipment/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div class=\"cs-wrapper\">\r\n      <div class=\"search-x\">\r\n        <el-form inline style=\"display: flex;width: 100%\">\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"dialogVisible = true\">新建</el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              :loading=\"exportLoading\"\r\n              @click=\"exportExcel\"\r\n            >导出</el-button>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <ExportCustomReport code=\"Shipping_plan_template\" name=\"导出派工单\" :ids=\"selections.map(i=>i.Id)\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"发货计划单号\" style=\"margin-left: auto\">\r\n            <el-input v-model=\"form.Code\" placeholder=\"请输入\" clearable style=\"width: 180px\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"项目名称\">\r\n            <el-select v-model=\"form.Sys_Project_Id\" placeholder=\"请选择\" filterable clearable style=\"width: 180px\">\r\n              <el-option\r\n                v-for=\"item in projects\"\r\n                :key=\"item.Sys_Project_Id\"\r\n                :label=\"item.Short_Name\"\r\n                :value=\"item.Sys_Project_Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"计划发货日期\">\r\n            <el-date-picker\r\n              v-model=\"form.dateRange\"\r\n              style=\"width: 300px\"\r\n              type=\"daterange\"\r\n              align=\"right\"\r\n              unlink-panels\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              :picker-options=\"pickerOptions\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\">\r\n            <el-select v-model=\"form.Status\" filterable clearable style=\"width: 100px\">\r\n              <el-option v-for=\"item in statusDict\" :key=\"item.label\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"search\">搜索</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n      </div>\r\n      <div\r\n        v-loading=\"tbLoading\"\r\n        class=\"fff cs-z-tb-wrapper\"\r\n        style=\"flex: 1 1 auto\"\r\n      >\r\n        <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          class=\"cs-plm-dy-table\"\r\n          :columns=\"columns\"\r\n          :config=\"tbConfig\"\r\n          :data=\"tbData\"\r\n          :page=\"pageInfo.Page\"\r\n          :total=\"total\"\r\n          border\r\n          stripe\r\n          @gridPageChange=\"handlePageChange\"\r\n          @gridSizeChange=\"handleSizeChange\"\r\n          @select=\"selectChange\"\r\n          @selectAll=\"handleSelectAll\"\r\n        >\r\n          <template slot=\"op\" slot-scope=\"{ row, index }\">\r\n            <el-button\r\n              v-if=\"[1,-1].includes(row.Status)\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleEdit(row.Id, row)\"\r\n            >编辑</el-button>\r\n            <el-button\r\n              v-if=\"[1,-1].includes(row.Status)\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleSub(row.Id)\"\r\n            >提交</el-button>\r\n            <el-button\r\n              v-if=\"[2,3,4].includes(row.Status)\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleInfo(row.Id,row)\"\r\n            >查看</el-button>\r\n            <el-button\r\n              v-if=\"[2].includes(row.Status)\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleWithdraw(row.Id)\"\r\n            >撤回</el-button>\r\n            <el-button\r\n              v-if=\"[1,-1].includes(row.Status)\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              style=\"color:red\"\r\n              @click=\"handleDel(row.Id)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </dynamic-data-table>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"新增发货计划\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <el-form\r\n        ref=\"form2\"\r\n        :model=\"form2\"\r\n        :rules=\"rules\"\r\n        label-width=\"70px\"\r\n        class=\"demo-ruleForm\"\r\n      >\r\n        <el-form-item label=\"项目\" prop=\"ProjectId\">\r\n          <el-select\r\n            v-model=\"form2.ProjectId\"\r\n            class=\"w100\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in projects\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Short_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item style=\"text-align: right\">\r\n          <el-button @click=\"resetForm2('form2')\">取 消</el-button>\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"submitForm2('form2')\"\r\n          >确 定</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProjectPageList } from '@/api/PRO/pro-schedules'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport {\r\n  CancelFlow,\r\n  GetProjectSendingAllCount,\r\n  SubmitProjectSending,\r\n  WithdrawDraft\r\n} from '@/api/PRO/component-stock-out'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { parseTime } from '@/utils'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport { DeleteOutPlan, ExportOutPlanList, GetOutPlanPageList, SubmitOutPlan } from '@/api/PRO/ship-plan'\r\nimport ExportCustomReport from '@/components/ExportCustomReport/index.vue'\r\n\r\nexport default {\r\n  name: 'ShipPlan',\r\n  components: { ExportCustomReport, DynamicDataTable },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      form: {\r\n        dateRange: [],\r\n        Sys_Project_Id: '',\r\n        Code: '',\r\n        Status: ''\r\n      },\r\n      pickerOptions: {\r\n        shortcuts: [\r\n          {\r\n            text: '今天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '最近一周',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '最近一个月',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      projects: [],\r\n      statusDict: [\r\n        {\r\n          label: '草稿',\r\n          value: '1'\r\n        },\r\n        {\r\n          label: '进行中',\r\n          value: '3'\r\n        },\r\n        {\r\n          label: '已完成',\r\n          value: '4'\r\n        }\r\n      ],\r\n      form2: {\r\n        ProjectId: ''\r\n      },\r\n      dialogVisible: false,\r\n      rules: {\r\n        ProjectId: [{ required: true, message: '请选择', trigger: 'change' }]\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/plan/add'),\r\n          name: 'PROShipPlanAdd',\r\n          meta: { title: '新建发货计划单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/edit',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/plan/add'),\r\n          name: 'PROShipPlanEdit',\r\n          meta: { title: '编辑发货计划单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/detail',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/plan/add'),\r\n          name: 'PROShipPlanDetail',\r\n          meta: { title: '发货计划详情' }\r\n        }\r\n      ],\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      selections: [],\r\n      totalData: {\r\n        Allsteelamount: 0,\r\n        Allsteelweight: 0\r\n      },\r\n      pageInfo: {\r\n        Page: 1,\r\n        PageSize: 20\r\n      },\r\n      tbConfig: {\r\n        Pager_Align: 'right',\r\n        Op_Width: 240\r\n      },\r\n      columns: [],\r\n      exportLoading: false\r\n    }\r\n  },\r\n  created() {\r\n    this.getProjectList()\r\n    this.getTableConfig()\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    selectChange({ selection, row }) {\r\n      this.selections = selection\r\n    },\r\n    handleSelectAll(selection) {\r\n      this.selections = selection\r\n    },\r\n    search() {\r\n      this.pageInfo.Page = 1\r\n      this.fetchData()\r\n    },\r\n    exportExcel() {\r\n      this.exportLoading = true\r\n      const form = {\r\n        ...this.form,\r\n        ...this.pageInfo\r\n      }\r\n      delete form['dateRange']\r\n      this.form.dateRange = this.form.dateRange || []\r\n      form.Plan_Date_Begin = parseTime(this.form.dateRange[0])\r\n        ? parseTime(this.form.dateRange[0])\r\n        : ''\r\n      form.Plan_Date_End = parseTime(this.form.dateRange[1])\r\n        ? parseTime(this.form.dateRange[1])\r\n        : ''\r\n      ExportOutPlanList(form).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(this.$baseUrl + res.Data)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.exportLoading = false\r\n      })\r\n    },\r\n    async getProjectList() {\r\n      this.treeLoading = true\r\n      this.tableLoading = true\r\n      const res = await GetProjectPageList({ PageSize: -1 })\r\n      this.projects = res.Data.Data\r\n      if (!res.Data.Data.length) {\r\n        this.$message.error('暂无项目')\r\n        this.treeLoading = false\r\n        this.tableLoading = false\r\n      } else {\r\n        this.projectId = res.Data.Data[0].Sys_Project_Id\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.$refs.form2.resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    resetForm2(formName) {\r\n      this.dialogVisible = false\r\n      this.$refs[formName].resetFields()\r\n    },\r\n    submitForm2(formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          const { ProjectId } = this.form2\r\n          const {\r\n            Name,\r\n            Id,\r\n            Code,\r\n            Address,\r\n            Receiver,\r\n            Receiver_Tel,\r\n            Sys_Project_Id,\r\n            Receive_UserName\r\n          } = this.projects.find((v) => v.Id === this.form2.ProjectId)\r\n          const data = {\r\n            ProjectId,\r\n            Id,\r\n            Name,\r\n            Code,\r\n            Address,\r\n            Receiver,\r\n            Receiver_Tel,\r\n            Sys_Project_Id,\r\n            Receive_UserName,\r\n            ProfessionalType: this.ProfessionalType\r\n          }\r\n          this.$router.push({\r\n            name: 'PROShipPlanAdd',\r\n            query: {\r\n              pg_redirect: this.$route.name,\r\n              p: encodeURIComponent(JSON.stringify(data))\r\n            }\r\n          })\r\n          this.dialogVisible = false\r\n          this.$refs.form2.resetFields()\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    handleEdit(id, { Project_Name }) {\r\n      this.$router.push({\r\n        name: 'PROShipPlanEdit',\r\n        query: { pg_redirect: this.$route.name, id, type: 'edit', p: JSON.stringify({ Name: Project_Name }) }\r\n      })\r\n    },\r\n    // 撤回至草稿\r\n    handleWithdraw(id) {\r\n      this.$confirm('撤回至草稿, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          WithdrawDraft({\r\n            id: id\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '撤销成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    handleSub(id) {\r\n      console.log(id, 'id')\r\n      this.$confirm('提交该发货计划, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          SubmitOutPlan({\r\n            id\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '提交成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    handleDel(id) {\r\n      this.$confirm('是否删除该发货计划?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteOutPlan({\r\n          id\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '删除成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    handleInfo(id, { Project_Name }) {\r\n      this.$router.push({\r\n        name: 'PROShipPlanDetail',\r\n        query: { pg_redirect: this.$route.name, id, type: 'view', p: JSON.stringify({ Name: Project_Name }) }\r\n      })\r\n    },\r\n    handleCancelFlow(instanceId) {\r\n      this.$confirm('是否撤回?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        CancelFlow({\r\n          instanceId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      const form = {\r\n        ...this.form,\r\n        ...this.pageInfo\r\n      }\r\n      delete form['dateRange']\r\n      this.form.dateRange = this.form.dateRange || []\r\n      form.Plan_Date_Begin = parseTime(this.form.dateRange[0])\r\n        ? parseTime(this.form.dateRange[0])\r\n        : ''\r\n      form.Plan_Date_End = parseTime(this.form.dateRange[1])\r\n        ? parseTime(this.form.dateRange[1])\r\n        : ''\r\n      GetOutPlanPageList(form).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data.map((v) => {\r\n            v.Plan_Date = v.Plan_Date\r\n              ? parseTime(new Date(v.Plan_Date), '{y}-{m}-{d}')\r\n              : v.Plan_Date\r\n            return v\r\n          })\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n      GetProjectSendingAllCount({ ...form }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // console.log(res.Data,\"res.Data\");\r\n          this.totalData = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getTableConfig() {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code: 'ProShipPlanList'\r\n        }).then(res => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message({\r\n                message: '表格配置不存在',\r\n                type: 'error'\r\n              })\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            this.columns = (Data.ColumnList.filter(v => v.Is_Display) || []).map(item => {\r\n              item.Is_Resizable = true\r\n              return item\r\n            })\r\n            if (this.pageInfo) {\r\n              this.pageInfo.PageSize = +Data.Grid.Row_Number\r\n            } else {\r\n              this.form.PageSize = +Data.Grid.Row_Number\r\n            }\r\n            resolve(this.columns)\r\n            // this.fetchData();\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handlePageChange(e) {\r\n      if (this.pageInfo) {\r\n        this.pageInfo.Page = e.page\r\n      } else {\r\n        this.form.Page = e.page\r\n      }\r\n      // console.log(this.pageInfo.Page);\r\n      this.fetchData()\r\n    },\r\n    handleSizeChange(e) {\r\n      if (this.pageInfo) {\r\n        this.pageInfo.Page = 1\r\n        this.pageInfo.PageSize = e.size\r\n      } else {\r\n        this.form.Page = 1\r\n        this.form.PageSize = e.size\r\n      }\r\n      // console.log(this.pageInfo);\r\n      this.fetchData()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n  .app-container {\r\n    .cs-wrapper {\r\n      background-color: #FFFFFF;\r\n      height: 100%;\r\n      padding: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .search-x {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .cs-label {\r\n          white-space: nowrap;\r\n        }\r\n      }\r\n\r\n      .tb-x {\r\n        flex: 1;\r\n        margin-bottom: 10px;\r\n        overflow: auto;\r\n      }\r\n    }\r\n\r\n    .cs-unit {\r\n      margin: 0 10px;\r\n    }\r\n\r\n  }\r\n\r\n  .cs-red {\r\n    color: red;\r\n  }\r\n\r\n</style>\r\n"]}]}
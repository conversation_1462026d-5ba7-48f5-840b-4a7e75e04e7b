{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\index.vue?vue&type=style&index=0&id=2d969454&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\index.vue", "mtime": 1758242836204}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAifkAvc3R5bGVzL21peGluLnNjc3MiOw0KQGltcG9ydCAifkAvc3R5bGVzL3RhYnMuc2NzcyI7DQoNCi5taW45MDAgew0KICBtaW4td2lkdGg6IDkwMHB4Ow0KICBvdmVyZmxvdzogYXV0bzsNCn0NCg0KLmFwcC13cmFwcGVyIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCg0KICAuY3MtbGVmdCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCg0KICAgIC5pbm5lci13cmFwcGVyIHsNCiAgICAgIGZsZXg6IDE7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgIHBhZGRpbmc6IDE2cHggMTBweCAxNnB4IDE2cHg7DQogICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KDQogICAgICAudHJlZS1zZWFyY2ggew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KDQogICAgICAgIC5zZWFyY2gtc2VsZWN0IHsNCiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAudHJlZS14IHsNCiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgICAgbWFyZ2luLXRvcDogMTZweDsNCiAgICAgICAgZmxleDogMTsNCg0KICAgICAgICAuY3Mtc2Nyb2xsIHsNCiAgICAgICAgICBvdmVyZmxvdy15OiBhdXRvOw0KICAgICAgICAgIEBpbmNsdWRlIHNjcm9sbEJhcjsNCiAgICAgICAgfQ0KDQogICAgICAgIC5lbC10cmVlIHsNCiAgICAgICAgICBoZWlnaHQ6IDEwMCU7DQoNCiAgICAgICAgICAvLzo6di1kZWVwIHsNCiAgICAgICAgICAvLyAgLmVsLXRyZWUtbm9kZSB7DQogICAgICAgICAgLy8gICAgbWluLXdpZHRoOiAyNDBweDsNCiAgICAgICAgICAvLyAgICB3aWR0aDogbWluLWNvbnRlbnQ7DQogICAgICAgICAgLy8NCiAgICAgICAgICAvLyAgICAuZWwtdHJlZS1ub2RlX19jaGlsZHJlbiB7DQogICAgICAgICAgLy8gICAgICBvdmVyZmxvdzogaW5oZXJpdDsNCiAgICAgICAgICAvLyAgICB9DQogICAgICAgICAgLy8gIH0NCiAgICAgICAgICAvL30NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5jcy1yaWdodCB7DQogICAgZmxleDogMTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgb3ZlcmZsb3c6IGF1dG87DQoNCiAgICAuY3Mtei10Yi13cmFwcGVyIHsNCiAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgIGZsZXg6IDE7DQogICAgICBoZWlnaHQ6IDA7DQoNCiAgICAgIC50Yi1jb250YWluZXIgew0KICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICBwYWRkaW5nOiAwIDE2cHg7DQogICAgICAgIGZsZXg6IDE7DQogICAgICAgIGhlaWdodDogMDsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAuY3MtYm90dG9tIHsNCiAgICAgIHBhZGRpbmc6IDhweCAxNnB4IDhweCAxNnB4Ow0KICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3ctcmV2ZXJzZTsNCiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KDQogICAgICAuZGF0YS1pbmZvIHsNCiAgICAgICAgLmluZm8teCB7DQogICAgICAgICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5wZy1pbnB1dCB7DQogICAgICAgIHdpZHRoOiAxMDBweDsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAgICAgfQ0KDQogICAgICAucGFnaW5hdGlvbi1jb250YWluZXIgew0KICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICBwYWRkaW5nOiAwOw0KDQogICAgICAgIDo6di1kZWVwIC5lbC1pbnB1dC0tc21hbGwgLmVsLWlucHV0X19pbm5lciB7DQogICAgICAgICAgaGVpZ2h0OiAyOHB4Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAyOHB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQoNCi56LWRpYWxvZyB7DQogIDo6di1kZWVwIHsNCiAgICAuZWwtZGlhbG9nX19oZWFkZXIgew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzI5OGRmZjsNCg0KICAgICAgLmVsLWRpYWxvZ19fdGl0bGUsDQogICAgICAuZWwtZGlhbG9nX19jbG9zZSB7DQogICAgICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5lbC1kaWFsb2dfX2JvZHkgew0KICAgICAgLy8gbWF4LWhlaWdodDogNzQwcHg7DQogICAgICBvdmVyZmxvdzogYXV0bzsNCiAgICAgIEBpbmNsdWRlIHNjcm9sbEJhcjsNCg0KICAgICAgJjo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICAgICAgICB3aWR0aDogOHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQouY3MtZnJvbSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgcGFkZGluZzogMTZweCAxNnB4IDAgMTZweDsNCiAgZGlzcGxheTogZmxleDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogcmdiYSgzNCwgNDAsIDUyLCAwLjY1KTsNCg0KICBsYWJlbCB7DQogICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgIHZlcnRpY2FsLWFsaWduOiB0b3A7DQogIH0NCg0KICAuY3MtZnJvbS10aXRsZSB7DQogICAgZmxleDogMTsNCiAgfQ0KDQogIC5tYjAgew0KICAgIG1hcmdpbi1ib3R0b206IDA7DQoNCiAgICA6OnYtZGVlcCB7DQogICAgICAuZWwtZm9ybS1pdGVtIHsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuY3Mtc2VhcmNoIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KfQ0KDQouaW5wdXQtd2l0aC1zZWxlY3Qgew0KICAvL3dpZHRoOiAyNTBweDsNCn0NCg0KLmNzLWJ1dHRvbi1ib3ggew0KICBwYWRkaW5nOiAxNnB4IDE2cHggNnB4IDE2cHg7DQogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBmbGV4LXdyYXA6IHdyYXA7DQoNCiAgOjp2LWRlZXAgLmVsLWJ1dHRvbiB7DQogICAgbWFyZ2luLWxlZnQ6IDAgIWltcG9ydGFudDsNCiAgICBtYXJnaW4tcmlnaHQ6IDEwcHggIWltcG9ydGFudDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAuY3MtbGVuZ3RoIHsNCiAgICBmbGV4OiAxOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBmbGV4LWRpcmVjdGlvbjogcm93LXJldmVyc2U7DQogIH0NCn0NCg0KLmluZm8tYm94IHsNCiAgbWFyZ2luOiAwIDE2cHggMTZweCAxNnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBoZWlnaHQ6IDY0cHg7DQogIGJhY2tncm91bmQ6IHJnYmEoNDEsIDE0MSwgMjU1LCAwLjA1KTsNCg0KICAuY3MtY29sIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtZXZlbmx5Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgbWFyZ2luLXJpZ2h0OiA2NHB4Ow0KICB9DQoNCiAgLmluZm8tbGFiZWwgew0KICAgIGNvbG9yOiAjOTk5OTk5Ow0KICB9DQoNCiAgaSB7DQogICAgY29sb3I6ICMwMGMzNjE7DQogICAgZm9udC1zdHlsZTogbm9ybWFsOw0KICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgbWFyZ2luLWxlZnQ6IDEwcHg7DQogIH0NCn0NCg0KOjp2LWRlZXAgLmVsLXRyZWUtbm9kZSB7DQogIG1pbi13aWR0aDogMjQwcHg7DQogIHdpZHRoOiBtaW4tY29udGVudDsNCn0NCg0KOjp2LWRlZXAgLmVsLXRyZWUtbm9kZSA+IC5lbC10cmVlLW5vZGVfX2NoaWxkcmVuIHsNCiAgb3ZlcmZsb3c6IGluaGVyaXQ7DQp9DQoNCi5zdHJldGNoLWJ0biB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgd2lkdGg6IDIwcHg7DQogIGhlaWdodDogMTMwcHg7DQogIHRvcDogY2FsYygoMTAwJSAtIDEzMHB4KSAvIDIpOw0KICByaWdodDogLTIwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGJhY2tncm91bmQ6ICNlZmYxZjM7DQogIGN1cnNvcjogcG9pbnRlcjsNCg0KICAuY2VudGVyLWJ0biB7DQogICAgd2lkdGg6IDE0cHg7DQogICAgaGVpZ2h0OiAxMDBweDsNCiAgICBib3JkZXItcmFkaXVzOiAwIDlweCA5cHggMDsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjOGM5NWE4Ow0KDQogICAgPiBpIHsNCiAgICAgIGxpbmUtaGVpZ2h0OiAxMDBweDsNCiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgIGNvbG9yOiAjZmZmOw0KICAgIH0NCiAgfQ0KfQ0KDQoqIHsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCn0NCg0KLmZvdXJHcmVlbiB7DQogIGNvbG9yOiAjMDBjMzYxOw0KICBmb250LXN0eWxlOiBub3JtYWw7DQp9DQoNCi5mb3VyT3JhbmdlIHsNCiAgY29sb3I6ICNmZjk0MDA7DQogIGZvbnQtc3R5bGU6IG5vcm1hbDsNCn0NCg0KLmZvdXJSZWQgew0KICBjb2xvcjogI2ZmMDAwMDsNCiAgZm9udC1zdHlsZTogbm9ybWFsOw0KfQ0KDQouY3MtYmx1ZSB7DQogIGNvbG9yOiAjNWFjOGZhOw0KfQ0KDQoub3JhbmdlQmcgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMTQ4LCAwLCAwLjEpOw0KfQ0KDQoucmVkQmcgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1MiwgMTA3LCAxMjcsIDAuMSk7DQp9DQouZ3JlZW5CZyB7DQogIGJhY2tncm91bmQ6IHJnYmEoMCwgMTk1LCA5NywgMC4xKTsNCn0NCg0KLmNzLXRhZyB7DQogIG1hcmdpbi1sZWZ0OiA4cHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgcGFkZGluZzogMnB4IDRweDsNCiAgYm9yZGVyLXJhZGl1czogMXB4Ow0KfQ0KDQouY3MtdHJlZS14IHsNCiAgOjp2LWRlZXAgew0KICAgIC5lbC1zZWxlY3Qgew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgfQ0KICB9DQp9DQouY3MtZGl2aWRlciB7DQogIG1hcmdpbjogMTZweCAwIDAgMDsNCn0NCi5pc1BpY0FjdGl2ZSB7DQogIGNvbG9yOiAjMjk4ZGZmOw0KICBjdXJzb3I6IHBvaW50ZXI7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs3DA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/component-list/v4", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      v-loading=\"pgLoading\"\r\n      class=\"h100 app-wrapper\"\r\n      element-loading-text=\"加载中\"\r\n    >\r\n      <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n        <div class=\"inner-wrapper\">\r\n          <div class=\"tree-search\">\r\n            <el-select\r\n              v-model=\"statusType\"\r\n              clearable\r\n              class=\"search-select\"\r\n              placeholder=\"导入状态选择\"\r\n            >\r\n              <el-option label=\"已导入\" value=\"已导入\" />\r\n              <el-option label=\"未导入\" value=\"未导入\" />\r\n              <el-option label=\"已变更\" value=\"已变更\" />\r\n            </el-select>\r\n            <el-input\r\n              v-model.trim=\"projectName\"\r\n              placeholder=\"关键词搜索\"\r\n              size=\"small\"\r\n              clearable\r\n              suffix-icon=\"el-icon-search\"\r\n              @blur=\"fetchTreeDataLocal\"\r\n              @clear=\"fetchTreeDataLocal\"\r\n              @keydown.enter.native=\"fetchTreeDataLocal\"\r\n            />\r\n          </div>\r\n          <el-divider class=\"cs-divider\" />\r\n          <div class=\"tree-x cs-scroll\">\r\n            <tree-detail\r\n              ref=\"tree\"\r\n              icon=\"icon-folder\"\r\n              is-custom-filter\r\n              :custom-filter-fun=\"customFilterFun\"\r\n              :loading=\"treeLoading\"\r\n              :tree-data=\"treeData\"\r\n              show-status\r\n              show-detail\r\n              :filter-text=\"filterText\"\r\n              :expanded-key=\"expandedKey\"\r\n              @handleNodeClick=\"handleNodeClick\"\r\n            >\r\n              <template #csLabel=\"{ showStatus, data }\">\r\n                <span\r\n                  v-if=\"!data.ParentNodes\"\r\n                  class=\"cs-blue\"\r\n                >({{ data.Code }})</span>{{ data.Label }}\r\n                <template v-if=\"showStatus && data.Label != '全部'\">\r\n                  <span v-if=\"data.Data.Is_Deepen_Change\" class=\"cs-tag redBg\">\r\n                    <i class=\"fourRed\">已变更</i></span>\r\n                  <span\r\n                    v-else\r\n                    :class=\"[\r\n                      'cs-tag',\r\n                      data.Data.Is_Imported == true ? 'greenBg' : 'orangeBg',\r\n                    ]\"\r\n                  >\r\n                    <i\r\n                      :class=\"[\r\n                        data.Data.Is_Imported == true\r\n                          ? 'fourGreen'\r\n                          : 'fourOrange',\r\n                      ]\"\r\n                    >{{\r\n                      data.Data.Is_Imported == true ? \"已导入\" : \"未导入\"\r\n                    }}</i>\r\n                  </span>\r\n                </template>\r\n              </template>\r\n            </tree-detail>\r\n          </div>\r\n        </div>\r\n      </ExpandableSection>\r\n      <div class=\"cs-right\">\r\n        <div ref=\"searchDom\" class=\"cs-from\">\r\n          <div class=\"cs-search\">\r\n            <el-form\r\n              ref=\"customParams\"\r\n              :model=\"customParams\"\r\n              label-width=\"80px\"\r\n              class=\"demo-form-inline\"\r\n            >\r\n              <el-row>\r\n                <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n                  <el-form-item :label=\"levelName + '名称'\" prop=\"Names\">\r\n                    <el-input\r\n                      v-model=\"names\"\r\n                      clearable\r\n                      style=\"width: 100%\"\r\n                      class=\"input-with-select\"\r\n                      placeholder=\"请输入内容\"\r\n                      size=\"small\"\r\n                    >\r\n                      <el-select\r\n                        slot=\"prepend\"\r\n                        v-model=\"nameMode\"\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100px\"\r\n                      >\r\n                        <el-option label=\"模糊搜索\" :value=\"1\" />\r\n                        <el-option label=\"精确搜索\" :value=\"2\" />\r\n                      </el-select>\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\" :lg=\"5\" :xl=\"6\">\r\n                  <el-form-item\r\n                    label-width=\"60px\"\r\n                    class=\"mb0\"\r\n                    label=\"批次\"\r\n                    prop=\"InstallUnit_Ids\"\r\n                  >\r\n                    <el-select\r\n                      v-model=\"customParams.InstallUnit_Ids\"\r\n                      filterable\r\n                      clearable\r\n                      multiple\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100%\"\r\n                      :disabled=\"!Boolean(customParams.Area_Id)\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"item in installUnitIdNameList\"\r\n                        :key=\"item.Id\"\r\n                        :label=\"item.Name\"\r\n                        :value=\"item.Id\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"5\" :xl=\"4\">\r\n                  <el-form-item label-width=\"92px\" prop=\"SteelType\">\r\n                    <template #label><span>{{ levelName + '类型' }}</span></template>\r\n                    <el-tree-select\r\n                      ref=\"treeSelectObjectType\"\r\n                      v-model=\"customParams.SteelType\"\r\n                      class=\"cs-tree-x\"\r\n                      :select-params=\"treeSelectParams\"\r\n                      :tree-params=\"ObjectTypeList\"\r\n                      value-key=\"Id\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n                  <el-form-item :label=\"levelName + '号'\" prop=\"SteelNumber\">\r\n                    <el-input\r\n                      v-model=\"customParams.SteelNumber\"\r\n                      placeholder=\"请输入\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n                  <el-form-item :label=\"levelName + '序号'\" prop=\"SteelCode\">\r\n                    <el-input\r\n                      v-model=\"customParams.SteelCode\"\r\n                      placeholder=\"请输入\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n                  <el-form-item label=\"规格\" prop=\"Spec\">\r\n                    <el-input\r\n                      v-model=\"customParams.Spec\"\r\n                      placeholder=\"请输入\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\" :lg=\"5\" :xl=\"6\">\r\n                  <el-form-item label-width=\"60px\" label=\"材质\" prop=\"Texture\">\r\n                    <el-input\r\n                      v-model=\"customParams.Texture\"\r\n                      placeholder=\"请输入\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"5\" :xl=\"4\">\r\n                  <el-form-item\r\n                    label-width=\"92px\"\r\n                    class=\"mb0\"\r\n                    label=\"是否直发件\"\r\n                    prop=\"Is_Direct\"\r\n                  >\r\n                    <el-select\r\n                      v-model=\"customParams.Is_Direct\"\r\n                      style=\"width: 100%\"\r\n                      placeholder=\"请选择\"\r\n                      clearable\r\n                    >\r\n                      <el-option label=\"全部\" value=\"\" />\r\n                      <el-option label=\"是\" :value=\"true\" />\r\n                      <el-option label=\"否\" :value=\"false\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n                  <el-form-item label=\"操作人\" prop=\"Create_UserName\">\r\n                    <el-input\r\n                      v-model=\"customParams.Create_UserName\"\r\n                      style=\"width: 100%\"\r\n                      placeholder=\"请输入\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n                  <el-form-item class=\"mb0\" label-width=\"16px\">\r\n                    <el-button\r\n                      type=\"primary\"\r\n                      @click=\"handleSearch()\"\r\n                    >搜索\r\n                    </el-button>\r\n                    <el-button @click=\"handleSearch('reset')\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"fff cs-z-tb-wrapper\">\r\n          <div class=\"cs-button-box\">\r\n            <div>\r\n              <el-dropdown\r\n                trigger=\"click\"\r\n                placement=\"bottom-start\"\r\n                @command=\"handleCommand($event, 1)\"\r\n              >\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"!currentLastLevel\"\r\n                >多级清单导入\r\n                  <i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    :disabled=\"allStopFlag\"\r\n                    command=\"add\"\r\n                  >新增导入</el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    :disabled=\"allStopFlag\"\r\n                    command=\"cover\"\r\n                  >覆盖导入</el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    :disabled=\"allStopFlag\"\r\n                    command=\"halfcover\"\r\n                  >部分覆盖导入</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <!-- <el-button\r\n                  type=\"primary\"\r\n                  @click=\"deepListImport(1)\"\r\n                >构件/零件导入</el-button>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  @click=\"deepListImport(0)\"\r\n                >构件导入</el-button> -->\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"modelListImport\"\r\n              >导入模型清单\r\n              </el-button>\r\n              <!--              <el-button-->\r\n              <!--                type=\"primary\"-->\r\n              <!--                @click=\"LocationImport\"-->\r\n              <!--              >位置信息导入-->\r\n              <!--              </el-button>-->\r\n              <el-button\r\n                v-if=\"!isVersionFour\"\r\n                :disabled=\"!selectList.length\"\r\n                @click=\"handleSchedulingInfoExport\"\r\n              >导出排产单模板\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"!isVersionFour\"\r\n                @click=\"handleSteelExport(2)\"\r\n              >导出{{ levelName }}</el-button>\r\n              <el-dropdown\r\n                v-else\r\n                trigger=\"click\"\r\n                placement=\"bottom-start\"\r\n                @command=\"handleExport\"\r\n              >\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"!currentLastLevel\"\r\n                >导出\r\n                  <i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item command=\"com\">纯{{ levelName }}</el-dropdown-item>\r\n                  <el-dropdown-item command=\"all\">完整清单</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <el-button @click=\"handleHistoryExport\">历史清单导出</el-button>\r\n              <el-button\r\n                :loading=\"scheduleLoading\"\r\n                :disabled=\"!selectList.length\"\r\n                @click=\"handleScheduleExport\"\r\n              >排产单导出</el-button>\r\n              <el-button\r\n                :disabled=\"\r\n                  !selectList.length || selectList.some((item) => item.stopFlag)\r\n                \"\r\n                type=\"primary\"\r\n                plain\r\n                @click=\"handleBatchEdit\"\r\n              >批量编辑\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                plain\r\n                :disabled=\"\r\n                  !selectList.length || selectList.some((item) => item.stopFlag)\r\n                \"\r\n                @click=\"handleDelete\"\r\n              >删除选中\r\n              </el-button>\r\n              <el-button\r\n                type=\"success\"\r\n                plain\r\n                :disabled=\"!Boolean(customParams.Sys_Project_Id)\"\r\n                @click=\"handelImport\"\r\n              >图纸导入\r\n              </el-button>\r\n            </div>\r\n            <div v-if=\"showTotalLength\" class=\"cs-length\">\r\n              <span class=\"txt-green\">累计长度：{{ deepenTotalLength }}</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"info-box\">\r\n            <div class=\"cs-col\">\r\n              <span>\r\n                <span class=\"info-label\">深化总数</span>\r\n                <i>{{ SteelAmountTotal }} 件</i>\r\n              </span>\r\n              <span><span class=\"info-label\">深化总量</span>\r\n                <i>{{ SteelAllWeightTotal }} t</i></span>\r\n            </div>\r\n            <div class=\"cs-col\">\r\n              <span><span class=\"info-label\">排产总数</span>\r\n                <i>{{ SchedulingNumTotal }} 件</i></span>\r\n              <span><span class=\"info-label\">排产总量</span>\r\n                <i>{{ SchedulingAllWeightTotal }} t</i></span>\r\n            </div>\r\n            <div class=\"cs-col\" style=\"cursor: pointer;\" @click=\"getProcessData()\">\r\n              <span><span class=\"info-label\">完成总数</span>\r\n                <i>{{ FinishCountTotal }} 件</i></span>\r\n              <span><span class=\"info-label\">完成总量</span>\r\n                <i>{{ FinishWeightTotal }} t</i></span>\r\n            </div>\r\n            <div class=\"cs-col\">\r\n              <span><span class=\"info-label\">直发件总数</span>\r\n                <i>{{ IsComponentTotal }} 件</i></span>\r\n              <span><span class=\"info-label\">直发件总量</span>\r\n                <i>{{ IsComponentTotalSteelAllWeight }} t</i></span>\r\n            </div>\r\n            <div class=\"cs-col\">\r\n              <span><span class=\"info-label\">毛重合计</span>\r\n                <i>{{ TotalGrossWeightT }} t</i></span>\r\n            </div>\r\n          </div>\r\n          <div class=\"tb-container\">\r\n            <vxe-table\r\n              v-loading=\"tbLoading\"\r\n              :empty-render=\"{ name: 'NotData' }\"\r\n              show-header-overflow\r\n              element-loading-spinner=\"el-icon-loading\"\r\n              element-loading-text=\"拼命加载中\"\r\n              empty-text=\"暂无数据\"\r\n              class=\"cs-vxe-table\"\r\n              height=\"auto\"\r\n              auto-resize\r\n              align=\"left\"\r\n              stripe\r\n              :data=\"tbData\"\r\n              resizable\r\n              :tooltip-config=\"{ enterable: true }\"\r\n              :row-config=\"{ isHover: true }\"\r\n              @checkbox-all=\"tbSelectChange\"\r\n              @checkbox-change=\"tbSelectChange\"\r\n            >\r\n              <vxe-column fixed=\"left\" type=\"checkbox\" width=\"44\" />\r\n              <vxe-column\r\n                v-for=\"(item, index) in columns\"\r\n                :key=\"index\"\r\n                :fixed=\"item.Is_Frozen ? item.Frozen_Dirction : ''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width ? item.Width : 120\"\r\n              >\r\n                <!-- <template #default=\"{ row }\">\r\n                  <div v-if=\"item.Code == 'Is_Component'\">\r\n                      <span v-if=\"row.Is_Component === 'True'\">否</span>\r\n                      <span v-else-if=\"row.Is_Component === 'False'\">是</span>\r\n                      <span v-else>-</span>\r\n                    </div>\r\n                </template> -->\r\n                <template #default=\"{ row }\">\r\n                  <div v-if=\"item.Code == 'SteelName'\">\r\n                    <el-tag\r\n                      v-if=\"row.Is_Change\"\r\n                      style=\"margin-right: 8px\"\r\n                      type=\"danger\"\r\n                    >变</el-tag>\r\n                    <el-tag\r\n                      v-if=\"row.stopFlag\"\r\n                      style=\"margin-right: 8px\"\r\n                      type=\"danger\"\r\n                    >停</el-tag>\r\n                    <!-- :class=\"[{ isPicActive: row.Drawing !== '暂无' }]\" -->\r\n                    <span\r\n                      class=\"isPicActive\"\r\n                      @click=\"getComponentInfo(row)\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }}</span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'SteelAmount'\">\r\n                    <span\r\n                      v-if=\"row.Is_Component_Status == true\"\r\n                      style=\"color: #298dff\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }} 件</span>\r\n                    <span\r\n                      v-else\r\n                      style=\"color: #298dff; cursor: pointer\"\r\n                      @click=\"handleViewModel(row)\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }} 件</span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'SchedulingNum'\">\r\n                    <span\r\n                      v-if=\"row[item.Code]\"\r\n                      style=\"color: #298dff; cursor: pointer\"\r\n                      @click=\"handleViewScheduling(row)\"\r\n                    >{{ row[item.Code] + \" 件\" }}</span>\r\n                    <span v-else>-</span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'SH'\">\r\n                    <el-link\r\n                      type=\"primary\"\r\n                      @click=\"handleViewSH(row, 0)\"\r\n                    >查看\r\n                    </el-link>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'Part'\">\r\n                    <el-link\r\n                      type=\"primary\"\r\n                      @click=\"handleViewPart(row)\"\r\n                    >查看\r\n                    </el-link>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'Is_Component'\">\r\n                    <span>\r\n                      <!--                      这玩意叫 是否是直发件 -->\r\n                      <el-tag\r\n                        v-if=\"row.Is_Component === 'True'\"\r\n                        type=\"danger\"\r\n                      >否</el-tag>\r\n                      <el-tag v-else type=\"success\">是</el-tag>\r\n                    </span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'Is_Component_Status'\">\r\n                    <span>\r\n                      <el-tag\r\n                        v-if=\"row.Is_Component === 'True'\"\r\n                        type=\"danger\"\r\n                      >否</el-tag>\r\n                      <el-tag v-else type=\"success\">是</el-tag>\r\n                    </span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'Is_Trace'\">\r\n                    <span>\r\n                      <el-tag\r\n                        v-if=\"row.Is_Trace\"\r\n                        type=\"success\"\r\n                      >是</el-tag>\r\n                      <el-tag v-else type=\"danger\">否</el-tag>\r\n                    </span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'Launch_Time'\">\r\n                    {{ row[item.Code] | timeFormat }}\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'Drawing'\">\r\n                    <span\r\n                      v-if=\"row.Drawing !== '暂无'\"\r\n                      style=\"color: #298dff; cursor: pointer\"\r\n                      @click=\"getComponentInfo(row)\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }}\r\n                    </span>\r\n                    <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                  </div>\r\n\r\n                  <div v-else>\r\n                    <span>{{ row[item.Code] || \"-\" }}</span>\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n\r\n              <vxe-column\r\n                fixed=\"right\"\r\n                align=\"left\"\r\n                title=\"操作\"\r\n                width=\"150\"\r\n                show-overflow\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"handleView(row)\"\r\n                  >详情\r\n                  </el-button>\r\n                  <el-button\r\n                    :disabled=\"row.stopFlag\"\r\n                    type=\"text\"\r\n                    @click=\"handleEdit(row)\"\r\n                  >编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"handleTrack(row)\"\r\n                  >轨迹图\r\n                  </el-button>\r\n                </template>\r\n              </vxe-column>\r\n            </vxe-table>\r\n          </div>\r\n          <div class=\"cs-bottom\">\r\n            <Pagination\r\n              class=\"cs-table-pagination\"\r\n              :total=\"total\"\r\n              max-height=\"100%\"\r\n              :page-sizes=\"tablePageSize\"\r\n              :page.sync=\"queryInfo.Page\"\r\n              :limit.sync=\"queryInfo.PageSize\"\r\n              layout=\"total, sizes, prev, pager, next, jumper\"\r\n              @pagination=\"changePage\"\r\n            >\r\n              <!--                  <span class=\"pg-input\">\r\n                      <el-select\r\n                        v-model.number=\"queryInfo.PageSize\"\r\n                        allow-create\r\n                        filterable\r\n                        default-first-option\r\n                        @change=\"changePage\"\r\n                      >\r\n                        <el-option v-for=\"(item,index) in customPageSize\" :key=\"index\" :label=\"`${item}条/页`\" :value=\"item\" />\r\n                      </el-select>\r\n                    </span>-->\r\n            </Pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"z-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :select-list=\"selectList\"\r\n        :custom-params=\"customDialogParams\"\r\n        :type-id=\"customParams.TypeId\"\r\n        :type-entity=\"typeEntity\"\r\n        :params-steel=\"treeParamsSteel\"\r\n        :project-id=\"customParams.Project_Id\"\r\n        :sys-project-id=\"customParams.Sys_Project_Id\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData\"\r\n        @checkPackage=\"handleComponentPack\"\r\n        @checkSteelMeans=\"handleSteelMeans\"\r\n        @checkModelList=\"handleSteelExport\"\r\n        @locationExport=\"locationExport\"\r\n      />\r\n    </el-dialog>\r\n    <bimdialog\r\n      ref=\"dialog\"\r\n      :is-auto-split=\"isAutoSplit\"\r\n      :type-entity=\"typeEntity\"\r\n      @getData=\"fetchData\"\r\n      @getProjectAreaData=\"fetchTreeData\"\r\n    />\r\n    <el-drawer\r\n      :visible.sync=\"trackDrawer\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      destroy-on-close\r\n      custom-class=\"trackDrawerClass\"\r\n    >\r\n      <template #title>\r\n        <div>\r\n          <span>{{ trackDrawerTitle }}</span>\r\n          <span style=\"margin-left: 24px\">{{\r\n            trackDrawerData.SteelAmount\r\n          }}</span>\r\n        </div>\r\n      </template>\r\n      <TracePlot :track-drawer-data=\"trackDrawerData\" />\r\n    </el-drawer>\r\n\r\n    <comDrawdialog ref=\"comDrawdialogRef\" @getData=\"fetchData\" />\r\n    <modelDrawing ref=\"modelDrawingRef\" type=\"构件\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport {\r\n  GetComponentImportDetailPageList,\r\n  DeleteComponents,\r\n  DeleteAllComponentWithQuery,\r\n  GetComponentSummaryInfo,\r\n  ExportComponentInfo,\r\n  ExportComponentSchedulingInfo,\r\n  ExportThreeBom,\r\n  ExportDeepenFullSchedulingInfo\r\n} from '@/api/PRO/component'\r\nimport {\r\n  GetProjectAreaTreeList,\r\n  GetInstallUnitIdNameList\r\n} from '@/api/PRO/project'\r\nimport { getConfigure } from '@/api/user'\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { GetFileType } from '@/api/sys'\r\n\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport TopHeader from '@/components/TopHeader/index.vue'\r\nimport comImport from '@/views/PRO/component-list/v4/component/Import.vue'\r\nimport ComponentsHistory from '@/views/PRO/component-list/v4/component/ComponentsHistory.vue'\r\nimport comImportByFactory from '@/views/PRO/component-list/v4/component/ImportByFactory.vue'\r\nimport HistoryExport from '@/views/PRO/component-list/v4/component/HistoryExport.vue'\r\nimport BatchEdit from '@/views/PRO/component-list/v4/component/BatchEditor.vue'\r\nimport ComponentPack from '@/views/PRO/component-list/v4/component/ComponentPack/index.vue'\r\nimport Edit from '@/views/PRO/component-list/v4/component/Edit.vue'\r\nimport OneClickGeneratePack from '@/views/PRO/component-list/v4/component/OneClickGeneratePack.vue'\r\nimport GeneratePack from '@/views/PRO/component-list/v4/component/GeneratePack.vue'\r\nimport ProductionConfirm from '@/views/PRO/component-list/v4/component/ProductionConfirm.vue'\r\nimport PartList from '@/views/PRO/component-list/v4/component/PartList.vue'\r\nimport SteelMeans from '@/views/PRO/component-list/v4/component/SteelMeans.vue'\r\nimport ProcessData from '@/views/PRO/component-list/v4/component/ProcessData.vue'\r\nimport ModelComponentCode from '@/views/PRO/component-list/v4/component/ModelComponentCode.vue'\r\nimport ProductionDetails from '@/views/PRO/component-list/v4/component/ProductionDetails.vue'\r\nimport ModelListImport from '@/views/PRO/component-list/v4/component/ModelListImport.vue'\r\nimport comDrawdialog from '@/views/PRO/production-order/deepen-files/dialog.vue' // 深化文件-构件详图导入\r\n\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport { timeFormat } from '@/filters'\r\n// import { Column, Header, Table, Tooltip } from 'vxe-table'\r\n// import Vue from 'vue'\r\nimport AuthButtons from '@/mixins/auth-buttons'\r\nimport bimdialog from '@/views/PRO/component-list/v4/component/bimdialog.vue'\r\nimport axios from 'axios'\r\n\r\nimport sysUseType from '@/directive/sys-use-type'\r\nimport { combineURL } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { findFirstNode } from '@/utils/tree'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport LocationImport from '@/views/PRO/component-list/v4/component/LocationImport.vue'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport TracePlot from '@/views/PRO/component-list/v4/component/TracePlot.vue'\r\nimport numeral from 'numeral'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nimport { mapGetters } from 'vuex'\r\n\r\nimport modelDrawing from '@/views/PRO/components/modelDrawing.vue'\r\n// Vue.use(Header).use(Column).use(Tooltip).use(Table)\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  directives: { elDragDialog, sysUseType },\r\n  components: {\r\n    ExpandableSection,\r\n    LocationImport,\r\n    TreeDetail,\r\n    TopHeader,\r\n    comImport,\r\n    comImportByFactory,\r\n    BatchEdit,\r\n    HistoryExport,\r\n    GeneratePack,\r\n    Edit,\r\n    ComponentPack,\r\n    OneClickGeneratePack,\r\n    Pagination,\r\n    bimdialog,\r\n    ComponentsHistory,\r\n    ProductionConfirm,\r\n    PartList,\r\n    SteelMeans,\r\n    ModelComponentCode,\r\n    ProductionDetails,\r\n    ModelListImport,\r\n    comDrawdialog,\r\n    TracePlot,\r\n    modelDrawing,\r\n    ProcessData\r\n  },\r\n  mixins: [AuthButtons],\r\n  data() {\r\n    return {\r\n      allStopFlag: false,\r\n      showExpand: true,\r\n      isAutoSplit: undefined,\r\n      tablePageSize: tablePageSize,\r\n      syncVisible: false,\r\n      syncForm: {\r\n        Is_Sync_To_Part: null\r\n      },\r\n      syncRules: {\r\n        Is_Sync_To_Part: {\r\n          required: true,\r\n          message: '请选择是否同步到相关零件',\r\n          trigger: 'change'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n\r\n      treeData: [],\r\n      treeLoading: true,\r\n      expandedKey: '', // -1是全部\r\n      projectName: '',\r\n      statusType: '',\r\n      searchHeight: 0,\r\n      searchStatus: true,\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      pgLoading: false,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n        ParameterJson: []\r\n      },\r\n      customPageSize: [10, 20, 50, 100],\r\n      installUnitIdNameList: [], // 批次数组\r\n      nameMode: 1,\r\n      names: '',\r\n      customParams: {\r\n        Code_Like: '',\r\n        Spec: '',\r\n        Texture: '',\r\n        Is_Direct: '',\r\n        Create_UserName: '',\r\n        InstallUnit_Ids: [],\r\n        SteelNames: '',\r\n        TypeId: '',\r\n        Sys_Project_Id: '',\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        Project_Name: '',\r\n        SteelCode: '',\r\n        SteelNumber: '',\r\n        Area_Name: ''\r\n      },\r\n      Unit: '',\r\n      Proportion: 0, // 专业的单位换算\r\n      customDialogParams: {},\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      selectList: [],\r\n      factoryOption: [],\r\n      projectList: [],\r\n      typeOption: [],\r\n      treeParamsSteel: [],\r\n      columns: [],\r\n      columnsOption: [\r\n        // { Display_Name: \"构件名称\", Code: \"SteelName\" },\r\n        // { Display_Name: \"规格\", Code: \"SteelSpec\" },\r\n        // { Display_Name: \"长度\", Code: \"SteelLength\" },\r\n        // { Display_Name: \"构件类型\", Code: \"SteelType\" },\r\n        // { Display_Name: \"材质\", Code: \"SteelMaterial\" },\r\n        // { Display_Name: \"深化数量\", Code: \"SteelAmount\" },\r\n        // { Display_Name: \"排产数量\", Code: \"SchedulingNum\" },\r\n        // { Display_Name: \"单重\", Code: \"SteelWeight\" },\r\n        // { Display_Name: \"总重\", Code: \"SteelAllWeight\" },\r\n        // { Display_Name: \"直发件\", Code: \"Is_Component_Status\" },\r\n        // { Display_Name: \"操作人\", Code: \"Create_UserName\" },\r\n        // { Display_Name: \"操作时间\", Code: \"Create_Date\" },\r\n      ],\r\n      title: '',\r\n      width: '60%',\r\n      tipLabel: '',\r\n      monomerList: [],\r\n      mode: '',\r\n      isMonomer: true,\r\n      historyVisible: false,\r\n      sysUseType: undefined,\r\n      productionConfirm: '',\r\n      SteelFormEditData: {},\r\n      deepenTotalLength: 0, // 深化总量\r\n      SteelAmountTotal: 0, // 深化总量\r\n      SchedulingNumTotal: 0, // 排产总量\r\n      SteelAllWeightTotal: 0, // 深化总重\r\n      SchedulingAllWeightTotal: 0, // 排产总重\r\n      FinishCountTotal: 0, // 完成数量\r\n      FinishWeightTotal: 0, // 完成重量\r\n      IsComponentTotal: 0,\r\n      TotalGrossWeight: 0,\r\n      IsComponentTotalSteelAllWeight: 0,\r\n      leftCol: 4,\r\n      rightCol: 40,\r\n      leftWidth: 320,\r\n      drawer: false,\r\n      scheduleLoading: false,\r\n      command: 'cover', //  cover覆盖导入  add新增导入\r\n      currentLastLevel: false, //  当前区域是否是最内层\r\n      cadRowCode: '',\r\n      cadRowProjectId: '',\r\n      IsUploadCad: false,\r\n      comDrawData: {},\r\n      currentNode: {},\r\n      trackDrawer: false,\r\n      trackDrawerTitle: '',\r\n      trackDrawerData: {},\r\n      levelName: '',\r\n      levelCode: ''\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    typeEntity() {\r\n      return this.typeOption.find((i) => i.Id === this.customParams.TypeId)\r\n    },\r\n    showTotalLength() {\r\n      const arr = [\r\n        this.customParams.Fuzzy_Search_Col,\r\n        this.customParams.Fuzzy_Search_Col2,\r\n        this.customParams.Fuzzy_Search_Col3,\r\n        this.customParams.Fuzzy_Search_Col4\r\n      ]\r\n      return arr.includes('SteelLength') && arr.includes('SteelSpec')\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    TotalGrossWeightT() {\r\n      return numeral(this.TotalGrossWeight || 0).format('0.[000]')\r\n    }\r\n  },\r\n  watch: {\r\n    'customParams.TypeId': function(newValue, oldValue) {\r\n      console.log({ oldValue })\r\n      if (oldValue && oldValue !== '0') {\r\n        this.fetchData()\r\n      }\r\n    },\r\n    names(n, o) {\r\n      this.changeMode()\r\n    },\r\n    nameMode(n, o) {\r\n      this.changeMode()\r\n    }\r\n  },\r\n  async created() {\r\n    const { comName } = await GetBOMInfo(-1)\r\n    console.log('levelName', comName)\r\n    this.levelName = comName\r\n    this.levelCode = -1\r\n    await this.getPreferenceSettingValue()\r\n    await this.getTypeList()\r\n    // await this.fetchData()\r\n    // await this.getComponentSummaryInfo()\r\n    this.fetchTreeData()\r\n    this.getFileType()\r\n  },\r\n  mounted() {\r\n  },\r\n  activated() {\r\n  },\r\n  methods: {\r\n    changeMode(n) {\r\n      if (this.nameMode === 1) {\r\n        this.customParams.Code_Like = this.names\r\n        this.customParams.SteelNames = ''\r\n      } else {\r\n        this.customParams.Code_Like = ''\r\n        this.customParams.SteelNames = this.names.replace(/\\s+/g, '\\n')\r\n      }\r\n    },\r\n\r\n    getComponentInfo(row) {\r\n      const drawingData = row.Drawing ? row.Drawing.split(',') : [] // 图纸数据\r\n      const fileUrlData = row.File_Url ? row.File_Url.split(',') : [] // 图纸数据文件地址数据\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingActive = drawingData[0]\r\n      }\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingDataList = drawingData.map((item, index) => ({\r\n          name: item,\r\n          label: item,\r\n          url: fileUrlData[index]\r\n        }))\r\n      }\r\n      this.getComponentInfoDrawing(row)\r\n    },\r\n\r\n    /**\r\n     * 获取featureId 模型构件id   cadId CAD图纸id\r\n     */\r\n    getComponentInfoDrawing(row) {\r\n      const importDetailId = row.Id\r\n      GetSteelCadAndBimId({ importDetailId: importDetailId }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const _data = res.Data?.[0]\r\n          if (!row.File_Url && !_data.ExtensionName) {\r\n            this.$message({\r\n              message: `当前${this.levelName}无图纸和模型`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          const drawingData = {\r\n            'extensionName': _data.ExtensionName,\r\n            'fileBim': _data.fileBim,\r\n            'IsUpload': _data.IsUpload,\r\n            'Code': row.SteelName,\r\n            'Sys_Project_Id': row.Sys_Project_Id\r\n          }\r\n\r\n          this.$nextTick((_) => {\r\n            this.$refs.modelDrawingRef.dwgInit(drawingData)\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 项目区域数据集\r\n    fetchTreeData() {\r\n      GetProjectAreaTreeList({\r\n        Type: 0,\r\n        Bom_Level: this.levelCode,\r\n        MenuId: this.$route.meta.Id,\r\n        projectName: this.projectName\r\n      }).then((res) => {\r\n        // const resAll = [\r\n        //   {\r\n        //     ParentNodes: null,\r\n        //     Id: '-1',\r\n        //     Code: '全部',\r\n        //     Label: '全部',\r\n        //     Level: null,\r\n        //     Data: {},\r\n        //     Children: []\r\n        //   }\r\n        // ]\r\n        // const resData = resAll.concat(res.Data)\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data\r\n        resData.map((item) => {\r\n          if (item.Children.length === 0) {\r\n            item.Data.Is_Imported = false\r\n          } else {\r\n            item.Data.Is_Imported = item.Children.some((ich) => {\r\n              return ich.Data.Is_Imported === true\r\n            })\r\n\r\n            item.Is_Directory = true\r\n            item.Children.map((it) => {\r\n              if (it.Children.length > 0) {\r\n                it.Is_Directory = true\r\n              }\r\n            })\r\n          }\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        if (Object.keys(this.currentNode).length === 0) {\r\n          this.setKey()\r\n        } else {\r\n          this.handleNodeClick(this.currentNode)\r\n        }\r\n        this.treeLoading = false\r\n      })\r\n    },\r\n    // 设置默认选中第一个区域末级节点\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            console.log(Data, '????')\r\n            this.currentNode = Data\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children.length > 0) {\r\n              return deepFilter(Children)\r\n            } else {\r\n              this.handleNodeClick(item)\r\n              return\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    // 选中左侧项目节点\r\n    handleNodeClick(data) {\r\n      this.handleSearch('reset', false, 'default')\r\n      this.currentNode = data\r\n      this.expandedKey = data.Id\r\n      this.$nextTick((_) => {\r\n        const cur = this.$refs['tree'].$refs.tree.getNode(this.expandedKey)\r\n        if (cur) {\r\n          this.isAutoSplit = cur?.data.Data.Is_Auto_Split\r\n        }\r\n      })\r\n      console.log(data, 'data2============')\r\n      this.InstallUnit_Id = ''\r\n      if (data.ParentNodes === null && data.Code !== '全部') {\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n        this.customParams.Project_Id = data.Data.Id\r\n        this.customParams.Area_Name = ''\r\n        this.customParams.Area_Id = ''\r\n      } else {\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n        this.customParams.Project_Id = data.Data.Project_Id\r\n        this.customParams.Area_Id = data.Data.Id\r\n      }\r\n      this.isAutoSplit = data.Data?.Is_Auto_Split\r\n      this.currentLastLevel = !!(data.Data.Level && data.Children.length === 0)\r\n      if (this.currentLastLevel) {\r\n        this.customParams.Project_Name = data.Data?.Project_Name\r\n        this.customParams.Area_Name = data.Label\r\n      }\r\n      const dataID = data.Id === -1 ? '' : data.Id\r\n      console.log(\r\n        this.customParams.Sys_Project_Id,\r\n        'this.customParams.Sys_Project_Id============11111'\r\n      )\r\n      console.log(\r\n        this.customParams.Area_Id,\r\n        'this.customParams.Area_Id============11111'\r\n      )\r\n      console.log(\r\n        this.customParams.Project_Id,\r\n        'this.customParams.Project_Id============11111'\r\n      )\r\n      this.pgLoading = true\r\n      this.getInstallUnitIdNameList(dataID, data)\r\n      this.fetchData()\r\n      this.getComponentSummaryInfo()\r\n    },\r\n\r\n    // 获取批次\r\n    getInstallUnitIdNameList(id, data) {\r\n      console.log(data, '???????????')\r\n      if (id === '' || data.Children.length > 0) {\r\n        this.installUnitIdNameList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: id }).then((res) => {\r\n          this.installUnitIdNameList = res.Data\r\n        })\r\n      }\r\n    },\r\n\r\n    // 搜索\r\n    handleSearch(reset, hasSearch = true, type = '') {\r\n      this.searchStatus = false\r\n      if (reset) {\r\n        this.$refs.customParams.resetFields()\r\n        this.names = ''\r\n        // this.customParams.Fuzzy_Search_Col = 'SteelName'\r\n        // this.customParams.Fuzzy_Search_Col2 = 'SteelMaterial'\r\n        // this.customParams.Fuzzy_Search_Col3 = 'SteelSpec'\r\n        // this.customParams.Fuzzy_Search_Col4 = 'SteelWeight'\r\n        this.searchStatus = true\r\n      }\r\n      // let SteelNames = this.customParams.SteelNamesFormat.trim()\r\n      // SteelNames = SteelNames.replace(/\\s+/g, '\\n')\r\n      // this.customParams.SteelNames = SteelNames\r\n\r\n      hasSearch && this.fetchData()\r\n      if (type === '') {\r\n        this.getComponentSummaryInfo()\r\n      }\r\n    },\r\n\r\n    // 获取系统偏好，是否弹出生产管理过程的弹框\r\n    async getPreferenceSettingValue() {\r\n      GetPreferenceSettingValue({ Code: 'Production_Confirm' }).then((res) => {\r\n        this.productionConfirm = res.Data\r\n      })\r\n    },\r\n\r\n    // 构件统计\r\n    getComponentSummaryInfo() {\r\n      GetComponentSummaryInfo({\r\n        ...this.customParams\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.SteelAmountTotal = Math.round(res.Data.DeepenNum * 1000) / 1000 // 深化总量\r\n          this.SchedulingNumTotal =\r\n            Math.round(res.Data.SchedulingNum * 1000) / 1000 // 排产总量\r\n          this.SteelAllWeightTotal =\r\n            Math.round(res.Data.DeepenWeight * 1000) / 1000 // 深化总重\r\n          this.SchedulingAllWeightTotal =\r\n            Math.round(res.Data.SchedulingWeight * 1000) / 1000 // 排产总重\r\n          this.FinishCountTotal =\r\n            Math.round(res.Data.Finish_Count * 1000) / 1000 // 完成总数\r\n          this.FinishWeightTotal =\r\n            Math.round(res.Data.Finish_Weight * 1000) / 1000 // 完成总重\r\n          this.IsComponentTotal = res.Data.Direct_Count || 0\r\n          this.TotalGrossWeight = res.Data.TotalGrossWeight || 0\r\n          this.IsComponentTotalSteelAllWeight =\r\n            Math.round((res.Data.Direct_Weight || 0) * 1000) / 1000\r\n          this.allStopFlag = !!res.Data.Is_Stop\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 工序完成量\r\n    getProcessData() {\r\n      this.width = '40%'\r\n      this.generateComponent(`${this.levelName}工序完成量`, 'ProcessData')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(this.customParams, this.selectList.map((v) => v.Id).toString())\r\n      })\r\n    },\r\n\r\n    // 获取表格配置\r\n    getTableConfig(code) {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code:\r\n            code +\r\n            ',' +\r\n            this.typeOption.find((i) => i.Id === this.customParams.TypeId).Code\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message.error('当前专业没有配置相对应表格')\r\n              this.tbLoading = true\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            const list = Data.ColumnList || []\r\n            this.columns = list\r\n              .filter((v) => v.Is_Display)\r\n              .map((item) => {\r\n                if (item.Code === 'SteelName') {\r\n                  item.fixed = 'left'\r\n                }\r\n                return item\r\n              })\r\n            this.queryInfo.PageSize = +Data.Grid.Row_Number || 20\r\n\r\n            const selectOption = JSON.parse(JSON.stringify(this.columns))\r\n\r\n            console.log(selectOption)\r\n            this.columnsOption = selectOption.filter((v) => {\r\n              return (\r\n                v.Display_Name !== '操作时间' &&\r\n                v.Display_Name !== '安装位置' &&\r\n                v.Display_Name !== '模型ID' &&\r\n                v.Display_Name !== '深化资料' &&\r\n                v.Display_Name !== '备注' &&\r\n                v.Display_Name !== '零件' &&\r\n                v.Display_Name !== '排产数量' &&\r\n                v.Code.indexOf('Attr') === -1 &&\r\n                v.Display_Name !== '构件类型' &&\r\n                v.Display_Name !== '批次'\r\n              )\r\n            })\r\n            resolve(this.columns)\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    // 构件列表\r\n    async getComponentImportDetailPageList() {\r\n      try {\r\n        const res = await GetComponentImportDetailPageList({\r\n          ...this.queryInfo,\r\n          ...this.customParams\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.tbData = (res.Data.Data || []).map((v) => {\r\n            v.Create_Date = timeFormat(\r\n              v.Create_Date,\r\n              '{y}-{m}-{d} {h}:{i}:{s}'\r\n            )\r\n            return v\r\n          })\r\n          this.deepenTotalLength = res.Data.DeepenTotalLength || 0\r\n          this.queryInfo.PageSize = res.Data.PageSize\r\n          this.total = res.Data.TotalCount\r\n          this.selectList = []\r\n          await this.getStopList()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      } catch (e) {\r\n        this.$message({\r\n          message: `获取${this.levelName}列表失败`,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    async getStopList() {\r\n      if (!this.tbData || !this.tbData.length) return\r\n      const submitObj = this.tbData.map((item) => ({\r\n        Id: item.Id,\r\n        Type: 2,\r\n        Bom_Level: this.levelCode\r\n      }))\r\n      try {\r\n        const res = await GetStopList(submitObj)\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach((item) => {\r\n            stopMap[item.Id] = item.Is_Stop !== null\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap[row.Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Id])\r\n            }\r\n          })\r\n        }\r\n      } catch (e) {}\r\n    },\r\n\r\n    // 获取表格数据\r\n    async fetchData() {\r\n      console.log('列表更新成功')\r\n      // 分开获取，提高接口速度\r\n      await this.getTableConfig('plm_component_page_list')\r\n      this.tbLoading = true\r\n      this.getComponentImportDetailPageList().then((res) => {\r\n        this.tbLoading = false\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n\r\n    async changePage() {\r\n      this.tbLoading = true\r\n      if (\r\n        typeof this.queryInfo.PageSize !== 'number' ||\r\n        this.queryInfo.PageSize < 1\r\n      ) {\r\n        this.queryInfo.PageSize = 10\r\n      }\r\n      this.getComponentImportDetailPageList().then((res) => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n\r\n    tbSelectChange(array) {\r\n      console.log('array', array)\r\n      this.selectList = array.records\r\n      this.SteelAmountTotal = 0\r\n      this.SchedulingNumTotal = 0\r\n      this.SteelAllWeightTotal = 0\r\n      this.SchedulingAllWeightTotal = 0\r\n      this.FinishCountTotal = 0\r\n      this.FinishWeightTotal = 0\r\n      this.IsComponentTotal = 0\r\n      this.TotalGrossWeight = 0\r\n      this.IsComponentTotalSteelAllWeight = 0\r\n      let SteelAllWeightTotalTemp = 0\r\n      let SchedulingAllWeightTotalTemp = 0\r\n      let FinishWeightTotalTemp = 0\r\n      let IsComponentTotalSteelAllWeightTemp = 0\r\n      if (this.selectList.length > 0) {\r\n        this.selectList.forEach((item) => {\r\n          const schedulingNum =\r\n            item.SchedulingNum == null ? 0 : item.SchedulingNum\r\n          this.SteelAmountTotal += item.SteelAmount\r\n          this.SchedulingNumTotal += item.SchedulingNum\r\n          this.FinishCountTotal += item.Finish_Count\r\n          this.TotalGrossWeight += item.TotalGrossWeight / 1000\r\n          SteelAllWeightTotalTemp += item.SteelAllWeight\r\n          SchedulingAllWeightTotalTemp += item.SteelWeight * schedulingNum\r\n          FinishWeightTotalTemp += item.Finish_Weight\r\n          this.IsComponentTotal +=\r\n            item.Is_Component === 'False' ? item.SteelAmount : 0\r\n          IsComponentTotalSteelAllWeightTemp +=\r\n            item.Is_Component === 'False' ? item.SteelAllWeight : 0\r\n        })\r\n        this.SteelAllWeightTotal =\r\n          Math.round((SteelAllWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n        this.SchedulingAllWeightTotal =\r\n          Math.round((SchedulingAllWeightTotalTemp / this.Proportion) * 1000) /\r\n          1000\r\n        this.FinishWeightTotal =\r\n          Math.round((FinishWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n        this.IsComponentTotalSteelAllWeight =\r\n          Math.round(\r\n            (IsComponentTotalSteelAllWeightTemp / this.Proportion) * 1000\r\n          ) / 1000\r\n      } else {\r\n        this.getComponentSummaryInfo()\r\n      }\r\n    },\r\n\r\n    getTbData(data) {\r\n      const { CountInfo } = data\r\n      // this.tipLabel = `累计上传构件${YearSteel}件，总重${YearAllWeight}t。`\r\n      this.tipLabel = CountInfo\r\n    },\r\n\r\n    async getTypeList() {\r\n      const res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      const data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.Proportion = data[0].Proportion\r\n        this.Unit = data[0].Unit\r\n        this.typeOption = Object.freeze(data)\r\n        if (this.typeOption.length > 0) {\r\n          this.customParams.TypeId = this.typeOption[0]?.Id\r\n        }\r\n        this.getCompTypeTree(this.typeOption[0].Code)\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n\r\n    getCompTypeTree(Code) {\r\n      this.loading = true\r\n      GetCompTypeTree({\r\n        professional: Code\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.treeParamsSteel = res.Data\r\n            this.ObjectTypeList.data = res.Data\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            this.treeData = []\r\n          }\r\n        })\r\n        .finally((_) => {\r\n          this.loading = false\r\n        })\r\n    },\r\n\r\n    // 删除查询结果\r\n    handleSearchDelete() {\r\n      if (this.customParams.Project_Id === '') {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请选择项目'\r\n        })\r\n        return false\r\n      }\r\n      this.$confirm('此操作将删除搜索的数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteAllComponentWithQuery({\r\n            ...this.customParams\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.fetchData()\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n\r\n    // 删除选中\r\n    handleDelete() {\r\n      this.$confirm('此操作将删除选择数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.tbLoading = true\r\n          DeleteComponents({\r\n            ids: this.selectList.map((v) => v.Id).toString()\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.fetchData()\r\n              this.fetchTreeData()\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n            .finally(() => {\r\n              this.tbLoading = false\r\n            })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n\r\n    handleEdit(row) {\r\n      this.width = '45%'\r\n      this.generateComponent(`编辑${this.levelName}`, 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n\r\n    handleBatchEdit() {\r\n      const SchedulArr = this.selectList.filter((item) => {\r\n        return item.SchedulingNum != null && item.SchedulingNum > 0\r\n      })\r\n      if (SchedulArr.length > 0) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: `选中行包含已排产的${this.levelName},编辑信息需要进行变更操作`\r\n        })\r\n      } else {\r\n        this.width = '40%'\r\n        this.generateComponent('批量编辑', 'BatchEdit')\r\n        this.$nextTick((_) => {\r\n          this.$refs['content'].init(this.selectList, this.columnsOption)\r\n        })\r\n      }\r\n    },\r\n\r\n    handleView(row) {\r\n      this.width = '45%'\r\n      this.generateComponent(`查看${this.levelName}`, 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = true\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n\r\n    // 查看构件的零件\r\n    handleViewPart(row) {\r\n      this.width = '60%'\r\n      this.generateComponent('多级清单', 'PartList')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n\r\n    // 查看深化资料 type 0构件  1零件\r\n    handleViewSH(row, type) {\r\n      this.width = '40%'\r\n      this.generateComponent('查看深化资料', 'SteelMeans')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(row, type)\r\n      })\r\n    },\r\n\r\n    // 回调查看零件的深化资料\r\n    handleSteelMeans(row) {\r\n      this.handleViewSH(row, 1)\r\n    },\r\n\r\n    // 深化模型唯一码\r\n    handleViewModel(row) {\r\n      this.width = '40%'\r\n      this.generateComponent(`模型${this.levelName}唯一码列表`, 'ModelComponentCode')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n\r\n    // 排产数量点击的生产详情\r\n    handleViewScheduling(row) {\r\n      this.width = '30%'\r\n      this.generateComponent('生产详情', 'ProductionDetails')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n\r\n    handleHistory(row) {\r\n      console.log({ row })\r\n      this.generateComponent(`${this.levelName}变更历史`, 'ComponentsHistory')\r\n      this.customDialogParams = {\r\n        steelUnique: row.SteelUnique\r\n      }\r\n    },\r\n\r\n    locationExport() {\r\n      this.handleSteelExport(3)\r\n    },\r\n    handleExport(v) {\r\n      if (v === 'com') {\r\n        this.handleSteelExport(2)\r\n      } else {\r\n        this.handleExportAll()\r\n      }\r\n    },\r\n    handleExportAll() {\r\n      ExportThreeBom({\r\n        // model: {\r\n        //   Project_Id: this.customParams.Project_Id,\r\n        //   Area_Id: this.customParams.Area_Id\r\n        // }\r\n        ...this.queryInfo,\r\n        ...this.customParams,\r\n        Ids: this.selectList.map((v) => v.Id).toString()\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 导出构件  type 0导出未绑定的构件   1导出已绑定的构件    2导出构件\r\n    async handleSteelExport(type) {\r\n      if (\r\n        this.customParams.Sys_Project_Id === '' &&\r\n        this.selectList.length === 0\r\n      ) {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请选择项目'\r\n        })\r\n        return false\r\n      }\r\n      const obj = {\r\n        Bom_Level: this.levelCode,\r\n        Type: type,\r\n        Import_Detail_Ids: this.selectList.map((v) => v.Id),\r\n        ...this.customParams,\r\n        Sys_Project_Id: this.customParams.Sys_Project_Id\r\n      }\r\n      const res = await ExportComponentInfo(obj)\r\n\r\n      if (!res.IsSucceed) {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n        return\r\n      }\r\n      // eslint-disable-next-line no-unused-vars\r\n      let fileName = localStorage.getItem('ProjectName') + `_${this.levelName}导出明细`\r\n      if (res.type === 'application/octet-stream') {\r\n        fileName += '.rar'\r\n      } else {\r\n        fileName += '.xls'\r\n      }\r\n      window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n      // downloadBlobFile(res.Data, fileName, ' ')\r\n    },\r\n\r\n    // 获取文件的arraybuffer格式并传入进行打包准备\r\n    getFile(url) {\r\n      return new Promise((resolve, reject) => {\r\n        axios({\r\n          method: 'get',\r\n          url,\r\n          responseType: 'arraybuffer'\r\n        })\r\n          .then((res) => {\r\n            resolve(res.data)\r\n          })\r\n          .catch((error) => {\r\n            reject(error.toString())\r\n          })\r\n      })\r\n    },\r\n\r\n    // 模型清单导入\r\n    modelListImport() {\r\n      this.width = '30%'\r\n      this.generateComponent('模型清单导入', 'ModelListImport')\r\n    },\r\n\r\n    LocationImport() {\r\n      this.width = '30%'\r\n      this.generateComponent('位置信息导入', 'LocationImport')\r\n    },\r\n\r\n    // 新增导入 or 覆盖导入\r\n    handleCommand(command, type) {\r\n      // console.log(command, 'command')\r\n      // console.log(type, 'type')\r\n      this.command = command\r\n      if (type === 1) {\r\n        this.deepListImport(1)\r\n      } else if (type === 0) {\r\n        this.deepListImport(0)\r\n      }\r\n    },\r\n\r\n    // 打开导入弹框 importType 1零构件 0 构件\r\n    deepListImport(importType) {\r\n      console.log(importType, 'importType')\r\n      const fileType = {\r\n        Catalog_Code: 'PLMDeepenFiles',\r\n        Code: this.typeEntity.Code,\r\n        name: this.typeEntity.Name\r\n      }\r\n      if (this.productionConfirm === 'true' && importType === 0) {\r\n        this.width = '30%'\r\n        this.generateComponent(`导入${this.levelName}`, 'ProductionConfirm')\r\n      } else {\r\n        this.$refs.dialog.handleOpen(\r\n          'add',\r\n          fileType,\r\n          null,\r\n          true,\r\n          '',\r\n          importType,\r\n          '',\r\n          this.command,\r\n          this.customParams\r\n        )\r\n      }\r\n    },\r\n\r\n    // 回调是否有生产过程\r\n    deepListImportAgin(productionConfirmData) {\r\n      const fileType = {\r\n        Catalog_Code: 'PLMDeepenFiles',\r\n        Code: this.typeEntity.Code,\r\n        name: this.typeEntity.Name\r\n      }\r\n      this.$refs.dialog.handleOpen(\r\n        'add',\r\n        fileType,\r\n        null,\r\n        true,\r\n        '',\r\n        0,\r\n        productionConfirmData,\r\n        this.command,\r\n        this.customParams\r\n      )\r\n    },\r\n\r\n    // 导出排产单\r\n    handleSchedulingInfoExport() {\r\n      ExportComponentSchedulingInfo({\r\n        ids: this.selectList.map((v) => v.Id).toString()\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n          if (res.Message) {\r\n            this.$alert(res.Message, '导出通知', {\r\n              confirmButtonText: '我知道了'\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    handleHistoryExport() {\r\n      if (this.customParams.Project_Id === '') {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请选择项目'\r\n        })\r\n        return false\r\n      }\r\n      this.width = '60%'\r\n      this.generateComponent('历史清单导出', 'HistoryExport')\r\n    },\r\n\r\n    handleScheduleExport() {\r\n      this.scheduleLoading = true\r\n      const ids = this.selectList.map((v) => v.Id).toString()\r\n      ExportDeepenFullSchedulingInfo({\r\n        Ids: ids\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '导出成功',\r\n              type: 'success'\r\n            })\r\n            window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally((_) => {\r\n          this.scheduleLoading = false\r\n        })\r\n    },\r\n\r\n    handleComponentPack({ data, type = 2 }) {\r\n      console.log('index', data, type)\r\n      this.width = '80%'\r\n      this.generateComponent(`查看${this.levelName}包`, 'ComponentPack')\r\n      this.$nextTick((_) => {\r\n        if (data) {\r\n          this.$refs['content'].getSubmitObj(data)\r\n        }\r\n        this.$refs['content'].handlePackage(type)\r\n      })\r\n    },\r\n\r\n    handleAllPack() {\r\n      this.width = '30%'\r\n      this.generateComponent('查询结果一键打包', 'OneClickGeneratePack')\r\n      this.customDialogParams = this.customParams\r\n    },\r\n\r\n    handleGenerate() {\r\n      this.width = '30%'\r\n      this.generateComponent(`生成${this.levelName}包`, 'GeneratePack')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(this.selectList)\r\n      })\r\n    },\r\n\r\n    handleClose(data) {\r\n      this.dialogVisible = false\r\n\r\n      // 选择是否需要生产管理过程后回调再次弹框 importType肯定是0\r\n      if (data === true || data === false) {\r\n        this.deepListImportAgin(data)\r\n      }\r\n    },\r\n\r\n    generateComponent(title, component) {\r\n      this.title = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [\r\n        data.Data.Is_Deepen_Change\r\n          ? '已变更'\r\n          : data.Data.Is_Imported\r\n            ? '已导入'\r\n            : '未导入'\r\n      ]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [\r\n          ...status,\r\n          data.Data.Is_Deepen_Change\r\n            ? '已变更'\r\n            : data.Data.Is_Imported\r\n              ? '已导入'\r\n              : '未导入'\r\n        ]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter((v) => !!v)\r\n      status = status.filter((v) => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    //\r\n    async getFileType() {\r\n      const params = {\r\n        catalogCode: 'PLMDeepenFiles'\r\n      }\r\n      const res = await GetFileType(params)\r\n      // 获取构件详图\r\n      const data = res.Data.find((v) => v.Label === '构件详图')\r\n\r\n      this.comDrawData = {\r\n        isSHQD: false,\r\n        Id: data.Id,\r\n        name: data.Label,\r\n        Catalog_Code: data.Code,\r\n        Code: data.Data?.English_Name\r\n      }\r\n\r\n      console.log(this.comDrawData, 'comDrawData')\r\n    },\r\n    // 图纸导入\r\n    handelImport() {\r\n      this.$refs.comDrawdialogRef.handleOpen(\r\n        'add',\r\n        this.comDrawData,\r\n        '',\r\n        false,\r\n        this.customParams.Sys_Project_Id,\r\n        false\r\n      )\r\n    },\r\n\r\n    // 轨迹图\r\n    handleTrack(row) {\r\n      console.log(row, 'row')\r\n      this.trackDrawer = true\r\n      this.trackDrawerTitle = row.SteelName\r\n      this.trackDrawerData = row\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/tabs.scss\";\r\n\r\n.min900 {\r\n  min-width: 900px;\r\n  overflow: auto;\r\n}\r\n\r\n.app-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  overflow: hidden;\r\n\r\n  .cs-left {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-right: 20px;\r\n\r\n    .inner-wrapper {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      padding: 16px 10px 16px 16px;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n\r\n      .tree-search {\r\n        display: flex;\r\n\r\n        .search-select {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .tree-x {\r\n        overflow: hidden;\r\n        margin-top: 16px;\r\n        flex: 1;\r\n\r\n        .cs-scroll {\r\n          overflow-y: auto;\r\n          @include scrollBar;\r\n        }\r\n\r\n        .el-tree {\r\n          height: 100%;\r\n\r\n          //::v-deep {\r\n          //  .el-tree-node {\r\n          //    min-width: 240px;\r\n          //    width: min-content;\r\n          //\r\n          //    .el-tree-node__children {\r\n          //      overflow: inherit;\r\n          //    }\r\n          //  }\r\n          //}\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .cs-z-tb-wrapper {\r\n      overflow: hidden;\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      height: 0;\r\n\r\n      .tb-container {\r\n        overflow: hidden;\r\n        padding: 0 16px;\r\n        flex: 1;\r\n        height: 0;\r\n      }\r\n    }\r\n\r\n    .cs-bottom {\r\n      padding: 8px 16px 8px 16px;\r\n      position: relative;\r\n      display: flex;\r\n      flex-direction: row-reverse;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      box-sizing: border-box;\r\n\r\n      .data-info {\r\n        .info-x {\r\n          margin-right: 20px;\r\n        }\r\n      }\r\n\r\n      .pg-input {\r\n        width: 100px;\r\n        margin-right: 20px;\r\n      }\r\n\r\n      .pagination-container {\r\n        text-align: right;\r\n        margin: 0;\r\n        padding: 0;\r\n\r\n        ::v-deep .el-input--small .el-input__inner {\r\n          height: 28px;\r\n          line-height: 28px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.z-dialog {\r\n  ::v-deep {\r\n    .el-dialog__header {\r\n      background-color: #298dff;\r\n\r\n      .el-dialog__title,\r\n      .el-dialog__close {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .el-dialog__body {\r\n      // max-height: 740px;\r\n      overflow: auto;\r\n      @include scrollBar;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.cs-from {\r\n  background-color: #ffffff;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n  padding: 16px 16px 0 16px;\r\n  display: flex;\r\n  font-size: 14px;\r\n  color: rgba(34, 40, 52, 0.65);\r\n\r\n  label {\r\n    display: inline-block;\r\n    margin-right: 20px;\r\n    white-space: nowrap;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .cs-from-title {\r\n    flex: 1;\r\n  }\r\n\r\n  .mb0 {\r\n    margin-bottom: 0;\r\n\r\n    ::v-deep {\r\n      .el-form-item {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-search {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.input-with-select {\r\n  //width: 250px;\r\n}\r\n\r\n.cs-button-box {\r\n  padding: 16px 16px 6px 16px;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n\r\n  ::v-deep .el-button {\r\n    margin-left: 0 !important;\r\n    margin-right: 10px !important;\r\n    margin-bottom: 10px !important;\r\n  }\r\n\r\n  .cs-length {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: row-reverse;\r\n  }\r\n}\r\n\r\n.info-box {\r\n  margin: 0 16px 16px 16px;\r\n  display: flex;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  height: 64px;\r\n  background: rgba(41, 141, 255, 0.05);\r\n\r\n  .cs-col {\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n    flex-direction: column;\r\n    margin-right: 64px;\r\n  }\r\n\r\n  .info-label {\r\n    color: #999999;\r\n  }\r\n\r\n  i {\r\n    color: #00c361;\r\n    font-style: normal;\r\n    font-weight: 600;\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n::v-deep .el-tree-node {\r\n  min-width: 240px;\r\n  width: min-content;\r\n}\r\n\r\n::v-deep .el-tree-node > .el-tree-node__children {\r\n  overflow: inherit;\r\n}\r\n\r\n.stretch-btn {\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 130px;\r\n  top: calc((100% - 130px) / 2);\r\n  right: -20px;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #eff1f3;\r\n  cursor: pointer;\r\n\r\n  .center-btn {\r\n    width: 14px;\r\n    height: 100px;\r\n    border-radius: 0 9px 9px 0;\r\n    background-color: #8c95a8;\r\n\r\n    > i {\r\n      line-height: 100px;\r\n      text-align: center;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.fourGreen {\r\n  color: #00c361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #ff9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #ff0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5ac8fa;\r\n}\r\n\r\n.orangeBg {\r\n  background: rgba(255, 148, 0, 0.1);\r\n}\r\n\r\n.redBg {\r\n  background: rgba(252, 107, 127, 0.1);\r\n}\r\n.greenBg {\r\n  background: rgba(0, 195, 97, 0.1);\r\n}\r\n\r\n.cs-tag {\r\n  margin-left: 8px;\r\n  font-size: 12px;\r\n  padding: 2px 4px;\r\n  border-radius: 1px;\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-divider {\r\n  margin: 16px 0 0 0;\r\n}\r\n.isPicActive {\r\n  color: #298dff;\r\n  cursor: pointer;\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\index.vue?vue&type=style&index=0&id=3a7a9256&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\index.vue", "mtime": 1757909680922}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouY29udGFpbmVyIHsNCiAgcGFkZGluZzogMTZweDsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCg0KICAuc2VhcmNoLXdyYXBwZXIgew0KICAgIHBhZGRpbmc6IDE2cHggMTZweCAxNnB4IDE2cHg7DQogICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICBib3JkZXItcmFkaXVzOiA0cHggNHB4IDRweCA0cHg7DQoNCiAgICAvLyA6OnYtZGVlcCAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsNCiAgICAvLyAgIHdpZHRoOiAxOTdweDsNCiAgICAvLyB9DQogICAgOjp2LWRlZXAgLmVsLWZvcm0taXRlbSB7DQogICAgICAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsNCiAgICAgICAgJiA+IC5lbC1pbnB1dCB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIH0NCg0KICAgICAgICAmID4gLmVsLXNlbGVjdCB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIH0NCiAgICAgICAgJiA+IC5lbC1kYXRlLWVkaXRvciB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIH0NCg0KICAgICAgICAuZWwtdHJlZS1zZWxlY3QtaW5wdXQgew0KICAgICAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLmVsLXRhYnN7DQogICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOw0KICAgIHBhZGRpbmctbGVmdDogMTZweDsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KICA6OnYtZGVlcCAucGFnaW5hdGlvbiB7DQogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZCAhaW1wb3J0YW50Ow0KICAgIG1hcmdpbi10b3A6IDEycHggIWltcG9ydGFudDsNCg0KICAgIC5lbC1pbnB1dC0tc21hbGwgLmVsLWlucHV0X19pbm5lciB7DQogICAgICBoZWlnaHQ6IDI4cHg7DQogICAgICBsaW5lLWhlaWdodDogMjhweDsNCiAgICB9DQogIH0NCn0NCg0KLm1haW4td3JhcHBlciB7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIG1hcmdpbi10b3A6IDE2cHg7DQogIGZsZXg6IDE7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGJvcmRlci1yYWRpdXM6IDRweCA0cHggNHB4IDRweDsNCiAgcGFkZGluZzogMTZweCAxNnB4IDA7DQogIG92ZXJmbG93OmhpZGRlbjsNCg0KICAudGIteHsNCiAgICBmbGV4OiAxOw0KICAgIGhlaWdodDogMDsNCiAgfQ0KICAuYnRuLXdyYXBwZXIgew0KICAgIHBhZGRpbmctYm90dG9tOiAxNnB4Ow0KICB9DQp9DQoNCi5wbG0tY3VzdG9tLWRpYWxvZyB7DQogIDo6di1kZWVwIHsNCiAgICAuZWwtZGlhbG9nIC5lbC1kaWFsb2dfX2JvZHkgew0KICAgICAgaGVpZ2h0OiA3MHZoOw0KICAgIH0NCiAgfQ0KfQ0KLmRyYXdlckJveCB7DQogIC5jaGFydFdyYXBwZXIgew0KICAgIHBhZGRpbmc6IDIwcHg7DQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiAxMDAlOw0KICB9DQp9DQoucGFnaW5hdGlvbi1jb250YWluZXJ7DQogIHBhZGRpbmc6IDA7DQogIG1hcmdpbjogMTBweCAwOw0KICB0ZXh0LWFsaWduOiByaWdodDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8pBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/plan-production/task-allocation/v4", "sourcesContent": ["<template>\r\n  <div v-loading=\"pgLoading\" class=\"container abs100\">\r\n    <el-tabs v-model=\"activeName\" @tab-click=\"handleTabsClick\">\r\n      <!-- <el-tab-pane label=\"构件\" name=\"2\" />\r\n      <el-tab-pane label=\"部件\" name=\"3\" />\r\n      <el-tab-pane label=\"零件\" name=\"1\" /> -->\r\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n    </el-tabs>\r\n    <div class=\"search-wrapper\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" class=\"demo-form-inline\">\r\n        <el-row>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"Project_Id\">\r\n              <el-select\r\n                ref=\"ProjectName\"\r\n                v-model=\"form.Project_Id\"\r\n                filterable\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                @change=\"projectChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in ProjectNameData\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"区域\" prop=\"Area_Id\">\r\n              <el-tree-select\r\n                ref=\"treeSelectArea\"\r\n                v-model=\"form.Area_Id\"\r\n                :disabled=\"!form.Project_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                class=\"cs-tree-x\"\r\n                :tree-params=\"treeParamsArea\"\r\n                @select-clear=\"areaClear\"\r\n                @node-click=\"areaChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <template>\r\n              <el-form-item label=\"批次\" prop=\"InstallUnit_Id\">\r\n                <el-select\r\n                  ref=\"SetupPosition\"\r\n                  v-model=\"form.InstallUnit_Id\"\r\n                  :disabled=\"!form.Area_Id\"\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  @change=\"setupPositionChange\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in SetupPositionData\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </template>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"排产单号\" prop=\"Schduling_Code\">\r\n              <el-input v-model=\"form.Schduling_Code\" type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n            <el-form-item label=\"任务工序\" prop=\"Process_Code\">\r\n              <el-select\r\n                v-model=\"form.Process_Code\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n                @change=\"processChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in processOption\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Code\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n            <el-form-item label=\"加工班组\" prop=\"Working_Team_Id\">\r\n              <el-select\r\n                v-model=\"form.Working_Team_Id\"\r\n                :disabled=\"!form.Process_Code\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in groupOption\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n            <el-form-item\r\n              v-if=\"Is_Workshop_Enabled\"\r\n              label=\"所属车间\"\r\n              prop=\"Workshop_Name\"\r\n            >\r\n              <el-select\r\n                v-model=\"form.Workshop_Id\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in workShopOption\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"是否分配\" prop=\"Allocate_Status\">\r\n              <el-select v-model=\"form.Allocate_Status\" multiple placeholder=\"请选择\" clearable=\"\">\r\n                <!--            <el-option label=\"全部\" value=\"\" />-->\r\n                <el-option label=\"未分配\" :value=\"1\" />\r\n                <el-option label=\"已分配\" :value=\"2\" />\r\n                <el-option label=\"分配完成\" :value=\"3\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label-width=\"16px\">\r\n              <el-button @click=\"handleReset\">重置</el-button>\r\n              <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div>\r\n        <el-button type=\"primary\" @click=\"drawerOpen\">班组负荷</el-button>\r\n        <el-button type=\"primary\" :disabled=\"!selectList.length\" @click=\"batchAllocationWithPreStepTask\">上道工序同步</el-button>\r\n      </div>\r\n    </div>\r\n    <div class=\"main-wrapper\">\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          :key=\"activeName\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          empty-text=\"暂无数据\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :row-config=\"{isCurrent: true, isHover: true}\"\r\n          :loading=\"pgLoading\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          stripe\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"handleSelectionChange\"\r\n          @checkbox-change=\"handleSelectionChange\"\r\n        >\r\n          <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n          <vxe-column\r\n            v-for=\"(item, index) in columns\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            :min-width=\"item.Width||120\"\r\n            :visible=\"item.visible\"\r\n          >\r\n            <template v-if=\"item.Code==='Total_Allocation_Count'\" #default=\"{ row }\">\r\n              {{ row.Total_Allocation_Count - row.Total_Receive_Count === row.Can_Allocation_Count ?'分配完成' :row.Total_Allocation_Count > 0? '已分配':'未分配' }}\r\n            </template>\r\n            <template v-else-if=\"item.Code==='Finish_Date'||item.Code==='Order_Date'\" #default=\"{ row }\">\r\n              {{ row[item.Code] | timeFormat }}\r\n            </template>\r\n            <template v-else #default=\"{ row }\">\r\n              <span>{{ (row[item.Code] ===0 ?0 : row[item.Code]) | displayValue }}</span>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column title=\"操作\" fixed=\"right\" width=\"125\">\r\n            <template #default=\"{ row , rowIndex }\">\r\n              <el-button type=\"text\" @click=\"handleView(row)\">查看</el-button>\r\n              <el-button\r\n                v-if=\"row.Can_Allocation_Count !== 0\"\r\n                type=\"text\"\r\n                @click=\"handleDetail(row)\"\r\n              >任务分配\r\n              </el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <Pagination\r\n        :total=\"total\"\r\n        :page-sizes=\"tablePageSize\"\r\n        :page.sync=\"queryInfo.Page\"\r\n        :limit.sync=\"queryInfo.PageSize\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @pagination=\"pageChange\"\r\n      />\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData(1)\"\r\n      />\r\n    </el-dialog>\r\n    <el-drawer\r\n      size=\"60%\"\r\n      custom-class=\"drawerBox\"\r\n      :visible.sync=\"drawer\"\r\n      direction=\"btt\"\r\n      :with-header=\"false\"\r\n      append-to-body\r\n      wrapper-closable\r\n    >\r\n      <div class=\"chartWrapper\">\r\n        <div ref=\"chartDom\" style=\"width: 100%; height: 100%\" />\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { debounce } from '@/utils'\r\nimport { GetTeamTaskAllocationPageList, GetWorkingTeamLoadRealTime, BatchAllocationWithPreStepTask } from '@/api/PRO/production-task'\r\nimport { GetProcessList, GetWorkingTeamBase } from '@/api/PRO/technology-lib'\r\nimport { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'\r\nimport { GetCurFactory } from '@/api/PRO/factory.js' // 获取是否开启车间接口\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport getProjectAreaUnit from '@/views/PRO/inventory/package/mixins/mixinsProject.js'\r\nimport * as echarts from 'echarts'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetBOMInfo, getBomCode, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROTaskAllocationList',\r\n  components: {\r\n    Pagination\r\n  },\r\n  mixins: [getTbInfo, addRouterPage, getProjectAreaUnit],\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      bomName: '',\r\n      selectList: [],\r\n      tablePageSize: tablePageSize,\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/detail',\r\n          hidden: true,\r\n          component: () =>\r\n            import('@/views/PRO/plan-production/task-allocation/v4/detail.vue'),\r\n          name: 'PROTaskAllocationInfo',\r\n          meta: { title: '调整分配' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/view',\r\n          hidden: true,\r\n          component: () =>\r\n            import('@/views/PRO/plan-production/task-allocation/v4/detail.vue'),\r\n          name: 'PROTaskAllocationView',\r\n          meta: { title: '查看分配' }\r\n        }\r\n      ],\r\n      activeName: getBomCode('-1'), // 1零件  2构件\r\n      dialogVisible: false,\r\n      pgLoading: false,\r\n      tipLabel: '',\r\n      title: '',\r\n      currentComponent: '',\r\n      dWidth: '40%',\r\n      form: {\r\n        InstallUnit_Id: '', // 批次ID\r\n        Project_Id: '', // 项目名称\r\n        Area_Id: '', // 区域ID\r\n        Workshop_Id: '',\r\n        Schduling_Code: '',\r\n        Process_Code: '',\r\n        Working_Team_Id: '',\r\n        Allocate_Status: [1, 2]\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      tbConfig: {\r\n        Op_Width: 180\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      processOption: [],\r\n      groupOption: [],\r\n      total: 0,\r\n      search: () => ({}),\r\n      workShopOption: [],\r\n      Is_Workshop_Enabled: false,\r\n      drawer: false,\r\n      myChart: null\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.activeName === getBomCode('-1')\r\n    },\r\n    isUnitPart() {\r\n      return checkIsUnitPart(this.activeName)\r\n    }\r\n  },\r\n  activated() {\r\n    console.log('activatedactivatedactivated')\r\n    this.fetchData()\r\n  },\r\n  async mounted() {\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n\r\n    await this.getIsWorkShop()\r\n    await this.getCurColumns()\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.getProcessOption()\r\n    this.getWorkshopOption()\r\n  },\r\n  methods: {\r\n    async getCurColumns() {\r\n      this.columns = await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationList' : 'PROTaskAllocationList')\r\n      this.columns = this.columns.map((item, index) => {\r\n        if (index === this.columns.length - 1) {\r\n          item.Min_Width = 140\r\n        }\r\n        if (item.Code === 'Workshop_Name') {\r\n          item.Is_Display = this.Is_Workshop_Enabled\r\n        }\r\n        return item\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取任务分配类别\r\n     * Page 分页\r\n     * PageSize\r\n     * form 筛选\r\n     * form.Process_Type   2构件  1零件\r\n     */\r\n    fetchData(page) {\r\n      this.form.Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1\r\n      this.form.Bom_Level = this.activeName\r\n      page && (this.queryInfo.Page = page)\r\n      this.pgLoading = true\r\n      GetTeamTaskAllocationPageList({\r\n        ...this.queryInfo,\r\n        ...this.form\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取工序\r\n     * type  1构件  2零件 3部件\r\n     */\r\n    getProcessOption() {\r\n      // const _type = typeMap[+this.activeName]\r\n      const _type = this.isCom ? 1 : this.isUnitPart ? 3 : 2\r\n      GetProcessList({\r\n        type: _type,\r\n        Bom_Level: this.activeName\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.processOption = res.Data\r\n          /* if (this.processOption.length) {\r\n            this.form.Process_Id = this.processOption[0]?.Id\r\n            this.getTeamOption()\r\n          } */\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    handleSelectionChange(array) {\r\n      this.selectList = array.records\r\n    },\r\n\r\n    taskChange() {\r\n      this.form.Process_Code = ''\r\n      this.form.Working_Team_Id = ''\r\n      this.getProcessOption()\r\n    },\r\n\r\n    getTeamOption() {\r\n      let processId = ''\r\n      const cur = this.processOption.find(\r\n        (item) => item.Code === this.form.Process_Code\r\n      )\r\n      if (cur) {\r\n        processId = cur.Id\r\n      }\r\n      GetWorkingTeamBase({\r\n        processId: processId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.groupOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getWorkshopOption() {\r\n      GetWorkshopPageList({ page: 1, pagesize: -1 }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            this.workShopOption = res.Data.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n    },\r\n    async getIsWorkShop() {\r\n      await GetCurFactory({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.Is_Workshop_Enabled = res.Data[0].Is_Workshop_Enabled\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    processChange(v) {\r\n      this.form.Working_Team_Id = ''\r\n      if (!v) {\r\n        return\r\n      }\r\n      this.getTeamOption()\r\n    },\r\n\r\n    tbSelectChange(array) { },\r\n\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.search(1)\r\n    },\r\n\r\n    handleDetail(row) {\r\n      this.$router.push({\r\n        name: 'PROTaskAllocationInfo',\r\n        query: {\r\n          bomLevel: this.activeName,\r\n          pg_type: this.isCom ? 'com' : this.isUnitPart ? 'unitPart' : 'part',\r\n          pg_redirect: 'PROTaskAllocationList',\r\n          Is_Workshop_Enabled: this.Is_Workshop_Enabled,\r\n          other: encodeURIComponent(JSON.stringify(row))\r\n        }\r\n      })\r\n    },\r\n\r\n    handleView(row) {\r\n      this.$router.push({\r\n        name: 'PROTaskAllocationView',\r\n        query: {\r\n          type: 'view',\r\n          bomLevel: this.activeName,\r\n          pg_type: this.isCom ? 'com' : this.isUnitPart ? 'unitPart' : 'part',\r\n          pg_redirect: 'PROTaskAllocationList',\r\n          Is_Workshop_Enabled: this.Is_Workshop_Enabled,\r\n          other: encodeURIComponent(JSON.stringify(row))\r\n        }\r\n      })\r\n    },\r\n\r\n    // 批量上道工序同步\r\n    batchAllocationWithPreStepTask() {\r\n      const List = this.selectList.map(item => {\r\n        return {\r\n          Area_Id: item.Area_Id,\r\n          InstallUnit_Id: item.InstallUnit_Id,\r\n          Process_Code: item.Process_Code,\r\n          Schduling_Code: item.Schduling_Code,\r\n          Workshop_Name: item.Workshop_Name\r\n        }\r\n      })\r\n      BatchAllocationWithPreStepTask({\r\n        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,\r\n        Bom_Level: this.activeName,\r\n        List\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.fetchData()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 切换tab\r\n    handleTabsClick(tab, event) {\r\n      this.form = {\r\n        InstallUnit_Id: '',\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        Allocate_Status: [1, 2],\r\n        Schduling_Code: '',\r\n        Process_Code: '',\r\n        Working_Team_Id: ''\r\n      }\r\n      this.getCurColumns()\r\n      this.getProcessOption()\r\n      this.fetchData()\r\n    },\r\n    // drawer\r\n    drawerOpen() {\r\n      this.drawer = true\r\n      const xAxisData = []\r\n      const data1 = []\r\n      const data2 = []\r\n      GetWorkingTeamLoadRealTime({\r\n\r\n        type: this.isCom ? 1 : this.isUnitPart ? 3 : 2,\r\n        Bom_Level: this.activeName\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          console.log(res)\r\n          if (res.Data && res.Data.length > 0) {\r\n            res.Data.map(i => {\r\n              xAxisData.push(i.Name)\r\n              data1.push(i.Load ?? 0)\r\n              data2.push(i.Real_Time_Load ?? 0)\r\n              this.$nextTick(() => {\r\n                console.log('in')\r\n                const chartDom = this.$refs.chartDom\r\n                if (this.myChart == null) {\r\n                  this.myChart = echarts.init(chartDom)\r\n                }\r\n\r\n                const emphasisStyle = {\r\n                  itemStyle: {\r\n                    shadowBlur: 10,\r\n                    shadowColor: 'rgba(0,0,0,0.3)'\r\n                  }\r\n                }\r\n                const echartOption = {\r\n                  title: {\r\n                    text: '班组负荷实时情况',\r\n                    textStyle: {\r\n                      fontSize: 16,\r\n                      color: '#222834'\r\n                    }\r\n                  },\r\n                  tooltip: {\r\n                    show: true,\r\n                    trigger: 'axis'\r\n                  },\r\n                  legend: {\r\n                    icon: 'rect',\r\n                    itemWidth: 8,\r\n                    itemHeight: 4,\r\n                    data: [],\r\n                    textStyle: {\r\n                      fontSize: 12,\r\n                      color: '#999999 '\r\n                    }\r\n                  },\r\n                  grid: {\r\n                    left: '3%',\r\n                    right: '4%',\r\n                    bottom: '3%',\r\n                    containLabel: true\r\n                  },\r\n                  xAxis: {\r\n                    data: xAxisData,\r\n                    axisLine: { onZero: true },\r\n                    splitLine: { show: false },\r\n                    splitArea: { show: false }\r\n                  },\r\n                  yAxis: {\r\n\r\n                  },\r\n                  series: [\r\n                    {\r\n                      name: '负荷提醒线',\r\n                      type: 'bar',\r\n                      barGap: '-100%',\r\n                      emphasis: emphasisStyle,\r\n                      data: data1,\r\n                      itemStyle: { color: '#91cc75' }\r\n                    }, {\r\n                      name: '当前负荷',\r\n                      type: 'bar',\r\n                      barGap: '-100%',\r\n                      emphasis: emphasisStyle,\r\n                      data: data2,\r\n                      itemStyle: { color: '#5470C6' }\r\n                    }\r\n                  ]\r\n                }\r\n                this.myChart.setOption(echartOption)\r\n              })\r\n            })\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .search-wrapper {\r\n    padding: 16px 16px 16px 16px;\r\n    background: #ffffff;\r\n    border-radius: 4px 4px 4px 4px;\r\n\r\n    // ::v-deep .el-form-item__content {\r\n    //   width: 197px;\r\n    // }\r\n    ::v-deep .el-form-item {\r\n      .el-form-item__content {\r\n        & > .el-input {\r\n          width: 100%;\r\n        }\r\n\r\n        & > .el-select {\r\n          width: 100%;\r\n        }\r\n        & > .el-date-editor {\r\n          width: 100%;\r\n        }\r\n\r\n        .el-tree-select-input {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .el-tabs{\r\n    margin-bottom: 16px;\r\n    background-color: #ffffff;\r\n    padding-left: 16px;\r\n    width: 100%;\r\n  }\r\n  ::v-deep .pagination {\r\n    justify-content: flex-end !important;\r\n    margin-top: 12px !important;\r\n\r\n    .el-input--small .el-input__inner {\r\n      height: 28px;\r\n      line-height: 28px;\r\n    }\r\n  }\r\n}\r\n\r\n.main-wrapper {\r\n  background: #ffffff;\r\n  margin-top: 16px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 4px 4px 4px 4px;\r\n  padding: 16px 16px 0;\r\n  overflow:hidden;\r\n\r\n  .tb-x{\r\n    flex: 1;\r\n    height: 0;\r\n  }\r\n  .btn-wrapper {\r\n    padding-bottom: 16px;\r\n  }\r\n}\r\n\r\n.plm-custom-dialog {\r\n  ::v-deep {\r\n    .el-dialog .el-dialog__body {\r\n      height: 70vh;\r\n    }\r\n  }\r\n}\r\n.drawerBox {\r\n  .chartWrapper {\r\n    padding: 20px;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n.pagination-container{\r\n  padding: 0;\r\n  margin: 10px 0;\r\n  text-align: right;\r\n}\r\n</style>\r\n"]}]}
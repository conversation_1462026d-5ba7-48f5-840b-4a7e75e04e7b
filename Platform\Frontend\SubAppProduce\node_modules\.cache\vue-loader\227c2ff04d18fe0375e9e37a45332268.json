{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\draft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\draft.vue", "mtime": 1758266753120}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7IGNsb3NlVGFnVmlldywgZGVib3VuY2UgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IEJhdGNoUHJvY2Vzc0FkanVzdCBmcm9tICcuL2NvbXBvbmVudHMvQmF0Y2hQcm9jZXNzQWRqdXN0Jw0KaW1wb3J0IHsNCiAgR2V0Q2FuU2NoZHVsaW5nUGFydExpc3QsDQogIEdldER3ZywNCiAgR2V0U2NoZHVsaW5nV29ya2luZ1RlYW1zLA0KICBTYXZlVW5pdFNjaGVkdWxpbmdXb3Jrc2hvcE5ldywNCiAgU2F2ZVNjaGR1bGluZ1Rhc2tCeUlkLCBTYXZlVW5pdFNjaGR1bGluZ0RyYWZ0TmV3LCBHZXRVbml0U2NoZHVsaW5nSW5mb0RldGFpbA0KfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KaW1wb3J0IHsgR2V0U3RvcExpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KaW1wb3J0IEFkZERyYWZ0IGZyb20gJy4vY29tcG9uZW50cy9hZGREcmFmdCcNCmltcG9ydCBPd25lclByb2Nlc3MgZnJvbSAnLi9jb21wb25lbnRzL093bmVyUHJvY2VzcycNCmltcG9ydCBXb3Jrc2hvcCBmcm9tICcuL2NvbXBvbmVudHMvV29ya3Nob3AudnVlJw0KaW1wb3J0IHsgR2V0R3JpZEJ5Q29kZSB9IGZyb20gJ0AvYXBpL3N5cycNCmltcG9ydCB7IGdldFVuaXF1ZSwgdW5pcXVlQ29kZSB9IGZyb20gJy4vY29uc3RhbnQnDQppbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICd1dWlkJw0KaW1wb3J0IG51bWVyYWwgZnJvbSAnbnVtZXJhbCcNCmltcG9ydCB7IEdldExpYkxpc3RUeXBlLCBHZXRQcm9jZXNzRmxvd0xpc3RXaXRoVGVjaG5vbG9neSwgR2V0UHJvY2Vzc0xpc3RCYXNlIH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJw0KaW1wb3J0IHsgQXJlYUdldEVudGl0eSB9IGZyb20gJ0AvYXBpL3BsbS9wcm9qZWN0cycNCmltcG9ydCB7IG1hcEFjdGlvbnMsIG1hcEdldHRlcnMgfSBmcm9tICd2dWV4Jw0KaW1wb3J0IHsgR2V0UGFydFR5cGVMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3BhcnRUeXBlJw0KaW1wb3J0IG1vbWVudCBmcm9tICdtb21lbnQnDQppbXBvcnQgRXhwYW5kYWJsZVNlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL0V4cGFuZGFibGVTZWN0aW9uL2luZGV4LnZ1ZScNCmltcG9ydCBUcmVlRGV0YWlsIGZyb20gJ0AvY29tcG9uZW50cy9UcmVlRGV0YWlsL2luZGV4LnZ1ZScNCmltcG9ydCB7IEdldEluc3RhbGxVbml0SWROYW1lTGlzdCwgR2V0UHJvamVjdEFyZWFUcmVlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9qZWN0Jw0KDQppbXBvcnQgeyBHZXRDb21wVHlwZVRyZWUgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJw0KaW1wb3J0IHsgcGFyc2VPc3NVcmwgfSBmcm9tICdAL3V0aWxzL2ZpbGUnDQppbXBvcnQgRHluYW1pY1RhYmxlRmllbGRzIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljVGFibGVGaWVsZHMvaW5kZXgudnVlJw0KDQppbXBvcnQgeyBnZXRDb25maWd1cmUgfSBmcm9tICdAL2FwaS91c2VyJw0KaW1wb3J0IHsgYmFzZVVybCB9IGZyb20gJ0AvdXRpbHMvYmFzZXVybCcNCmltcG9ydCB7IEdldFN0ZWVsQ2FkQW5kQmltSWQgfSBmcm9tICdAL2FwaS9QUk8vY29tcG9uZW50Jw0KaW1wb3J0IHsgZ2V0Qm9tTmFtZSB9IGZyb20gJ0Avdmlld3MvUFJPL2JvbS1zZXR0aW5nL3V0aWxzJw0KDQpjb25zdCBTUExJVF9TWU1CT0wgPSAnJF8kJw0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IER5bmFtaWNUYWJsZUZpZWxkcywgVHJlZURldGFpbCwgRXhwYW5kYWJsZVNlY3Rpb24sIEJhdGNoUHJvY2Vzc0FkanVzdCwgQWRkRHJhZnQsIFdvcmtzaG9wLCBPd25lclByb2Nlc3MgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgZHJhd2VyOiBmYWxzZSwNCiAgICAgIGRyYXdlcnN1bGw6IGZhbHNlLA0KICAgICAgaWZyYW1lS2V5OiAnJywNCiAgICAgIGZ1bGxzY3JlZW5pZDogJycsDQogICAgICBpZnJhbWVVcmw6ICcnLA0KICAgICAgZnVsbGJpbWlkOiAnJywNCiAgICAgIGZpbGVCaW06ICcnLA0KICAgICAgSXNVcGxvYWRDYWQ6IGZhbHNlLA0KICAgICAgY2FkUm93Q29kZTogJycsDQogICAgICBjYWRSb3dQcm9qZWN0SWQ6ICcnLA0KICAgICAgdGJLZXk6IDEwMCwNCiAgICAgIGlzQ29tcG9uZW50T3B0aW9uczogWw0KICAgICAgICB7IGxhYmVsOiAn5pivJywgdmFsdWU6IGZhbHNlIH0sDQogICAgICAgIHsgbGFiZWw6ICflkKYnLCB2YWx1ZTogdHJ1ZSB9DQogICAgICBdLA0KICAgICAgc3BlY09wdGlvbnM6IFt7IGRhdGE6ICcnIH1dLA0KICAgICAgZmlsdGVyVHlwZU9wdGlvbjogW3sgZGF0YTogJycgfV0sDQogICAgICBmaWx0ZXJDb2RlT3B0aW9uOiBbeyBkYXRhOiAnJyB9XSwNCiAgICAgIHByb2plY3RPcHRpb25zOiBbeyBkYXRhOiAnJyB9XSwNCiAgICAgIGFyZWFPcHRpb25zOiBbeyBkYXRhOiAnJyB9XSwNCiAgICAgIGluc3RhbGxPcHRpb25zOiBbeyBkYXRhOiAnJyB9XSwNCiAgICAgIHByb2plY3RMaXN0OiBbXSwNCiAgICAgIGluc3RhbGxMaXN0OiBbXSwNCiAgICAgIGFyZWFMaXN0OiBbXSwNCiAgICAgIHBpY2tlck9wdGlvbnM6IHsNCiAgICAgICAgZGlzYWJsZWREYXRlKHRpbWUpIHsNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGlubmVyRm9ybTogew0KICAgICAgICBwcm9qZWN0TmFtZTogJycsDQogICAgICAgIGFyZWFOYW1lOiAnJywNCiAgICAgICAgaW5zdGFsbE5hbWU6ICcnLA0KICAgICAgICBzZWFyY2hDb250ZW50OiAnJywNCiAgICAgICAgc2VhcmNoU3BlY1NlYXJjaDogJycsDQogICAgICAgIHNlYXJjaERpcmVjdDogJycNCiAgICAgIH0sDQogICAgICBjdXJTZWFyY2g6IDEsDQogICAgICBzZWFyY2hUeXBlOiAnJywNCiAgICAgIGZvcm1JbmxpbmU6IHsNCiAgICAgICAgU2NoZHVsaW5nX0NvZGU6ICcnLA0KICAgICAgICBDcmVhdGVfVXNlck5hbWU6ICcnLA0KICAgICAgICBGaW5pc2hfRGF0ZTogJycsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiAnJywNCiAgICAgICAgUmVtYXJrOiAnJw0KICAgICAgfSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgY3VycmVudElkczogJycsDQogICAgICBjb2x1bW5zOiBbXSwNCiAgICAgIHRiRGF0YTogW10sDQogICAgICB0YkNvbmZpZzoge30sDQogICAgICBUb3RhbENvdW50OiAwLA0KICAgICAgbXVsdGlwbGVTZWxlY3Rpb246IFtdLA0KICAgICAgc2hvd0V4cGFuZDogdHJ1ZSwNCiAgICAgIHBnTG9hZGluZzogZmFsc2UsDQogICAgICBkZWxldGVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHdvcmtTaG9wSXNPcGVuOiBmYWxzZSwNCiAgICAgIGlzT3duZXJOdWxsOiBmYWxzZSwNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgb3BlbkFkZERyYWZ0OiBmYWxzZSwNCiAgICAgIHNhdmVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBpc0NoZWNrQWxsOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLA0KICAgICAgZ3JpZENvZGU6ICcnLA0KICAgICAgZFdpZHRoOiAnMjUlJywNCiAgICAgIHRpdGxlOiAnJywNCiAgICAgIHNlYXJjaDogKCkgPT4gKHt9KSwNCiAgICAgIHBhZ2VUeXBlOiB1bmRlZmluZWQsDQogICAgICB0aXBMYWJlbDogJycsDQogICAgICB0ZWNobm9sb2d5T3B0aW9uOiBbXSwNCiAgICAgIHR5cGVPcHRpb246IFtdLA0KICAgICAgd29ya2luZ1RlYW06IFtdLA0KICAgICAgcGFnZVN0YXR1czogdW5kZWZpbmVkLA0KICAgICAgc2NoZWR1bGVJZDogJycsDQogICAgICBwYXJ0Q29tT3duZXJDb2x1bW46IG51bGwsDQoNCiAgICAgIGluc3RhbGxVbml0SWRMaXN0OiBbXSwNCiAgICAgIHByb2plY3RJZDogJycsDQogICAgICBhcmVhSWQ6ICcnLA0KICAgICAgcHJvamVjdE5hbWU6ICcnLA0KICAgICAgc3RhdHVzVHlwZTogJycsDQogICAgICBleHBhbmRlZEtleTogJycsDQogICAgICAvLyB0cmVlTG9hZGluZzogZmFsc2UsDQogICAgICB0cmVlRGF0YTogW10sDQogICAgICB0cmVlUGFyYW1zQ29tcG9uZW50VHlwZTogew0KICAgICAgICAnZGVmYXVsdC1leHBhbmQtYWxsJzogdHJ1ZSwNCiAgICAgICAgJ2NoZWNrLXN0cmljdGx5JzogdHJ1ZSwNCiAgICAgICAgZmlsdGVyYWJsZTogdHJ1ZSwNCiAgICAgICAgY2xpY2tQYXJlbnQ6IHRydWUsDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICBwcm9wczogew0KICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLA0KICAgICAgICAgIGxhYmVsOiAnTGFiZWwnLA0KICAgICAgICAgIHZhbHVlOiAnRGF0YScNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIHRyZWVTZWxlY3RQYXJhbXM6IHsNCiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLA0KICAgICAgICBjb2xsYXBzZVRhZ3M6IHRydWUsDQogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQ0KICAgICAgfSwNCiAgICAgIGRpc2FibGVkQWRkOiB0cnVlLA0KICAgICAgbGV2ZWxOYW1lOiAnJywNCiAgICAgIHByb2plY3RPcHRpb246IFtdDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgICd0YkRhdGEubGVuZ3RoJzogew0KICAgICAgaGFuZGxlcihuLCBvKSB7DQogICAgICAgIHRoaXMuY2hlY2tPd25lcigpDQogICAgICAgIHRoaXMuZG9GaWx0ZXIoKQ0KICAgICAgfSwNCiAgICAgIGltbWVkaWF0ZTogZmFsc2UNCiAgICB9DQogIH0sDQoNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc0NvbSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSAnY29tJw0KICAgIH0sDQogICAgaXNWaWV3KCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVN0YXR1cyA9PT0gJ3ZpZXcnDQogICAgfSwNCiAgICBpc0VkaXQoKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlU3RhdHVzID09PSAnZWRpdCcNCiAgICB9LA0KICAgIGlzQWRkKCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVN0YXR1cyA9PT0gJ2FkZCcNCiAgICB9LA0KICAgIGFkZERyYWZ0S2V5KCkgew0KICAgICAgcmV0dXJuIHRoaXMuZXhwYW5kZWRLZXkgKyB0aGlzLmZvcm1JbmxpbmUuSW5zdGFsbFVuaXRfSWQNCiAgICB9LA0KICAgIC8vIGZpbHRlclRleHQoKSB7DQogICAgLy8gICByZXR1cm4gdGhpcy5wcm9qZWN0TmFtZSArIFNQTElUX1NZTUJPTCArIHRoaXMuc3RhdHVzVHlwZQ0KICAgIC8vIH0sDQogICAgc3RhdHVzQ29kZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLmlzQ29tID8gJ0NvbXBfU2NoZHVsZV9TdGF0dXMnIDogJ1BhcnRfU2NoZHVsZV9TdGF0dXMnDQogICAgfSwNCiAgICBpbnN0YWxsTmFtZSgpIHsNCiAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmluc3RhbGxVbml0SWRMaXN0LmZpbmQodiA9PiB2LklkID09PSB0aGlzLmZvcm1JbmxpbmUuSW5zdGFsbFVuaXRfSWQpDQogICAgICBpZiAoaXRlbSkgew0KICAgICAgICByZXR1cm4gaXRlbS5OYW1lDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJycNCiAgICAgIH0NCiAgICB9LA0KICAgIGlzUGFydFByZXBhcmUoKSB7DQogICAgICByZXR1cm4gdGhpcy5nZXRJc1BhcnRQcmVwYXJlICYmICF0aGlzLmlzQ29tDQogICAgfSwNCiAgICBpc05lc3QoKSB7DQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIGlzU3RvcEZsYWcoKSB7DQogICAgICByZXR1cm4gdGhpcy50YkRhdGEuc29tZShpdGVtID0+IGl0ZW0uc3RvcEZsYWcpDQogICAgfSwNCiAgICAuLi5tYXBHZXR0ZXJzKCdmYWN0b3J5SW5mbycsIFsnd29ya3Nob3BFbmFibGVkJywgJ2dldElzUGFydFByZXBhcmUnXSksDQogICAgLi4ubWFwR2V0dGVycygnc2NoZWR1bGUnLCBbJ3Byb2Nlc3NMaXN0JywgJ25lc3RJZHMnXSkNCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHsNCiAgICAgIC8vIHRoaXMuYmFzZUNhZFVybCA9ICdodHRwOi8vbG9jYWxob3N0Ojk1MjknDQogICAgICAvLyB0aGlzLmJhc2VDYWRVcmwgPSAnaHR0cDovL2dsZW5kYWxlLW1vZGVsLmJpbXRrLmNvbScNCiAgICAgIHRoaXMuYmFzZUNhZFVybCA9ICdodHRwOi8vZ2xlbmRhbGUtbW9kZWwtZGV2LmJpbXRrLnRlY2gnDQogICAgfSBlbHNlIHsNCiAgICAgIGdldENvbmZpZ3VyZSh7IGNvZGU6ICdnbGVuZGFsZV91cmwnIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmJhc2VDYWRVcmwgPSByZXMuRGF0YQ0KICAgICAgfSkNCiAgICB9DQogIH0sDQogIGFzeW5jIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0UHJvY2Vzc0xpc3QoKQ0KICAgIHRoaXMudGJEYXRhTWFwID0ge30NCiAgICB0aGlzLmNyYWZ0Q29kZU1hcCA9IHt9DQogICAgdGhpcy5wYWdlVHlwZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnBnX3R5cGUNCiAgICB0aGlzLmxldmVsID0gdGhpcy4kcm91dGUucXVlcnkubGV2ZWwNCiAgICB0aGlzLnBhZ2VTdGF0dXMgPSB0aGlzLiRyb3V0ZS5xdWVyeS5zdGF0dXMNCiAgICB0aGlzLm1vZGVsID0gdGhpcy4kcm91dGUucXVlcnkubW9kZWwNCiAgICB0aGlzLnNjaGVkdWxlSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5waWQgfHwgJycNCiAgICAvLyAvLyB0aGlzLmZvcm1JbmxpbmUuQ3JlYXRlX1VzZXJOYW1lID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lDQogICAgLy8gLy8g5qGG5p626Zeu6aKY5byV6LW3c3RvcmXmlbDmja7kuKLlpLHvvIzlt7Llj43ppojvvIznu5PmnpzvvJrmraTlpITlhYjkvb/nlKhsb2NhbFN0b3JhZ2UNCiAgICB0aGlzLmZvcm1JbmxpbmUuQ3JlYXRlX1VzZXJOYW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1VzZXJBY2NvdW50JykNCiAgICAvLyBpZiAoIXRoaXMuaXNDb20pIHsNCiAgICAvLyAgIHRoaXMuZ2V0UGFydFR5cGUoKQ0KICAgIC8vIH0gZWxzZSB7DQogICAgLy8gfQ0KDQogICAgdGhpcy51bmlxdWUgPSB1bmlxdWVDb2RlKCkNCiAgICB0aGlzLmNoZWNrV29ya3Nob3BJc09wZW4oKQ0KDQogICAgdGhpcy5zZWFyY2ggPSBkZWJvdW5jZSh0aGlzLmZldGNoRGF0YSwgODAwLCB0cnVlKQ0KICAgIGF3YWl0IHRoaXMubWVyZ2VDb25maWcoKQ0KICAgIGlmICh0aGlzLmlzVmlldyB8fCB0aGlzLmlzRWRpdCkgew0KICAgICAgY29uc3QgeyBhcmVhSWQsIGluc3RhbGwgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5DQogICAgICAvLyB0aGlzLmFyZWFJZCA9IGFyZWFJZA0KICAgICAgLy8gdGhpcy5mb3JtSW5saW5lLkluc3RhbGxVbml0X0lkID0gaW5zdGFsbA0KICAgICAgLy8gdGhpcy5nZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoKQ0KICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgIH0NCg0KICAgIGlmICh0aGlzLmlzQWRkKSB7DQogICAgICAvLyB0aGlzLmZldGNoVHJlZURhdGEoKQ0KICAgICAgdGhpcy5nZXRUeXBlKCkNCiAgICB9DQogICAgaWYgKHRoaXMuaXNFZGl0KSB7DQogICAgICB0aGlzLmdldFR5cGUoKQ0KICAgIH0NCg0KICAgIHRoaXMubGV2ZWxOYW1lID0gYXdhaXQgZ2V0Qm9tTmFtZSh0aGlzLmxldmVsKQ0KDQogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCB0aGlzLmZyYW1lTGlzdGVuZXIpDQogICAgdGhpcy4kb25jZSgnaG9vazpiZWZvcmVEZXN0cm95JywgKCkgPT4gew0KICAgICAgY29uc29sZS5sb2coJ2RlYWN0aXZhdGVkJykNCiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdtZXNzYWdlJywgdGhpcy5mcmFtZUxpc3RlbmVyKQ0KICAgIH0pDQogIH0sDQogIGFjdGl2YXRlZCgpIHsNCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignbWVzc2FnZScsIHRoaXMuZnJhbWVMaXN0ZW5lcikNCiAgICB0aGlzLiRvbmNlKCdob29rOmRlYWN0aXZhdGVkJywgKCkgPT4gew0KICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCB0aGlzLmZyYW1lTGlzdGVuZXIpDQogICAgfSkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC4uLm1hcEFjdGlvbnMoJ3NjaGVkdWxlJywgWydjaGFuZ2VQcm9jZXNzTGlzdCcsICdpbml0UHJvY2Vzc0xpc3QnLCAnY2hhbmdlQWRkVGJLZXlzJ10pLA0KICAgIGNoZWNrT3duZXIoKSB7DQogICAgICBpZiAodGhpcy5pc0NvbSkgcmV0dXJuDQogICAgICB0aGlzLmlzT3duZXJOdWxsID0gdGhpcy50YkRhdGEuZXZlcnkodiA9PiAhdi5Db21wX0ltcG9ydF9EZXRhaWxfSWQpICYmICF0aGlzLmlzTmVzdA0KICAgICAgY29uc3QgaWR4ID0gdGhpcy5jb2x1bW5zLmZpbmRJbmRleCh2ID0+IHYuQ29kZSA9PT0gJ1BhcnRfVXNlZF9Qcm9jZXNzJykNCiAgICAgIGlmICh0aGlzLmlzT3duZXJOdWxsKSB7DQogICAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBpZiAoaWR4ID09PSAtMSkgew0KICAgICAgICAgIGlmICghdGhpcy5vd25lckNvbHVtbikgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliJfooajphY3nva7lrZfmrrXnvLrlsJHpg6jku7bpoobnlKjlt6Xluo/lrZfmrrUnLA0KICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5jb2x1bW5zLnB1c2godGhpcy5vd25lckNvbHVtbikNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmNvbVBhcnQgPSB0cnVlDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBtZXJnZUNvbmZpZygpIHsNCiAgICAgIGF3YWl0IHRoaXMuZ2V0Q29uZmlnKCkNCiAgICAgIGF3YWl0IHRoaXMuZ2V0V29ya1RlYW0oKQ0KICAgIH0sDQogICAgZG9GaWx0ZXIoKSB7DQogICAgICB0aGlzLnByb2plY3RMaXN0ID0gW10NCiAgICAgIHRoaXMuaW5zdGFsbExpc3QgPSBbXQ0KICAgICAgdGhpcy5hcmVhTGlzdCA9IFtdDQogICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKGN1ciA9PiB7DQogICAgICAgIGlmIChjdXIuUHJvamVjdF9OYW1lICYmICF0aGlzLnByb2plY3RMaXN0LmluY2x1ZGVzKGN1ci5Qcm9qZWN0X05hbWUpKSB7DQogICAgICAgICAgdGhpcy5wcm9qZWN0TGlzdC5wdXNoKGN1ci5Qcm9qZWN0X05hbWUpDQogICAgICAgIH0NCiAgICAgICAgaWYgKGN1ci5JbnN0YWxsVW5pdF9OYW1lICYmICF0aGlzLmluc3RhbGxMaXN0LmluY2x1ZGVzKGN1ci5JbnN0YWxsVW5pdF9OYW1lKSkgew0KICAgICAgICAgIHRoaXMuaW5zdGFsbExpc3QucHVzaChjdXIuSW5zdGFsbFVuaXRfTmFtZSkNCiAgICAgICAgfQ0KICAgICAgICBpZiAoY3VyLkFyZWFfTmFtZSAmJiAhdGhpcy5hcmVhTGlzdC5pbmNsdWRlcyhjdXIuQXJlYV9OYW1lKSkgew0KICAgICAgICAgIHRoaXMuYXJlYUxpc3QucHVzaChjdXIuQXJlYV9OYW1lKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0Q29uZmlnKCkgew0KICAgICAgbGV0IGNvbmZpZ0NvZGUgPSAnJw0KICAgICAgaWYgKHRoaXMuaXNOZXN0KSB7DQogICAgICAgIGlmICh0aGlzLmlzVmlldykgew0KICAgICAgICAgIGNvbmZpZ0NvZGUgPSAnUFJPTmVzdGluZ1NjaGVkdWxlRGV0YWlsJw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbmZpZ0NvZGUgPSAnUFJPTmVzdGluZ1NjaGVkdWxlQ29uZmlnJw0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25maWdDb2RlID0gKHRoaXMuaXNWaWV3ID8gJ1BST1VuaXRQYXJ0Vmlld1BhZ2VUYkNvbmZpZ19uZXcnIDogJ1BST1VuaXRQYXJ0RHJhZnRQYWdlVGJDb25maWdfbmV3JykNCiAgICAgIH0NCiAgICAgIHRoaXMuZ3JpZENvZGUgPSBjb25maWdDb2RlDQogICAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKGNvbmZpZ0NvZGUpDQogICAgICBpZiAoIXRoaXMud29ya3Nob3BFbmFibGVkKSB7DQogICAgICAgIHRoaXMuY29sdW1ucyA9IHRoaXMuY29sdW1ucy5maWx0ZXIodiA9PiB2LkNvZGUgIT09ICdXb3Jrc2hvcF9OYW1lJykNCiAgICAgIH0NCiAgICAgIHRoaXMuY2hlY2tPd25lcigpDQogICAgfSwNCiAgICBhc3luYyBjaGFuZ2VDb2x1bW4oKSB7DQogICAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKHRoaXMuZ3JpZENvZGUpDQogICAgICB0aGlzLnRiS2V5KysNCiAgICB9LA0KICAgIC8qICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7DQogICAgICBjb25zb2xlLmxvZygnZGF0YScsIGRhdGEpDQogICAgICBpZiAodGhpcy5hcmVhSWQgPT09IGRhdGEuSWQpIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLg0KICAgICAgID0gdHJ1ZQ0KICAgICAgaWYgKCFkYXRhLlBhcmVudE5vZGVzIHx8IGRhdGEuQ2hpbGRyZW4/Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAoZGF0YT8uRGF0YVt0aGlzLnN0YXR1c0NvZGVdID09PSAn5pyq5a+85YWlJykgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5riF5Y2V5pyq5a+85YWl77yM6K+36IGU57O75rex5YyW5Lq65ZGY5a+85YWl5riF5Y2VJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIGNvbnN0IGluaXREYXRhID0gKHsgRGF0YSB9KSA9PiB7DQogICAgICAgIHRoaXMuYXJlYUlkID0gRGF0YS5JZA0KICAgICAgICB0aGlzLnByb2plY3RJZCA9IERhdGEuUHJvamVjdF9JZA0KICAgICAgICB0aGlzLmV4cGFuZGVkS2V5ID0gdGhpcy5hcmVhSWQNCiAgICAgICAgdGhpcy5mb3JtSW5saW5lLkZpbmlzaF9EYXRlID0gJycNCiAgICAgICAgdGhpcy5mb3JtSW5saW5lLkluc3RhbGxVbml0X0lkID0gJycNCiAgICAgICAgdGhpcy5mb3JtSW5saW5lLlJlbWFyayA9ICcnDQogICAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgICAgdGhpcy5nZXRBcmVhSW5mbygpDQogICAgICAgIHRoaXMuZ2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0KCkNCiAgICAgIH0NCg0KICAgICAgaWYgKHRoaXMudGJEYXRhLmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRjb25maXJtKCfliIfmjaLljLrln5/lj7PkvqfmlbDmja7muIXnqbrvvIzmmK/lkKbnoa7orqQ/JywgJ+aPkOekuicsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIGluaXREYXRhKGRhdGEpDQogICAgICAgICAgdGhpcy5kaXNhYmxlZEFkZCA9IGZhbHNlDQogICAgICAgICAgdGhpcy50YkRhdGFNYXAgPSB7fQ0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmRpc2FibGVkQWRkID0gZmFsc2UNCiAgICAgICAgaW5pdERhdGEoZGF0YSkNCiAgICAgIH0NCiAgICB9LCovDQoNCiAgICAvKiBjdXN0b21GaWx0ZXJGdW4odmFsdWUsIGRhdGEsIG5vZGUpIHsNCiAgICAgIGNvbnN0IGFyciA9IHZhbHVlLnNwbGl0KFNQTElUX1NZTUJPTCkNCiAgICAgIGNvbnN0IGxhYmVsVmFsID0gYXJyWzBdDQogICAgICBjb25zdCBzdGF0dXNWYWwgPSBhcnJbMV0NCiAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlDQogICAgICBsZXQgcGFyZW50Tm9kZSA9IG5vZGUucGFyZW50DQogICAgICBsZXQgbGFiZWxzID0gW25vZGUubGFiZWxdDQogICAgICBsZXQgc3RhdHVzID0gW2RhdGEuRGF0YVt0aGlzLnN0YXR1c0NvZGVdXQ0KICAgICAgbGV0IGxldmVsID0gMQ0KICAgICAgd2hpbGUgKGxldmVsIDwgbm9kZS5sZXZlbCkgew0KICAgICAgICBsYWJlbHMgPSBbLi4ubGFiZWxzLCBwYXJlbnROb2RlLmxhYmVsXQ0KICAgICAgICBzdGF0dXMgPSBbLi4uc3RhdHVzLCBkYXRhLkRhdGFbdGhpcy5zdGF0dXNDb2RlXV0NCiAgICAgICAgcGFyZW50Tm9kZSA9IHBhcmVudE5vZGUucGFyZW50DQogICAgICAgIGxldmVsKysNCiAgICAgIH0NCiAgICAgIGxhYmVscyA9IGxhYmVscy5maWx0ZXIodiA9PiAhIXYpDQogICAgICBzdGF0dXMgPSBzdGF0dXMuZmlsdGVyKHYgPT4gISF2KQ0KICAgICAgbGV0IHJlc3VsdExhYmVsID0gdHJ1ZQ0KICAgICAgbGV0IHJlc3VsdFN0YXR1cyA9IHRydWUNCiAgICAgIGlmICh0aGlzLnN0YXR1c1R5cGUpIHsNCiAgICAgICAgcmVzdWx0U3RhdHVzID0gc3RhdHVzLnNvbWUocyA9PiBzLmluZGV4T2Yoc3RhdHVzVmFsKSAhPT0gLTEpDQogICAgICB9DQogICAgICBpZiAodGhpcy5wcm9qZWN0TmFtZSkgew0KICAgICAgICByZXN1bHRMYWJlbCA9IGxhYmVscy5zb21lKHMgPT4gcy5pbmRleE9mKGxhYmVsVmFsKSAhPT0gLTEpDQogICAgICB9DQogICAgICByZXR1cm4gcmVzdWx0TGFiZWwgJiYgcmVzdWx0U3RhdHVzDQogICAgfSwqLw0KICAgIGFzeW5jIGZldGNoRGF0YSgpIHsNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgbGV0IHJlc0RhdGEgPSBudWxsDQogICAgICBpZiAodGhpcy5pc05lc3QpIHsNCiAgICAgICAgaWYgKHRoaXMuaXNWaWV3KSB7DQogICAgICAgICAgcmVzRGF0YSA9IGF3YWl0IHRoaXMuZ2V0UGFydFBhZ2VMaXN0KCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXNEYXRhID0gYXdhaXQgdGhpcy5nZXROZXN0UGFnZUxpc3QoKQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXNEYXRhID0gYXdhaXQgdGhpcy5nZXRQYXJ0UGFnZUxpc3QoKQ0KICAgICAgfQ0KDQogICAgICB0aGlzLmluaXRUYkRhdGEocmVzRGF0YSkNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICB9LA0KICAgIGZldGNoVHJlZURhdGFMb2NhbCgpIHsNCiAgICAgIC8vIHRoaXMuZmlsdGVyVGV4dCA9IHRoaXMucHJvamVjdE5hbWUNCiAgICB9LA0KICAgIGZldGNoVHJlZVN0YXR1cygpIHsNCiAgICAgIC8vIHRoaXMuZmlsdGVyVGV4dCA9IHRoaXMuc3RhdHVzVHlwZQ0KICAgIH0sDQogICAgLyogICAgZmV0Y2hUcmVlRGF0YSgpIHsNCiAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSB0cnVlDQogICAgICBHZXRQcm9qZWN0QXJlYVRyZWVMaXN0KHsgcHJvamVjdE5hbWU6IHRoaXMucHJvamVjdE5hbWUsIHR5cGU6IHRoaXMuaXNDb20gPyAxIDogMiB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5EYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGNvbnN0IHJlc0RhdGEgPSByZXMuRGF0YS5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgaXRlbS5Jc19EaXJlY3RvcnkgPSB0cnVlDQogICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy50cmVlRGF0YSA9IHJlc0RhdGENCiAgICAgICAgdGhpcy5zZXRLZXkoKQ0KICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwqLw0KICAgIC8vIHNldEtleSgpIHsNCiAgICAvLyAgIGNvbnN0IGRlZXBGaWx0ZXIgPSAodHJlZSkgPT4gew0KICAgIC8vICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRyZWUubGVuZ3RoOyBpKyspIHsNCiAgICAvLyAgICAgICBjb25zdCBpdGVtID0gdHJlZVtpXQ0KICAgIC8vICAgICAgIGNvbnN0IHsgRGF0YSwgQ2hpbGRyZW4gfSA9IGl0ZW0NCiAgICAvLyAgICAgICBjb25zb2xlLmxvZyhEYXRhKQ0KICAgIC8vICAgICAgIGlmIChEYXRhLlBhcmVudElkICYmICFDaGlsZHJlbj8ubGVuZ3RoKSB7DQogICAgLy8gICAgICAgICB0aGlzLmhhbmRsZU5vZGVDbGljayhpdGVtKQ0KICAgIC8vICAgICAgICAgcmV0dXJuDQogICAgLy8gICAgICAgfSBlbHNlIHsNCiAgICAvLyAgICAgICAgIGlmIChDaGlsZHJlbiAmJiBDaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgLy8gICAgICAgICAgIHJldHVybiBkZWVwRmlsdGVyKENoaWxkcmVuKQ0KICAgIC8vICAgICAgICAgfQ0KICAgIC8vICAgICAgIH0NCiAgICAvLyAgICAgfQ0KICAgIC8vICAgfQ0KICAgIC8vICAgcmV0dXJuIGRlZXBGaWx0ZXIodGhpcy50cmVlRGF0YSkNCiAgICAvLyB9LA0KICAgIGNsb3NlVmlldygpIHsNCiAgICAgIGNsb3NlVGFnVmlldyh0aGlzLiRzdG9yZSwgdGhpcy4kcm91dGUpDQogICAgfSwNCiAgICBjaGVja1dvcmtzaG9wSXNPcGVuKCkgew0KICAgICAgdGhpcy53b3JrU2hvcElzT3BlbiA9IHRydWUNCiAgICB9LA0KICAgIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7DQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gYXJyYXkucmVjb3Jkcw0KICAgIH0sDQogICAgZ2V0QXJlYUluZm8oKSB7DQogICAgICB0aGlzLmZvcm1JbmxpbmUuRmluaXNoX0RhdGUgPSAnJw0KICAgICAgQXJlYUdldEVudGl0eSh7DQogICAgICAgIGlkOiB0aGlzLmFyZWFJZA0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGlmICghcmVzLkRhdGEpIHsNCiAgICAgICAgICAgIHJldHVybiBbXQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbW9tZW50KHJlcy5EYXRhPy5EZW1hbmRfQmVnaW5fRGF0ZSkNCiAgICAgICAgICBjb25zdCBlbmQgPSBtb21lbnQocmVzLkRhdGE/LkRlbWFuZF9FbmRfRGF0ZSkNCiAgICAgICAgICB0aGlzLnBpY2tlck9wdGlvbnMuZGlzYWJsZWREYXRlID0gKHRpbWUpID0+IHsNCiAgICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA8IHN0YXJ0IHx8IHRpbWUuZ2V0VGltZSgpID4gZW5kDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgIHRoaXMub3BlbkFkZERyYWZ0ID0gZmFsc2UNCiAgICB9LA0KICAgIGdldE5lc3RQYWdlTGlzdCgpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7DQogICAgICAgIEdldENhblNjaGR1bGluZ1BhcnRMaXN0KHsNCiAgICAgICAgICBJZHM6IHRoaXMubmVzdElkcw0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IF9saXN0ID0gcmVzPy5EYXRhIHx8IFtdDQogICAgICAgICAgICBjb25zdCBsaXN0ID0gX2xpc3QubWFwKHYgPT4gew0KICAgICAgICAgICAgICB2LlBhcnRfVXNlZF9Qcm9jZXNzID0gdi5TY2hlZHVsZWRfVXNlZF9Qcm9jZXNzIHx8IHYuUGFydF9UeXBlX1VzZWRfUHJvY2Vzcw0KICAgICAgICAgICAgICAvLyB2Lm9yaWdpbmFsUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCA/IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCA6ICcnDQogICAgICAgICAgICAgIHYuV29ya3Nob3BfSWQgPSB2LlNjaGVkdWxlZF9Xb3Jrc2hvcF9JZA0KICAgICAgICAgICAgICB2LldvcmtzaG9wX05hbWUgPSB2LlNjaGVkdWxlZF9Xb3Jrc2hvcF9OYW1lDQogICAgICAgICAgICAgIHYuVGVjaG5vbG9neV9QYXRoID0gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoIHx8IHYuVGVjaG5vbG9neV9QYXRoDQogICAgICAgICAgICAgIHYuY2hvb3NlQ291bnQgPSB2LkNhbl9TY2hkdWxpbmdfQ291bnQNCg0KICAgICAgICAgICAgICByZXR1cm4gdg0KICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgcmVzb2x2ZShsaXN0KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZWplY3QoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRQYXJ0UGFnZUxpc3QoKSB7DQogICAgICBjb25zdCB7DQogICAgICAgIHBpZA0KICAgICAgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5DQogICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBHZXRVbml0U2NoZHVsaW5nSW5mb0RldGFpbCh7DQogICAgICAgIFNjaGR1bGluZ19QbGFuX0lkOiBwaWQNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnN0IFNhcmVQYXJ0c01vZGVsID0gcmVzLkRhdGE/LlNhcmVQYXJ0c01vZGVsLm1hcCh2ID0+IHsNCiAgICAgICAgICAgIGlmICh2LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgICAgICAgLy8g5bey5a2Y5Zyo5pON5L2c6L+H5pWw5o2uDQogICAgICAgICAgICAgIHYuUGFydF9Vc2VkX1Byb2Nlc3MgPSB2LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgICAgIH0gaWYgKHYuVW5pdF9QYXJ0X1VzZWRfUHJvY2Vzcykgew0KICAgICAgICAgICAgICAvLyDlt7LlrZjlnKjmk43kvZzov4fmlbDmja4NCiAgICAgICAgICAgICAgdi5QYXJ0X1VzZWRfUHJvY2VzcyA9IHYuVW5pdF9QYXJ0X1VzZWRfUHJvY2Vzcw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdi5jaG9vc2VDb3VudCA9IHYuQ2FuX1NjaGR1bGluZ19Db3VudA0KICAgICAgICAgICAgcmV0dXJuIHYNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMuZm9ybUlubGluZSA9IE9iamVjdC5hc3NpZ24odGhpcy5mb3JtSW5saW5lLCByZXMuRGF0YT8uU2NoZHVsaW5nX1BsYW4pDQogICAgICAgICAgcmVzLkRhdGE/LlByb2Nlc3NfTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgY29uc3QgcGxpc3QgPSB7DQogICAgICAgICAgICAgIGtleTogaXRlbS5Qcm9jZXNzX0NvZGUsDQogICAgICAgICAgICAgIHZhbHVlOiBpdGVtDQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLmNoYW5nZVByb2Nlc3NMaXN0KHBsaXN0KQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICB0aGlzLmdldFN0b3BMaXN0KFNhcmVQYXJ0c01vZGVsKQ0KICAgICAgICAgIHJldHVybiBTYXJlUGFydHNNb2RlbA0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgICByZXR1cm4gW10NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGNvbnNvbGUubG9nKCdyZXN1bHQnLCByZXN1bHQpDQogICAgICByZXR1cm4gcmVzdWx0IHx8IFtdDQogICAgfSwNCiAgICBhc3luYyBnZXRTdG9wTGlzdChsaXN0KSB7DQogICAgICBjb25zb2xlLmxvZygnZ2V0U3RvcExpc3QnLCBsaXN0KQ0KICAgICAgY29uc3Qgc3VibWl0T2JqID0gbGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgSWQ6IGl0ZW0uUGFydF9BZ2dyZWdhdGVfSWQsDQogICAgICAgICAgVHlwZTogMw0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgYXdhaXQgR2V0U3RvcExpc3Qoc3VibWl0T2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3Qgc3RvcE1hcCA9IHt9DQogICAgICAgICAgcmVzLkRhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgIHN0b3BNYXBbaXRlbS5JZF0gPSAhIWl0ZW0uSXNfU3RvcA0KICAgICAgICAgIH0pDQogICAgICAgICAgbGlzdC5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBpZiAoc3RvcE1hcFtyb3cuUGFydF9BZ2dyZWdhdGVfSWRdKSB7DQogICAgICAgICAgICAgIHRoaXMuJHNldChyb3csICdzdG9wRmxhZycsIHN0b3BNYXBbcm93LlBhcnRfQWdncmVnYXRlX0lkXSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaW5pdFRiRGF0YShsaXN0LCB0ZWFtS2V5ID0gJ0FsbG9jYXRpb25fVGVhbXMnKSB7DQogICAgICBjb25zb2xlLmxvZyg1LCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGxpc3QpKSkNCiAgICAgIHRoaXMudGJEYXRhID0gbGlzdC5tYXAocm93ID0+IHsNCiAgICAgICAgY29uc3QgcHJvY2Vzc0xpc3QgPSByb3cuVGVjaG5vbG9neV9QYXRoPy5zcGxpdCgnLycpIHx8IFtdDQogICAgICAgIHJvdy51dWlkID0gdXVpZHY0KCkNCiAgICAgICAgdGhpcy5hZGRFbGVtZW50VG9UYkRhdGEocm93KQ0KICAgICAgICBpZiAocm93W3RlYW1LZXldKSB7DQogICAgICAgICAgY29uc3QgbmV3RGF0YSA9IHJvd1t0ZWFtS2V5XS5maWx0ZXIoKHIpID0+IHByb2Nlc3NMaXN0LmZpbmRJbmRleCgocCkgPT4gci5Qcm9jZXNzX0NvZGUgPT09IHApICE9PSAtMSkNCiAgICAgICAgICBuZXdEYXRhLmZvckVhY2goKGVsZSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShyb3cudXVpZCwgZWxlLlByb2Nlc3NfQ29kZSwgZWxlLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgICAgIGNvbnN0IG1heCA9IHRoaXMuZ2V0Um93VW5pcXVlTWF4KHJvdy51dWlkLCBlbGUuUHJvY2Vzc19Db2RlLCBlbGUuV29ya2luZ19UZWFtX0lkKQ0KICAgICAgICAgICAgcm93W2NvZGVdID0gZWxlLkNvdW50DQogICAgICAgICAgICByb3dbbWF4XSA9IDANCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuc2V0SW5wdXRNYXgocm93KQ0KICAgICAgICByZXR1cm4gcm93DQogICAgICB9KQ0KICAgICAgbGV0IGlkcyA9ICcnDQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBpZHMgPSB0aGlzLnRiRGF0YS5tYXAodiA9PiB2LkNvbXBfSW1wb3J0X0RldGFpbF9JZCkudG9TdHJpbmcoKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWRzID0gdGhpcy50YkRhdGEubWFwKHYgPT4gdi5QYXJ0X0FnZ3JlZ2F0ZV9JZCkudG9TdHJpbmcoKQ0KICAgICAgfQ0KICAgICAgdGhpcy5jdXJyZW50SWRzID0gaWRzDQogICAgfSwNCiAgICBhc3luYyBhZGRUb1RiTGlzdChuZXdMaXN0KSB7DQogICAgICBhd2FpdCB0aGlzLm1lcmdlU2VsZWN0TGlzdChuZXdMaXN0KQ0KICAgICAgdGhpcy5zZXRBZGRUYktleSgpDQogICAgfSwNCiAgICBhc3luYyBtZXJnZVNlbGVjdExpc3QobmV3TGlzdCkgew0KICAgICAgY29uc29sZS50aW1lKCdmZmYnKQ0KICAgICAgYXdhaXQgdGhpcy5tZXJnZUNyYWZ0UHJvY2VzcyhuZXdMaXN0KQ0KICAgICAgbGV0IGhhc1VzZWRQYXJ0RmxhZyA9IHRydWUNCiAgICAgIG5ld0xpc3QuZm9yRWFjaCgoZWxlbWVudCwgaW5kZXgpID0+IHsNCiAgICAgICAgY29uc3QgY3VyID0gdGhpcy5nZXRNZXJnZVVuaXF1ZVJvdyhlbGVtZW50KQ0KICAgICAgICBjb25zb2xlLmxvZygxLCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGVsZW1lbnQpKSkNCiAgICAgICAgaWYgKCFlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICAgIGNvbnN0IGN1clBhdGhBcnIgPSB0aGlzLmNyYWZ0Q29kZU1hcFtlbGVtZW50LlRlY2hub2xvZ3lfQ29kZV0NCiAgICAgICAgICBpZiAodGhpcy5jcmFmdENvZGVNYXBbZWxlbWVudC5UZWNobm9sb2d5X0NvZGVdIGluc3RhbmNlb2YgQXJyYXkpIHsNCiAgICAgICAgICAgIGlmIChlbGVtZW50LlVuaXRfUGFydF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgICAgICAgY29uc3QgcGFydFVzZWRQcm9jZXNzQXJyID0gZWxlbWVudC5Vbml0X1BhcnRfVXNlZF9Qcm9jZXNzLnNwbGl0KCcsJykNCiAgICAgICAgICAgICAgY29uc3QgYWxsUGFydHNJbmNsdWRlZCA9IHBhcnRVc2VkUHJvY2Vzc0Fyci5ldmVyeShwYXJ0ID0+IGN1clBhdGhBcnIuaW5jbHVkZXMocGFydCkpDQoNCiAgICAgICAgICAgICAgaWYgKCFhbGxQYXJ0c0luY2x1ZGVkKSB7DQogICAgICAgICAgICAgICAgaGFzVXNlZFBhcnRGbGFnID0gZmFsc2UNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCA9IGN1clBhdGhBcnIuam9pbignLycpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoID0gY3VyUGF0aEFyci5qb2luKCcvJykNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYgKCFjdXIpIHsNCiAgICAgICAgICBlbGVtZW50LnB1dWlkID0gZWxlbWVudC51dWlkDQogICAgICAgICAgZWxlbWVudC5TY2hkdWxlZF9Db3VudCA9IGVsZW1lbnQuY2hvb3NlQ291bnQNCiAgICAgICAgICBlbGVtZW50LlNjaGR1bGVkX1dlaWdodCA9IG51bWVyYWwoZWxlbWVudC5jaG9vc2VDb3VudCAqIGVsZW1lbnQuV2VpZ2h0KS5mb3JtYXQoJzAuWzAwXScpDQogICAgICAgICAgdGhpcy50YkRhdGEucHVzaChlbGVtZW50KQ0KICAgICAgICAgIHRoaXMuYWRkRWxlbWVudFRvVGJEYXRhKGVsZW1lbnQpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICBjdXIucHV1aWQgPSBlbGVtZW50LnV1aWQNCg0KICAgICAgICBjdXIuU2NoZHVsZWRfQ291bnQgKz0gZWxlbWVudC5jaG9vc2VDb3VudA0KICAgICAgICBjdXIuU2NoZHVsZWRfV2VpZ2h0ID0gbnVtZXJhbChjdXIuU2NoZHVsZWRfV2VpZ2h0KS5hZGQoZWxlbWVudC5jaG9vc2VDb3VudCAqIGVsZW1lbnQuV2VpZ2h0KS5mb3JtYXQoJzAuWzAwXScpDQogICAgICAgIGlmICghY3VyLlRlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIHRoaXMuc2V0SW5wdXRNYXgoY3VyKQ0KICAgICAgfSkNCiAgICAgIGNvbnNvbGUubG9nKCdoYXNVc2VkUGFydEZsYWcnLCBoYXNVc2VkUGFydEZsYWcpDQogICAgICB0aGlzLnNob3dDcmFmdFVzZWRQYXJ0UmVzdWx0KGhhc1VzZWRQYXJ0RmxhZykNCiAgICAgIC8vIGlmICh0aGlzLmlzQ29tKSB7DQogICAgICAvLyAgIHRoaXMudGJEYXRhLnNvcnQoKGEsIGIpID0+IGEuaW5pdFJvd0luZGV4IC0gYi5pbml0Um93SW5kZXgpDQogICAgICAvLyB9IGVsc2Ugew0KICAgICAgLy8gICB0aGlzLnRiRGF0YS5zb3J0KChhLCBiKSA9PiBhLlBhcnRfQ29kZS5sb2NhbGVDb21wYXJlKGIuUGFydF9Db2RlKSkNCiAgICAgIC8vIH0NCiAgICAgIHRoaXMudGJEYXRhLnNvcnQoKGEsIGIpID0+IGEuaW5pdFJvd0luZGV4IC0gYi5pbml0Um93SW5kZXgpDQogICAgICBjb25zb2xlLnRpbWVFbmQoJ2ZmZicpDQogICAgICBjb25zb2xlLmxvZygndGhpcy50YkRhdGFNYXAnLCB0aGlzLnRiRGF0YU1hcCwgdGhpcy50YkRhdGEpDQogICAgfSwNCiAgICBzaG93Q3JhZnRVc2VkUGFydFJlc3VsdChoYXNVc2VkUGFydCkgew0KICAgICAgaWYgKGhhc1VzZWRQYXJ0KSByZXR1cm4gdHJ1ZQ0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMuJGFsZXJ0KCfpg6jliIbpg6jku7blt6Xluo/ot6/lvoTlhoXkuI3ljIXlkKvpm7bku7bpoobnlKjlt6Xluo/or7fmiYvliqjmjpLkuqcnLCAn5o+Q56S6Jywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJw0KICAgICAgICB9KQ0KICAgICAgfSwgMjAwKQ0KICAgICAgcmV0dXJuIGZhbHNlDQogICAgfSwNCiAgICBhZGRFbGVtZW50VG9UYkRhdGEoZWxlbWVudCkgew0KICAgICAgY29uc3Qga2V5ID0gdGhpcy5nZXRVbmlLZXkoZWxlbWVudCkNCiAgICAgIHRoaXMudGJEYXRhTWFwW2tleV0gPSBlbGVtZW50DQogICAgfSwNCiAgICBnZXRNZXJnZVVuaXF1ZVJvdyhlbGVtZW50KSB7DQogICAgICBjb25zdCBrZXkgPSB0aGlzLmdldFVuaUtleShlbGVtZW50KQ0KICAgICAgcmV0dXJuIHRoaXMudGJEYXRhTWFwW2tleV0NCiAgICB9LA0KICAgIGdldFVuaUtleShlbGVtZW50KSB7DQogICAgICByZXR1cm4gZ2V0VW5pcXVlKHRoaXMuaXNDb20sIGVsZW1lbnQpDQogICAgfSwNCiAgICBjaGVja0Zvcm0oKSB7DQogICAgICBsZXQgaXNWYWxpZGF0ZSA9IHRydWUNCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm1JbmxpbmUnXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKCF2YWxpZCkgaXNWYWxpZGF0ZSA9IGZhbHNlDQogICAgICB9KQ0KICAgICAgcmV0dXJuIGlzVmFsaWRhdGUNCiAgICB9LA0KICAgIGFzeW5jIHNhdmVEcmFmdChpc09yZGVyID0gZmFsc2UpIHsNCiAgICAgIGNvbnN0IGNoZWNrU3VjY2VzcyA9IHRoaXMuY2hlY2tGb3JtKCkNCiAgICAgIGlmICghY2hlY2tTdWNjZXNzKSByZXR1cm4gZmFsc2UNCiAgICAgIGNvbnN0IHsgdGFibGVEYXRhLCBzdGF0dXMgfSA9IHRoaXMuZ2V0U3VibWl0VGJJbmZvKCkNCiAgICAgIGlmICghc3RhdHVzKSByZXR1cm4gZmFsc2UNCiAgICAgIGlmICghaXNPcmRlcikgew0KICAgICAgICB0aGlzLnNhdmVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgfQ0KDQogICAgICBjb25zdCBpc1N1Y2Nlc3MgPSBhd2FpdCB0aGlzLmhhbmRsZVNhdmVEcmFmdCh0YWJsZURhdGEsIGlzT3JkZXIpDQogICAgICBjb25zb2xlLmxvZygnaXNTdWNjZXNzJywgaXNTdWNjZXNzKQ0KICAgICAgaWYgKCFpc1N1Y2Nlc3MpIHJldHVybiBmYWxzZQ0KICAgICAgaWYgKGlzT3JkZXIpIHJldHVybiBpc1N1Y2Nlc3MNCiAgICAgIHRoaXMuJHJlZnNbJ2RyYWZ0J10/LmZldGNoRGF0YSgpDQogICAgICB0aGlzLnNhdmVMb2FkaW5nID0gZmFsc2UNCiAgICB9LA0KICAgIGFzeW5jIHNhdmVXb3JrU2hvcCgpIHsNCiAgICAgIGNvbnN0IGNoZWNrU3VjY2VzcyA9IHRoaXMuY2hlY2tGb3JtKCkNCiAgICAgIGlmICghY2hlY2tTdWNjZXNzKSByZXR1cm4gZmFsc2UNCiAgICAgIGNvbnN0IG9iaiA9IHt9DQogICAgICBpZiAoIXRoaXMudGJEYXRhLmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5pWw5o2u5LiN6IO95Li656m6JywNCiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBvYmouU2NoZHVsaW5nX0NvbXBzID0gdGhpcy50YkRhdGENCiAgICAgIH0gZWxzZSB7DQogICAgICAgIG9iai5TYXJlUGFydHNNb2RlbCA9IHRoaXMudGJEYXRhDQogICAgICB9DQogICAgICBpZiAodGhpcy5pc0VkaXQpIHsNCiAgICAgICAgb2JqLlNjaGR1bGluZ19QbGFuID0gdGhpcy5mb3JtSW5saW5lDQogICAgICB9IGVsc2Ugew0KICAgICAgICBvYmouU2NoZHVsaW5nX1BsYW4gPSB7DQogICAgICAgICAgLi4udGhpcy5mb3JtSW5saW5lLA0KICAgICAgICAgIFByb2plY3RfSWQ6IHRoaXMucHJvamVjdElkLA0KICAgICAgICAgIEFyZWFfSWQ6IHRoaXMuYXJlYUlkLA0KICAgICAgICAgIFNjaGR1bGluZ19Nb2RlbDogdGhpcy5tb2RlbCAvLyAx5p6E5Lu25Y2V54us5o6S5Lqn77yMMumbtuS7tuWNleeLrOaOkuS6p++8jDPmnoQv6Zu25Lu25LiA6LW35o6S5LqnDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMucGdMb2FkaW5nID0gdHJ1ZQ0KDQogICAgICBTYXZlVW5pdFNjaGVkdWxpbmdXb3Jrc2hvcE5ldyhvYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnBnTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJywNCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5jbG9zZVZpZXcoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnBnTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRTdWJtaXRUYkluZm8oKSB7DQogICAgICAvLyDlpITnkIbkuIrkvKDnmoTmlbDmja4NCiAgICAgIGxldCB0YWJsZURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMudGJEYXRhKSkNCiAgICAgIHRhYmxlRGF0YSA9IHRhYmxlRGF0YS5maWx0ZXIoaXRlbSA9PiBpdGVtLlNjaGR1bGVkX0NvdW50ID4gMCkNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGFibGVEYXRhLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGVsZW1lbnQgPSB0YWJsZURhdGFbaV0NCiAgICAgICAgbGV0IGxpc3QgPSBbXQ0KICAgICAgICBpZiAoIWVsZW1lbnQuVGVjaG5vbG9neV9QYXRoKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAn5bel5bqP5LiN6IO95Li656m6JywNCiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICAgIH0pDQogICAgICAgICAgcmV0dXJuIHsgc3RhdHVzOiBmYWxzZSB9DQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuaXNQYXJ0UHJlcGFyZSAmJiAhZWxlbWVudC5QYXJ0X1VzZWRfUHJvY2VzcyAmJiBlbGVtZW50LlR5cGUgIT09ICdEaXJlY3QnICYmIHRoaXMuY29tUGFydCkgew0KICAgICAgICAgIGNvbnN0IG1zZyA9ICfpoobnlKjlt6Xluo/kuI3og73kuLrnqbonDQogICAgICAgICAgaWYgKHRoaXMuaXNOZXN0KSB7DQogICAgICAgICAgICBpZiAoZWxlbWVudC5Db21wX0ltcG9ydF9EZXRhaWxfSWQpIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogbXNnLA0KICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICByZXR1cm4geyBzdGF0dXM6IGZhbHNlIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IG1zZywNCiAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmV0dXJuIHsgc3RhdHVzOiBmYWxzZSB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC8vIGlmICghdGhpcy5pc0NvbSAmJiBlbGVtZW50LkNvbXBfSW1wb3J0X0RldGFpbF9JZCAmJiAhZWxlbWVudC5QYXJ0X1VzZWRfUHJvY2Vzcykgew0KICAgICAgICAvLyAgIC8vIOmbtuaehOS7tiDpm7bku7bljZXni6zmjpLkuqcNCiAgICAgICAgLy8gICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgLy8gICAgIG1lc3NhZ2U6ICfpm7bku7bpoobnlKjlt6Xluo/kuI3og73kuLrnqbonLA0KICAgICAgICAvLyAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIC8vICAgfSkNCiAgICAgICAgLy8gICByZXR1cm4geyBzdGF0dXM6IGZhbHNlIH0NCiAgICAgICAgLy8gfQ0KICAgICAgICBpZiAoZWxlbWVudC5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoICYmIGVsZW1lbnQuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCAhPT0gZWxlbWVudC5UZWNobm9sb2d5X1BhdGgpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IGDor7flkozor6XljLrln5/mibnmrKHkuIvlt7LmjpLkuqflkIwke3RoaXMuaXNDb20gPyAn5p6E5Lu2JyA6ICfpg6jku7YnfeS/neaMgeW3peW6j+S4gOiHtGAsDQogICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHJldHVybiB7IHN0YXR1czogZmFsc2UgfQ0KICAgICAgICB9DQogICAgICAgIGlmIChlbGVtZW50LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MgJiYgZWxlbWVudC5TY2hlZHVsZWRfVXNlZF9Qcm9jZXNzICE9PSBlbGVtZW50LlBhcnRfVXNlZF9Qcm9jZXNzKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiBg6K+35ZKM6K+l5Yy65Z+f5om55qyh5LiL5bey5o6S5Lqn5ZCM6YOo5Lu26aKG55So5bel5bqP5L+d5oyB5LiA6Ie0YCwNCiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICAgIH0pDQogICAgICAgICAgcmV0dXJuIHsgc3RhdHVzOiBmYWxzZSB9DQogICAgICAgIH0NCiAgICAgICAgY29uc3QgcHJvY2Vzc0xpc3QgPSBBcnJheS5mcm9tKG5ldyBTZXQoZWxlbWVudC5UZWNobm9sb2d5X1BhdGguc3BsaXQoJy8nKSkpDQogICAgICAgIC8vIHByb2Nlc3NMaXN0LmZvckVhY2goY29kZSA9PiB7DQogICAgICAgIC8vICAgY29uc3QgZ3JvdXBzID0gZWxlbWVudC5BbGxvY2F0aW9uX1RlYW1zLmZpbHRlcih2ID0+IHYuUHJvY2Vzc19Db2RlID09PSBjb2RlKQ0KICAgICAgICAvLyAgICBjb25zdCBncm91cHNMaXN0ID0gZ3JvdXBzLm1hcChncm91cCA9PiB7DQogICAgICAgIC8vICAgY29uc3QgdUNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShlbGVtZW50LnV1aWQsIGNvZGUsIGdyb3VwLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgLy8gICBjb25zdCB1TWF4ID0gdGhpcy5nZXRSb3dVbmlxdWVNYXgoZWxlbWVudC51dWlkLCBjb2RlLCBncm91cC5Xb3JraW5nX1RlYW1fSWQpDQogICAgICAgIC8vICAgY29uc3Qgb2JqID0gew0KICAgICAgICAvLyAgICAgVGVhbV9UYXNrX0lkOiBlbGVtZW50LlRlYW1fVGFza19JZCwNCiAgICAgICAgLy8gICAgIENvbXBfQ29kZTogZWxlbWVudC5Db21wX0NvZGUsDQogICAgICAgIC8vICAgICBBZ2Fpbl9Db3VudDogK2VsZW1lbnRbdUNvZGVdIHx8IDAsIC8vIOS4jeWhq++8jOWQjuWPsOiuqeS8oDANCiAgICAgICAgLy8gICAgIFBhcnRfQ29kZTogdGhpcy5pc0NvbSA/IG51bGwgOiAnJywNCiAgICAgICAgLy8gICAgIFByb2Nlc3NfQ29kZTogY29kZSwNCiAgICAgICAgLy8gICAgIFRlY2hub2xvZ3lfUGF0aDogZWxlbWVudC5UZWNobm9sb2d5X1BhdGgsDQogICAgICAgIC8vICAgICBXb3JraW5nX1RlYW1fSWQ6IGdyb3VwLldvcmtpbmdfVGVhbV9JZCwNCiAgICAgICAgLy8gICAgIFdvcmtpbmdfVGVhbV9OYW1lOiBncm91cC5Xb3JraW5nX1RlYW1fTmFtZQ0KICAgICAgICAvLyAgIH0NCiAgICAgICAgLy8gICBkZWxldGUgZWxlbWVudFt1Q29kZV0NCiAgICAgICAgLy8gICBkZWxldGUgZWxlbWVudFt1TWF4XQ0KICAgICAgICAvLyAgIHJldHVybiBvYmoNCiAgICAgICAgLy8gfSkNCiAgICAgICAgLy8gY29uc3QgYWdhaW5Db3VudCA9IGxpc3QucmVkdWNlKChhY2MsIGN1cikgPT4gew0KICAgICAgICAvLyAgIHJldHVybiBhY2MgKyBjdXIuQWdhaW5fQ291bnQNCiAgICAgICAgLy8gfSwgMCkNCiAgICAgICAgLy8gaWYgKGFnYWluQ291bnQgPiBlbGVtZW50LlNjaGR1bGVkX0NvdW50KSB7DQogICAgICAgIC8vICAgZWxlbWVudC5BbGxvY2F0aW9uX1RlYW1zID0gW10NCiAgICAgICAgLy8gfQ0KDQogICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgcHJvY2Vzc0xpc3QubGVuZ3RoOyBqKyspIHsNCiAgICAgICAgICBjb25zdCBjb2RlID0gcHJvY2Vzc0xpc3Rbal0NCiAgICAgICAgICBjb25zdCBzY2hkdWxlZENvdW50ID0gZWxlbWVudC5TY2hkdWxlZF9Db3VudCB8fCAwDQogICAgICAgICAgbGV0IGdyb3VwcyA9IFtdDQogICAgICAgICAgaWYgKGVsZW1lbnQuQWxsb2NhdGlvbl9UZWFtcykgew0KICAgICAgICAgICAgZ3JvdXBzID0gZWxlbWVudC5BbGxvY2F0aW9uX1RlYW1zLmZpbHRlcih2ID0+IHYuUHJvY2Vzc19Db2RlID09PSBjb2RlKQ0KICAgICAgICAgIH0NCiAgICAgICAgICBjb25zdCBhZ2FpbkNvdW50ID0gZ3JvdXBzLnJlZHVjZSgoYWNjLCBjdXIpID0+IHsNCiAgICAgICAgICAgIHJldHVybiBhY2MgKyAoY3VyLkFnYWluX0NvdW50IHx8IDApDQogICAgICAgICAgfSwgMCkNCiAgICAgICAgICBpZiAoYWdhaW5Db3VudCA+IHNjaGR1bGVkQ291bnQpIHsNCiAgICAgICAgICAgIGxpc3QgPSBbXQ0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgbGlzdC5wdXNoKC4uLmdyb3VwcykNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCBoYXNJbnB1dCA9IE9iamVjdC5rZXlzKGVsZW1lbnQpLmZpbHRlcihfID0+IF8uc3RhcnRzV2l0aChlbGVtZW50Wyd1dWlkJ10pKQ0KICAgICAgICBoYXNJbnB1dC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgZGVsZXRlIGVsZW1lbnRbaXRlbV0NCiAgICAgICAgfSkNCiAgICAgICAgZGVsZXRlIGVsZW1lbnRbJ3V1aWQnXQ0KICAgICAgICBkZWxldGUgZWxlbWVudFsnX1hfUk9XX0tFWSddDQogICAgICAgIGRlbGV0ZSBlbGVtZW50WydwdXVpZCddDQogICAgICAgIGVsZW1lbnQuQWxsb2NhdGlvbl9UZWFtcyA9IGxpc3QNCiAgICAgIH0NCiAgICAgIHJldHVybiB7IHRhYmxlRGF0YSwgc3RhdHVzOiB0cnVlIH0NCiAgICB9LA0KICAgIGFzeW5jIGhhbmRsZVNhdmVEcmFmdCh0YWJsZURhdGEsIGlzT3JkZXIpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfkv53lrZjojYnnqL8nKQ0KICAgICAgY29uc3QgX2Z1biA9IFNhdmVVbml0U2NoZHVsaW5nRHJhZnROZXcNCiAgICAgIGNvbnN0IG9iaiA9IHt9DQogICAgICBvYmouU2FyZVBhcnRzTW9kZWwgPSB0YWJsZURhdGENCiAgICAgIGNvbnN0IHAgPSBbXQ0KICAgICAgZm9yIChjb25zdCBvYmpLZXkgaW4gdGhpcy5wcm9jZXNzTGlzdCkgew0KICAgICAgICBpZiAodGhpcy5wcm9jZXNzTGlzdC5oYXNPd25Qcm9wZXJ0eShvYmpLZXkpKSB7DQogICAgICAgICAgcC5wdXNoKHRoaXMucHJvY2Vzc0xpc3Rbb2JqS2V5XSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgb2JqLlByb2Nlc3NfTGlzdCA9IHANCg0KICAgICAgaWYgKHRoaXMuaXNFZGl0KSB7DQogICAgICAgIG9iai5TY2hkdWxpbmdfUGxhbiA9IHRoaXMuZm9ybUlubGluZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgb2JqLlNjaGR1bGluZ19QbGFuID0gew0KICAgICAgICAgIC4uLnRoaXMuZm9ybUlubGluZSwNCiAgICAgICAgICBQcm9qZWN0X0lkOiB0aGlzLnByb2plY3RJZCwNCiAgICAgICAgICBBcmVhX0lkOiB0aGlzLmFyZWFJZCwNCiAgICAgICAgICBTY2hkdWxpbmdfTW9kZWw6IHRoaXMubW9kZWwgLy8gMeaehOS7tuWNleeLrOaOkuS6p++8jDLpm7bku7bljZXni6zmjpLkuqfvvIwz5p6EL+mbtuS7tuS4gOi1t+aOkuS6pw0KICAgICAgICB9DQogICAgICB9DQogICAgICBsZXQgb3JkZXJTdWNjZXNzID0gZmFsc2UNCiAgICAgIGNvbnNvbGUubG9nKCdvYmonLCBvYmopDQoNCiAgICAgIGF3YWl0IF9mdW4ob2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgaWYgKCFpc09yZGVyKSB7DQogICAgICAgICAgICB0aGlzLnBnTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycsDQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMuY2xvc2VWaWV3KCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy50ZW1wbGF0ZVNjaGVkdWxlQ29kZSA9IHJlcy5EYXRhDQogICAgICAgICAgICBvcmRlclN1Y2Nlc3MgPSB0cnVlDQogICAgICAgICAgICBjb25zb2xlLmxvZygn5L+d5a2Y6I2J56i/5oiQ5YqfICcpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuc2F2ZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBjb25zb2xlLmxvZygn57uT5p2fICcpDQogICAgICByZXR1cm4gb3JkZXJTdWNjZXNzDQogICAgfSwNCiAgICBoYW5kbGVEZWxldGUoKSB7DQogICAgICB0aGlzLmRlbGV0ZUxvYWRpbmcgPSB0cnVlDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgY29uc3Qgc2VsZWN0ZWRVdWlkcyA9IG5ldyBTZXQodGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5tYXAodiA9PiB2LnV1aWQpKQ0KICAgICAgICB0aGlzLnRiRGF0YSA9IHRoaXMudGJEYXRhLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICBjb25zdCBpc1NlbGVjdGVkID0gc2VsZWN0ZWRVdWlkcy5oYXMoaXRlbS51dWlkKQ0KICAgICAgICAgIGlmIChpc1NlbGVjdGVkKSB7DQogICAgICAgICAgICBjb25zdCBrZXkgPSB0aGlzLmdldFVuaUtleShpdGVtKQ0KICAgICAgICAgICAgZGVsZXRlIHRoaXMudGJEYXRhTWFwW2tleV0NCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuICFpc1NlbGVjdGVkDQogICAgICAgIH0pDQogICAgICAgIC8vIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICAvLyAgIGNvbnN0IF9saXN0ID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5maWx0ZXIodiA9PiB2LnB1dWlkKQ0KICAgICAgICAvLyAgIHRoaXMuJHJlZnNbJ2RyYWZ0J10/Lm1lcmdlRGF0YShfbGlzdCkNCiAgICAgICAgLy8gICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW10NCiAgICAgICAgLy8gfSkNCiAgICAgICAgdGhpcy5kZWxldGVMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0sIDApDQogICAgfSwNCiAgICBhc3luYyBnZXRXb3JrVGVhbSgpIHsNCiAgICAgIGF3YWl0IEdldFNjaGR1bGluZ1dvcmtpbmdUZWFtcyh7DQogICAgICAgIHR5cGU6IDMNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLndvcmtpbmdUZWFtID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVTdWJtaXQoKSB7DQogICAgICB0aGlzLiRyZWZzWydmb3JtSW5saW5lJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICghdmFsaWQpIHJldHVybg0KICAgICAgICBjb25zdCB7IHRhYmxlRGF0YSwgc3RhdHVzIH0gPSB0aGlzLmdldFN1Ym1pdFRiSW5mbygpDQogICAgICAgIGlmICghc3RhdHVzKSByZXR1cm4NCiAgICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5o+Q5Lqk5b2T5YmN5pWw5o2uPycsICfmj5DnpLonLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnNhdmVEcmFmdERvU3VibWl0KHRhYmxlRGF0YSkNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIHNhdmVEcmFmdERvU3VibWl0KCkgew0KICAgICAgdGhpcy5wZ0xvYWRpbmcgPSB0cnVlDQogICAgICBpZiAodGhpcy5mb3JtSW5saW5lPy5TY2hkdWxpbmdfQ29kZSkgew0KICAgICAgICBjb25zdCBpc1N1Y2Nlc3MgPSBhd2FpdCB0aGlzLnNhdmVEcmFmdCh0cnVlKQ0KICAgICAgICBjb25zb2xlLmxvZygnc2F2ZURyYWZ0RG9TdWJtaXQnLCBpc1N1Y2Nlc3MpDQogICAgICAgIGlzU3VjY2VzcyAmJiB0aGlzLmRvU3VibWl0KHRoaXMuZm9ybUlubGluZS5JZCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnN0IGlzU3VjY2VzcyA9IGF3YWl0IHRoaXMuc2F2ZURyYWZ0KHRydWUpDQogICAgICAgIGlzU3VjY2VzcyAmJiB0aGlzLmRvU3VibWl0KHRoaXMudGVtcGxhdGVTY2hlZHVsZUNvZGUpDQogICAgICB9DQogICAgfSwNCiAgICBkb1N1Ym1pdChzY2hlZHVsZUNvZGUpIHsNCiAgICAgIFNhdmVTY2hkdWxpbmdUYXNrQnlJZCh7DQogICAgICAgIHNjaGR1bGluZ1BsYW5JZDogc2NoZWR1bGVDb2RlDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAn5LiL6L6+5oiQ5YqfJywNCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5jbG9zZVZpZXcoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkuZmluYWxseShfID0+IHsNCiAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkuY2F0Y2goXyA9PiB7DQogICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRXb3JrU2hvcCh2YWx1ZSkgew0KICAgICAgY29uc29sZS5sb2coJ3ZhbHVlJywgdmFsdWUpDQogICAgICBjb25zdCB7DQogICAgICAgIG9yaWdpbiwNCiAgICAgICAgcm93LA0KICAgICAgICB3b3JrU2hvcDogew0KICAgICAgICAgIElkLA0KICAgICAgICAgIERpc3BsYXlfTmFtZQ0KICAgICAgICB9DQogICAgICB9ID0gdmFsdWUNCiAgICAgIGlmIChvcmlnaW4gPT09IDIpIHsNCiAgICAgICAgaWYgKHZhbHVlLndvcmtTaG9wPy5JZCkgew0KICAgICAgICAgIHJvdy5Xb3Jrc2hvcF9OYW1lID0gRGlzcGxheV9OYW1lDQogICAgICAgICAgcm93LldvcmtzaG9wX0lkID0gSWQNCiAgICAgICAgICB0aGlzLnNldFBhdGgocm93LCBJZCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByb3cuV29ya3Nob3BfTmFtZSA9ICcnDQogICAgICAgICAgcm93LldvcmtzaG9wX0lkID0gJycNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmICh2YWx1ZS53b3JrU2hvcD8uSWQpIHsNCiAgICAgICAgICAgIGl0ZW0uV29ya3Nob3BfTmFtZSA9IERpc3BsYXlfTmFtZQ0KICAgICAgICAgICAgaXRlbS5Xb3Jrc2hvcF9JZCA9IElkDQogICAgICAgICAgICB0aGlzLnNldFBhdGgoaXRlbSwgSWQpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGl0ZW0uV29ya3Nob3BfTmFtZSA9ICcnDQogICAgICAgICAgICBpdGVtLldvcmtzaG9wX0lkID0gJycNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBzZXRQYXRoKHJvdywgSWQpIHsNCiAgICAgIGlmIChyb3c/LlNjaGVkdWxlZF9Xb3Jrc2hvcF9JZCkgew0KICAgICAgICBpZiAocm93LlNjaGVkdWxlZF9Xb3Jrc2hvcF9JZCAhPT0gSWQpIHsNCiAgICAgICAgICByb3cuVGVjaG5vbG9neV9QYXRoID0gJycNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcm93LlRlY2hub2xvZ3lfUGF0aCA9ICcnDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVCYXRjaFdvcmtzaG9wKG9yaWdpbiwgcm93KSB7DQogICAgICB0aGlzLnRpdGxlID0gb3JpZ2luID09PSAxID8gJ+aJuemHj+WIhumFjei9pumXtCcgOiAn5YiG6YWN6L2m6Ze0Jw0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1dvcmtzaG9wJw0KICAgICAgdGhpcy5kV2lkdGggPSAnMzAlJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5mZXRjaERhdGEob3JpZ2luLCByb3cpDQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0UHJvY2Vzc09wdGlvbih3b3Jrc2hvcElkKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gew0KICAgICAgICBHZXRQcm9jZXNzTGlzdEJhc2Uoew0KICAgICAgICAgIHdvcmtzaG9wSWQ6IHdvcmtzaG9wSWQsDQogICAgICAgICAgdHlwZTogMw0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IHByb2Nlc3MgPSByZXMuRGF0YS5tYXAodiA9PiB2LkNvZGUpDQogICAgICAgICAgICByZXNvbHZlKHByb2Nlc3MpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0TGliVHlwZShjb2RlLCB3b3Jrc2hvcElkKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHsNCiAgICAgICAgY29uc3Qgb2JqID0gew0KICAgICAgICAgIENvbXBvbmVudF90eXBlOiBjb2RlLA0KICAgICAgICAgIHR5cGU6IDENCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy53b3Jrc2hvcEVuYWJsZWQpIHsNCiAgICAgICAgICBvYmoud29ya3Nob3BJZCA9IHdvcmtzaG9wSWQNCiAgICAgICAgfQ0KICAgICAgICBHZXRMaWJMaXN0VHlwZShvYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgaWYgKHJlcy5EYXRhLkRhdGEgJiYgcmVzLkRhdGEuRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgY29uc3QgaW5mbyA9IHJlcy5EYXRhLkRhdGFbMF0NCiAgICAgICAgICAgICAgY29uc3Qgd29ya0NvZGUgPSBpbmZvLldvcmtDb2RlICYmIGluZm8uV29ya0NvZGUucmVwbGFjZSgvXFwvZywgJy8nKQ0KICAgICAgICAgICAgICByZXNvbHZlKHdvcmtDb2RlKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgcmVzb2x2ZSh1bmRlZmluZWQpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgaW5wdXRDaGFuZ2Uocm93KSB7DQogICAgICB0aGlzLnNldElucHV0TWF4KHJvdykNCiAgICB9LA0KICAgIHNldElucHV0TWF4KHJvdykgew0KICAgICAgY29uc3QgaW5wdXRWYWx1ZXNLZXlzID0gT2JqZWN0LmtleXMocm93KQ0KICAgICAgICAuZmlsdGVyKHYgPT4gIXYuZW5kc1dpdGgoJ21heCcpICYmIHYuc3RhcnRzV2l0aChyb3cudXVpZCkgJiYgdi5sZW5ndGggPiByb3cudXVpZC5sZW5ndGgpDQogICAgICBpbnB1dFZhbHVlc0tleXMuZm9yRWFjaCgodmFsKSA9PiB7DQogICAgICAgIGNvbnN0IGN1ckNvZGUgPSB2YWwuc3BsaXQoU1BMSVRfU1lNQk9MKVsxXQ0KICAgICAgICBjb25zdCBvdGhlclRvdGFsID0gaW5wdXRWYWx1ZXNLZXlzLmZpbHRlcih4ID0+IHsNCiAgICAgICAgICBjb25zdCBjb2RlID0geC5zcGxpdChTUExJVF9TWU1CT0wpWzFdDQogICAgICAgICAgcmV0dXJuIHggIT09IHZhbCAmJiBjb2RlID09PSBjdXJDb2RlDQogICAgICAgIH0pLnJlZHVjZSgoYWNjLCBpdGVtKSA9PiB7DQogICAgICAgICAgcmV0dXJuIGFjYyArIG51bWVyYWwocm93W2l0ZW1dKS52YWx1ZSgpDQogICAgICAgIH0sIDApDQogICAgICAgIHJvd1t2YWwgKyBTUExJVF9TWU1CT0wgKyAnbWF4J10gPSByb3cuU2NoZHVsZWRfQ291bnQgLSBvdGhlclRvdGFsDQogICAgICB9KQ0KICAgIH0sDQogICAgc2VuZFByb2Nlc3MoeyBhcnIsIHN0ciB9KSB7DQogICAgICBsZXQgaXNTdWNjZXNzID0gdHJ1ZQ0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgaXRlbSA9IGFycltpXQ0KICAgICAgICBpZiAoaXRlbS5vcmlnaW5hbFBhdGggJiYgaXRlbS5vcmlnaW5hbFBhdGggIT09IHN0cikgew0KICAgICAgICAgIGlzU3VjY2VzcyA9IGZhbHNlDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgfQ0KICAgICAgICBpdGVtLlRlY2hub2xvZ3lfUGF0aCA9IHN0cg0KICAgICAgfQ0KICAgICAgaWYgKCFpc1N1Y2Nlc3MpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+ivt+WSjOivpeWMuuWfn+aJueasoeS4i+W3suaOkuS6p+WQjOmDqOS7tuS/neaMgeW3peW6j+S4gOiHtCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICByZXNldFdvcmtUZWFtTWF4KHJvdywgc3RyKSB7DQogICAgICBpZiAoc3RyKSB7DQogICAgICAgIHJvdy5UZWNobm9sb2d5X1BhdGggPSBzdHINCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHN0ciA9IHJvdy5UZWNobm9sb2d5X1BhdGgNCiAgICAgIH0NCiAgICAgIGNvbnN0IGxpc3QgPSBzdHI/LnNwbGl0KCcvJykgfHwgW10NCiAgICAgIHRoaXMud29ya2luZ1RlYW0uZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgIGNvbnN0IGN1ciA9IGxpc3Quc29tZShrID0+IGsgPT09IGVsZW1lbnQuUHJvY2Vzc19Db2RlKQ0KICAgICAgICBjb25zdCBjb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUocm93LnV1aWQsIGVsZW1lbnQuUHJvY2Vzc19Db2RlLCBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgY29uc3QgbWF4ID0gdGhpcy5nZXRSb3dVbmlxdWVNYXgocm93LnV1aWQsIGVsZW1lbnQuUHJvY2Vzc19Db2RlLCBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgaWYgKGN1cikgew0KICAgICAgICAgIGlmICghcm93W2NvZGVdKSB7DQogICAgICAgICAgICB0aGlzLiRzZXQocm93LCBjb2RlLCAwKQ0KICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgbWF4LCByb3cuU2NoZHVsZWRfQ291bnQpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJGRlbGV0ZShyb3csIGNvZGUpDQogICAgICAgICAgdGhpcy4kZGVsZXRlKHJvdywgbWF4KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgY2hlY2tQZXJtaXNzaW9uVGVhbShwcm9jZXNzU3RyLCBwcm9jZXNzQ29kZSkgew0KICAgICAgaWYgKCFwcm9jZXNzU3RyKSByZXR1cm4gZmFsc2UNCiAgICAgIGNvbnN0IGxpc3QgPSBwcm9jZXNzU3RyPy5zcGxpdCgnLycpIHx8IFtdDQogICAgICByZXR1cm4gISFsaXN0LnNvbWUodiA9PiB2ID09PSBwcm9jZXNzQ29kZSkNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0VGFibGVDb25maWcoY29kZSkgew0KICAgICAgYXdhaXQgR2V0R3JpZEJ5Q29kZSh7DQogICAgICAgIGNvZGUNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBjb25zdCB7IElzU3VjY2VlZCwgRGF0YSwgTWVzc2FnZSB9ID0gcmVzDQogICAgICAgIGlmIChJc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnRiQ29uZmlnID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy50YkNvbmZpZywgRGF0YS5HcmlkKQ0KICAgICAgICAgIGNvbnN0IGxpc3QgPSBEYXRhLkNvbHVtbkxpc3QgfHwgW10NCiAgICAgICAgICB0aGlzLm93bmVyQ29sdW1uID0gbGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5Db2RlID09PSAnUGFydF9Vc2VkX1Byb2Nlc3MnKQ0KICAgICAgICAgIHRoaXMub3duZXJDb2x1bW4yID0gbGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5Db2RlID09PSAnSXNfTWFpbl9QYXJ0JykNCiAgICAgICAgICB0aGlzLmNvbHVtbnMgPSB0aGlzLnNldENvbHVtbkRpc3BsYXkobGlzdCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IE1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldENvbHVtbkRpc3BsYXkobGlzdCkgew0KICAgICAgcmV0dXJuIGxpc3QuZmlsdGVyKHYgPT4gdi5Jc19EaXNwbGF5KQ0KICAgICAgLy8gLm1hcChpdGVtID0+IHsNCiAgICAgIC8vICAgaWYgKEZJWF9DT0xVTU4uaW5jbHVkZXMoaXRlbS5Db2RlKSkgew0KICAgICAgLy8gICAgIGl0ZW0uZml4ZWQgPSAnbGVmdCcNCiAgICAgIC8vICAgfQ0KICAgICAgLy8gICByZXR1cm4gaXRlbQ0KICAgICAgLy8gfSkNCiAgICB9LA0KICAgIGFjdGl2ZUNlbGxNZXRob2QoeyByb3csIGNvbHVtbiwgY29sdW1uSW5kZXggfSkgew0KICAgICAgaWYgKHRoaXMuaXNWaWV3KSByZXR1cm4gZmFsc2UNCiAgICAgIGNvbnN0IHByb2Nlc3NDb2RlID0gY29sdW1uLmZpZWxkPy5zcGxpdCgnJF8kJylbMV0NCiAgICAgIHJldHVybiB0aGlzLmNoZWNrUGVybWlzc2lvblRlYW0ocm93LlRlY2hub2xvZ3lfUGF0aCwgcHJvY2Vzc0NvZGUpDQogICAgfSwNCiAgICBvcGVuQlBBRGlhbG9nKHR5cGUsIHJvdykgew0KICAgICAgaWYgKHRoaXMud29ya3Nob3BFbmFibGVkKSB7DQogICAgICAgIGlmICh0eXBlID09PSAxKSB7DQogICAgICAgICAgY29uc3QgSXNVbmlxdWUgPSB0aGlzLmNoZWNrSXNVbmlxdWVXb3Jrc2hvcCgpDQogICAgICAgICAgaWYgKCFJc1VuaXF1ZSkgcmV0dXJuDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMudGl0bGUgPSB0eXBlID09PSAyID8gJ+W3peW6j+iwg+aVtCcgOiAn5om56YeP5bel5bqP6LCD5pW0Jw0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ0JhdGNoUHJvY2Vzc0FkanVzdCcNCiAgICAgIHRoaXMuZFdpZHRoID0gJzUwJScNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uc2V0RGF0YSh0eXBlID09PSAyID8gW3Jvd10gOiB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLCB0eXBlID09PSAyID8gcm93LlRlY2hub2xvZ3lfUGF0aCA6ICcnKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNoZWNrSXNVbmlxdWVXb3Jrc2hvcCgpIHsNCiAgICAgIGxldCBpc1VuaXF1ZSA9IHRydWUNCiAgICAgIGNvbnN0IGZpcnN0ViA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25bMF0uV29ya3Nob3BfTmFtZQ0KICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPCB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uW2ldDQogICAgICAgIGlmIChpdGVtLldvcmtzaG9wX05hbWUgIT09IGZpcnN0Vikgew0KICAgICAgICAgIGlzVW5pcXVlID0gZmFsc2UNCiAgICAgICAgICBicmVhaw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAoIWlzVW5pcXVlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfmibnph4/liIbphY3lt6Xluo/ml7blj6rmnInnm7jlkIzovabpl7TkuIvnmoTmiY3lj6/kuIDotbfmibnph4/liIbphY0nLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGlzVW5pcXVlDQogICAgfSwNCiAgICBjaGVja0hhc1dvcmtTaG9wKHR5cGUsIGFycikgew0KICAgICAgbGV0IGhhc1dvcmtTaG9wID0gdHJ1ZQ0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgaXRlbSA9IGFycltpXQ0KICAgICAgICBpZiAoIWl0ZW0uV29ya3Nob3BfTmFtZSkgew0KICAgICAgICAgIGhhc1dvcmtTaG9wID0gZmFsc2UNCiAgICAgICAgICBicmVhaw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAoIWhhc1dvcmtTaG9wKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfor7flhYjpgInmi6novabpl7TlkI7lho3ov5vooYzlt6Xluo/liIbphY0nLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGhhc1dvcmtTaG9wDQogICAgfSwNCiAgICBoYW5kbGVBZGREaWFsb2codHlwZSA9ICdhZGQnKSB7DQogICAgICB0aGlzLnRpdGxlID0gJ+a3u+WKoOmDqOS7ticNCg0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ0FkZERyYWZ0Jw0KICAgICAgdGhpcy5kV2lkdGggPSAnOTYlJw0KICAgICAgdGhpcy5vcGVuQWRkRHJhZnQgPSB0cnVlDQoNCiAgICAgIHRoaXMuc2V0QWRkVGJLZXkoKQ0KDQogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgdGhpcy4kcmVmc1snZHJhZnQnXS5pbml0RGF0YSgpDQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0QWRkVGJLZXkoKSB7DQogICAgICBjb25zdCBzZWxlY3RLZXlzID0gdGhpcy50YkRhdGEuZmlsdGVyKGN1ciA9PiBjdXIucHV1aWQpLm1hcCh2ID0+IHYucHV1aWQpDQogICAgICB0aGlzLmNoYW5nZUFkZFRiS2V5cyhzZWxlY3RLZXlzKQ0KICAgIH0sDQogICAgZ2V0Um93VW5pcXVlKHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpIHsNCiAgICAgIHJldHVybiBgJHt1dWlkfSR7U1BMSVRfU1lNQk9MfSR7cHJvY2Vzc0NvZGV9JHtTUExJVF9TWU1CT0x9JHt3b3JraW5nSWR9YA0KICAgIH0sDQogICAgZ2V0Um93VW5pcXVlTWF4KHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpIHsNCiAgICAgIHJldHVybiB0aGlzLmdldFJvd1VuaXF1ZSh1dWlkLCBwcm9jZXNzQ29kZSwgd29ya2luZ0lkKSArIGAke1NQTElUX1NZTUJPTH1tYXhgDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3RNZW51KHYpIHsNCiAgICAgIGlmICh2ID09PSAncHJvY2VzcycpIHsNCiAgICAgICAgdGhpcy5vcGVuQlBBRGlhbG9nKDEpDQogICAgICB9IGVsc2UgaWYgKHYgPT09ICdjcmFmdCcpIHsNCiAgICAgICAgdGhpcy5oYW5kbGVTZXRDcmFmdFByb2Nlc3MoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgbWVyZ2VDcmFmdFByb2Nlc3MobGlzdCkgew0KICAgICAgbGV0IGNvZGVzID0gWy4uLm5ldyBTZXQobGlzdC5tYXAodiA9PiB2LlRlY2hub2xvZ3lfQ29kZSkpXQ0KICAgICAgZm9yIChjb25zdCBrZXkgaW4gdGhpcy5jcmFmdENvZGVNYXApIHsNCiAgICAgICAgaWYgKHRoaXMuY3JhZnRDb2RlTWFwLmhhc093blByb3BlcnR5KGtleSkpIHsNCiAgICAgICAgICBjb2RlcyA9IGNvZGVzLmZpbHRlcihjb2RlID0+IGNvZGUgIT09IGtleSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY29uc3QgX2NyYWZ0Q29kZU1hcCA9IGF3YWl0IHRoaXMuZ2V0Q3JhZnRQcm9jZXNzKGNvZGVzKQ0KICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLmNyYWZ0Q29kZU1hcCwgX2NyYWZ0Q29kZU1hcCkNCiAgICB9LA0KICAgIGFzeW5jIGhhbmRsZVNldENyYWZ0UHJvY2VzcygpIHsNCiAgICAgIGNvbnN0IHNob3dTdWNjZXNzID0gKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5bey5YiG6YWN5oiQ5YqfJywNCiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGNvbnN0IHJvd0xpc3QgPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLm1hcCh2ID0+IHYuVGVjaG5vbG9neV9Db2RlKS5maWx0ZXIodiA9PiAhIXYpDQogICAgICBpZiAoIXJvd0xpc3QubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICflt6Xoibrku6PnoIHkuI3lrZjlnKgnLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGF3YWl0IHRoaXMubWVyZ2VDcmFmdFByb2Nlc3ModGhpcy5tdWx0aXBsZVNlbGVjdGlvbikNCiAgICAgIGNvbnN0IHdvcmtzaG9wSWRzID0gQXJyYXkuZnJvbShuZXcgU2V0KHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKHYgPT4gdi5Xb3Jrc2hvcF9JZCkuZmlsdGVyKHYgPT4gISF2KSkpDQogICAgICBjb25zdCB3X3Byb2Nlc3MgPSBbXQ0KICAgICAgaWYgKHdvcmtzaG9wSWRzLmxlbmd0aCkgew0KICAgICAgICB3b3Jrc2hvcElkcy5mb3JFYWNoKHdvcmtzaG9wSWQgPT4gew0KICAgICAgICAgIHdfcHJvY2Vzcy5wdXNoKHRoaXMuZ2V0UHJvY2Vzc09wdGlvbih3b3Jrc2hvcElkKS50aGVuKHJlc3VsdCA9PiAoew0KICAgICAgICAgICAgW3dvcmtzaG9wSWRdOiByZXN1bHQNCiAgICAgICAgICB9KSkpDQogICAgICAgIH0pDQogICAgICAgIGNvbnN0IHdvcmtzaG9wUHJvbWlzZSA9IFByb21pc2UuYWxsKHdfcHJvY2VzcykudGhlbigodmFsdWVzKSA9PiB7DQogICAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oe30sIC4uLnZhbHVlcykNCiAgICAgICAgfSkNCiAgICAgICAgd29ya3Nob3BQcm9taXNlLnRoZW4od29ya3Nob3AgPT4gew0KICAgICAgICAgIGxldCBmbGFnID0gdHJ1ZQ0KICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGg7IGkrKykgew0KICAgICAgICAgICAgY29uc3QgY3VyUm93ID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbltpXQ0KICAgICAgICAgICAgY29uc3Qgd29ya3Nob3BQcm9jZXNzID0gd29ya3Nob3BbY3VyUm93LldvcmtzaG9wX0lkXQ0KICAgICAgICAgICAgY29uc3QgY3JhZnRBcnJheSA9IHRoaXMuY3JhZnRDb2RlTWFwW2N1clJvdy5UZWNobm9sb2d5X0NvZGVdDQogICAgICAgICAgICBpZiAoY3JhZnRBcnJheSkgew0KICAgICAgICAgICAgICBjb25zdCBpc0luY2x1ZGVkID0gY3JhZnRBcnJheS5ldmVyeShwcm9jZXNzID0+IHdvcmtzaG9wUHJvY2Vzcy5pbmNsdWRlcyhwcm9jZXNzKSkNCiAgICAgICAgICAgICAgaWYgKCFpc0luY2x1ZGVkKSB7DQogICAgICAgICAgICAgICAgZmxhZyA9IGZhbHNlDQogICAgICAgICAgICAgICAgY29udGludWUNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBjdXJSb3cuVGVjaG5vbG9neV9QYXRoID0gY3JhZnRBcnJheS5qb2luKCcvJykNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKCFmbGFnKSB7DQogICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kYWxlcnQoJ+aJgOmAiei9pumXtOS4i+ePree7hOWKoOW3peW3peW6j+S4jeWMheWQq+W3peiJuuS7o+eggeW3peW6j+ivt+aJi+WKqOaOkuS6pycsICfmj5DnpLonLCB7DQogICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9LCAyMDApDQogICAgICAgICAgfQ0KDQogICAgICAgICAgZmxhZyAmJiBzaG93U3VjY2VzcygpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmZvckVhY2goKGN1clJvdykgPT4gew0KICAgICAgICAgIGNvbnN0IGNyYWZ0QXJyYXkgPSB0aGlzLmNyYWZ0Q29kZU1hcFtjdXJSb3cuVGVjaG5vbG9neV9Db2RlXQ0KICAgICAgICAgIGlmIChjcmFmdEFycmF5KSB7DQogICAgICAgICAgICBjdXJSb3cuVGVjaG5vbG9neV9QYXRoID0gY3JhZnRBcnJheS5qb2luKCcvJykNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIHNob3dTdWNjZXNzKCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUJhdGNoT3duZXIodHlwZSwgcm93KSB7DQogICAgICB0aGlzLnRpdGxlID0gJ+aJuemHj+WIhumFjemihueUqOW3peW6jycNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdPd25lclByb2Nlc3MnDQogICAgICB0aGlzLmRXaWR0aCA9ICczMCUnDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLnNldE9wdGlvbih0eXBlID09PSAyLCB0eXBlID09PSAyID8gW3Jvd10gOiB0aGlzLm11bHRpcGxlU2VsZWN0aW9uKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldENyYWZ0UHJvY2VzcyhneUdyb3VwID0gW10pIHsNCiAgICAgIGd5R3JvdXAgPSBneUdyb3VwLmZpbHRlcih2ID0+ICEhdikNCiAgICAgIGlmICghZ3lHcm91cC5sZW5ndGgpIHJldHVybiBQcm9taXNlLnJlc29sdmUoe30pDQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gew0KICAgICAgICBHZXRQcm9jZXNzRmxvd0xpc3RXaXRoVGVjaG5vbG9neSh7DQogICAgICAgICAgVGVjaG5vbG9neUNvZGVzOiBneUdyb3VwLA0KICAgICAgICAgIFR5cGU6IDMNCiAgICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICBjb25zdCBneUxpc3QgPSByZXMuRGF0YSB8fCBbXQ0KICAgICAgICAgICAgY29uc3QgZ3lNYXAgPSBneUxpc3QucmVkdWNlKChhY2MsIGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgYWNjW2l0ZW0uQ29kZV0gPSBpdGVtLlRlY2hub2xvZ3lfUGF0aA0KICAgICAgICAgICAgICByZXR1cm4gYWNjDQogICAgICAgICAgICB9LCB7fSkNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdneU1hcCcsIGd5TWFwKQ0KICAgICAgICAgICAgcmVzb2x2ZShneU1hcCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmVqZWN0KCkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlUmV2ZXJzZSgpIHsNCiAgICAgIGNvbnN0IGN1ciA9IFtdDQogICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgICAgZWxlbWVudC5jaGVja2VkID0gIWVsZW1lbnQuY2hlY2tlZA0KICAgICAgICBpZiAoZWxlbWVudC5jaGVja2VkKSB7DQogICAgICAgICAgY3VyLnB1c2goZWxlbWVudCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBjdXINCiAgICAgIGlmICh0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aCA9PT0gdGhpcy50YkRhdGEubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuJHJlZnNbJ3hUYWJsZSddLnNldEFsbENoZWNrYm94Um93KHRydWUpDQogICAgICB9DQogICAgICBpZiAodGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kcmVmc1sneFRhYmxlJ10uc2V0QWxsQ2hlY2tib3hSb3coZmFsc2UpDQogICAgICB9DQogICAgfSwNCiAgICAvLyB0YkZpbHRlckNoYW5nZSgpIHsNCiAgICAvLyAgIGNvbnN0IHhUYWJsZSA9IHRoaXMuJHJlZnMueFRhYmxlDQogICAgLy8gICBjb25zdCBjb2x1bW4gPSB4VGFibGUuZ2V0Q29sdW1uQnlGaWVsZCgnVHlwZV9OYW1lJykNCiAgICAvLyAgIGlmICghY29sdW1uPy5maWx0ZXJzPy5sZW5ndGgpIHJldHVybg0KICAgIC8vICAgY29sdW1uLmZpbHRlcnMuZm9yRWFjaChkID0+IHsNCiAgICAvLyAgICAgZC5jaGVja2VkID0gZC52YWx1ZSA9PT0gdGhpcy5zZWFyY2hUeXBlDQogICAgLy8gICB9KQ0KICAgIC8vICAgeFRhYmxlLnVwZGF0ZURhdGEoKQ0KICAgIC8vIH0sDQogICAgZ2V0VHlwZSgpIHsNCiAgICAgIGNvbnN0IGdldENvbXBUcmVlID0gKCkgPT4gew0KICAgICAgICBjb25zdCBmdW4gPSB0aGlzLmlzQ29tID8gR2V0Q29tcFR5cGVUcmVlIDogR2V0UGFydFR5cGVMaXN0DQogICAgICAgIGZ1bih7fSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICBsZXQgcmVzdWx0ID0gcmVzLkRhdGENCiAgICAgICAgICAgIGlmICghdGhpcy5pc0NvbSkgew0KICAgICAgICAgICAgICByZXN1bHQgPSByZXN1bHQNCiAgICAgICAgICAgICAgICAubWFwKCh2LCBpZHgpID0+IHsNCiAgICAgICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgICAgIERhdGE6IHYuTmFtZSwNCiAgICAgICAgICAgICAgICAgICAgTGFiZWw6IHYuTmFtZQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLnRyZWVQYXJhbXNDb21wb25lbnRUeXBlLmRhdGEgPSByZXN1bHQNCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVNlbGVjdENvbXBvbmVudFR5cGU/LnRyZWVEYXRhVXBkYXRlRnVuKHJlc3VsdCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQoNCiAgICAgIGdldENvbXBUcmVlKCkNCiAgICB9LA0KICAgIC8vIOafpeeci+Wbvue6uA0KICAgIGhhbmRsZUR3Zyhyb3cpIHsNCiAgICAgIGNvbnNvbGUubG9nKCdyb3cnLCByb3cpDQogICAgICBjb25zdCBvYmogPSB7fQ0KICAgICAgaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgICAgb2JqLkNvbXBfSWQgPSByb3cuQ29tcF9JbXBvcnRfRGV0YWlsX0lkDQogICAgICB9IGVsc2Ugew0KICAgICAgICBvYmouUGFydF9JZCA9IHJvdy5QYXJ0X0FnZ3JlZ2F0ZV9JZA0KICAgICAgfQ0KICAgICAgR2V0U3RlZWxDYWRBbmRCaW1JZCh7IGltcG9ydERldGFpbElkOiBvYmouUGFydF9JZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLmV4dGVuc2lvbk5hbWUgPSByZXMuRGF0YVswXS5FeHRlbnNpb25OYW1lDQogICAgICAgICAgdGhpcy5maWxlQmltID0gcmVzLkRhdGFbMF0uZmlsZUJpbQ0KICAgICAgICAgIHRoaXMuSXNVcGxvYWRDYWQgPSByZXMuRGF0YVswXS5Jc1VwbG9hZA0KICAgICAgICAgIHRoaXMuY2FkUm93Q29kZSA9IHJvdy5QYXJ0X0NvZGUNCiAgICAgICAgICB0aGlzLmNhZFJvd1Byb2plY3RJZCA9IHJvdy5TeXNfUHJvamVjdF9JZA0KICAgICAgICAgIHRoaXMuZmlsZVZpZXcoKQ0KICAgICAgICB9DQogICAgICB9KQ0KDQogICAgICAvLyBHZXREd2cob2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAvLyAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAvLyAgICAgY29uc3QgZmlsZXVybCA9IHJlcz8uRGF0YT8ubGVuZ3RoICYmIHJlcy5EYXRhWzBdLkZpbGVfVXJsDQogICAgICAvLyAgICAgd2luZG93Lm9wZW4oJ2h0dHA6Ly9kd2d2MS5iaW10ay5jb206NTQzMi8/Q2FkVXJsPScgKyBwYXJzZU9zc1VybChmaWxldXJsKSwgJ19ibGFuaycpDQogICAgICAvLyAgIH0gZWxzZSB7DQogICAgICAvLyAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAvLyAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgIC8vICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgIC8vICAgICB9KQ0KICAgICAgLy8gICB9DQogICAgICAvLyB9KQ0KICAgIH0sDQogICAgc2V0UHJvY2Vzc0xpc3QoaW5mbykgew0KICAgICAgdGhpcy5jaGFuZ2VQcm9jZXNzTGlzdChpbmZvKQ0KICAgIH0sDQogICAgcmVzZXRJbm5lckZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWydzZWFyY2hGb3JtJ10ucmVzZXRGaWVsZHMoKQ0KICAgICAgdGhpcy4kcmVmcy54VGFibGUuY2xlYXJGaWx0ZXIoKQ0KICAgIH0sDQogICAgaW5uZXJGaWx0ZXIoKSB7DQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW10NCiAgICAgIGNvbnN0IGFyciA9IFtdDQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBhcnIucHVzaCgnVHlwZScsICdDb21wX0NvZGUnLCAnU3BlYycsICdJc19Db21wb25lbnQnKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgYXJyLnB1c2goJ1BhcnRfQ29kZScsICdTcGVjJywgJ1R5cGVfTmFtZScsICdQcm9qZWN0X05hbWUnLCAnQXJlYV9OYW1lJywgJ0luc3RhbGxVbml0X05hbWUnKQ0KICAgICAgfQ0KDQogICAgICBjb25zdCB4VGFibGUgPSB0aGlzLiRyZWZzLnhUYWJsZQ0KICAgICAgeFRhYmxlLmNsZWFyQ2hlY2tib3hSb3coKQ0KICAgICAgYXJyLmZvckVhY2goKGVsZW1lbnQsIGlkeCkgPT4gew0KICAgICAgICBjb25zdCBjb2x1bW4gPSB4VGFibGUuZ2V0Q29sdW1uQnlGaWVsZChlbGVtZW50KQ0KICAgICAgICBpZiAoZWxlbWVudCA9PT0gJ0lzX0NvbXBvbmVudCcpIHsNCiAgICAgICAgICBjb2x1bW4uZmlsdGVycy5mb3JFYWNoKChvcHRpb24sIGlkeCkgPT4gew0KICAgICAgICAgICAgb3B0aW9uLmNoZWNrZWQgPSBpZHggPT09ICh0aGlzLmlubmVyRm9ybS5zZWFyY2hEaXJlY3QgPyAwIDogMSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIGlmIChlbGVtZW50ID09PSAnU3BlYycpIHsNCiAgICAgICAgICBjb25zdCBvcHRpb24gPSBjb2x1bW4uZmlsdGVyc1swXQ0KICAgICAgICAgIG9wdGlvbi5kYXRhID0gdGhpcy5pbm5lckZvcm0uc2VhcmNoU3BlY1NlYXJjaA0KICAgICAgICAgIG9wdGlvbi5jaGVja2VkID0gdHJ1ZQ0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKGVsZW1lbnQgPT09ICdDb21wX0NvZGUnIHx8IGVsZW1lbnQgPT09ICdQYXJ0X0NvZGUnKSB7DQogICAgICAgICAgY29uc3Qgb3B0aW9uID0gY29sdW1uLmZpbHRlcnNbMF0NCiAgICAgICAgICBvcHRpb24uZGF0YSA9IHRoaXMuaW5uZXJGb3JtLnNlYXJjaENvbnRlbnQNCiAgICAgICAgICBvcHRpb24uY2hlY2tlZCA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZWxlbWVudCA9PT0gJ1Byb2plY3RfTmFtZScpIHsNCiAgICAgICAgICBjb25zdCBvcHRpb24gPSBjb2x1bW4uZmlsdGVyc1swXQ0KICAgICAgICAgIG9wdGlvbi5kYXRhID0gdGhpcy5pbm5lckZvcm0ucHJvamVjdE5hbWUNCiAgICAgICAgICBvcHRpb24uY2hlY2tlZCA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZWxlbWVudCA9PT0gJ0FyZWFfTmFtZScpIHsNCiAgICAgICAgICBjb25zdCBvcHRpb24gPSBjb2x1bW4uZmlsdGVyc1swXQ0KICAgICAgICAgIG9wdGlvbi5kYXRhID0gdGhpcy5pbm5lckZvcm0uYXJlYU5hbWUNCiAgICAgICAgICBvcHRpb24uY2hlY2tlZCA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZWxlbWVudCA9PT0gJ0luc3RhbGxVbml0X05hbWUnKSB7DQogICAgICAgICAgY29uc3Qgb3B0aW9uID0gY29sdW1uLmZpbHRlcnNbMF0NCiAgICAgICAgICBvcHRpb24uZGF0YSA9IHRoaXMuaW5uZXJGb3JtLmluc3RhbGxOYW1lDQogICAgICAgICAgb3B0aW9uLmNoZWNrZWQgPSB0cnVlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICB4VGFibGUudXBkYXRlRGF0YSgpDQogICAgfSwNCiAgICBmaWx0ZXJDb21wb25lbnRNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAodGhpcy5pbm5lckZvcm0uc2VhcmNoRGlyZWN0ID09PSAnJykgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHJvdy5Jc19Db21wb25lbnQgPT09ICF0aGlzLmlubmVyRm9ybS5zZWFyY2hEaXJlY3QNCiAgICB9LA0KICAgIGZpbHRlclNwZWNNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAodGhpcy5pbm5lckZvcm0uc2VhcmNoU3BlY1NlYXJjaC50cmltKCkgPT09ICcnKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQogICAgICBjb25zdCBzcGxpdEFuZENsZWFuID0gKGlucHV0KSA9PiBpbnB1dC50cmltKCkucmVwbGFjZSgvXHMrL2csICcgJykuc3BsaXQoJyAnKQ0KICAgICAgY29uc3Qgc3BlY0FycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmlubmVyRm9ybS5zZWFyY2hTcGVjU2VhcmNoKQ0KICAgICAgcmV0dXJuIHNwZWNBcnJheS5zb21lKGNvZGUgPT4gKHJvdy5TcGVjIHx8ICcnKS5pbmNsdWRlcyhjb2RlKSkNCiAgICB9LA0KDQogICAgZmlsdGVyQ29kZU1ldGhvZCh7IG9wdGlvbiwgcm93IH0pIHsNCiAgICAgIGlmICh0aGlzLmlubmVyRm9ybS5zZWFyY2hDb250ZW50LnRyaW0oKSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0NCg0KICAgICAgY29uc3Qgc3BsaXRBbmRDbGVhbiA9IChpbnB1dCkgPT4gaW5wdXQudHJpbSgpLnJlcGxhY2UoL1xzKy9nLCAnICcpLnNwbGl0KCcgJykNCg0KICAgICAgY29uc3QgY3VyID0gdGhpcy5pc0NvbSA/ICdDb21wX0NvZGUnIDogJ1BhcnRfQ29kZScNCg0KICAgICAgY29uc3QgYXJyID0gc3BsaXRBbmRDbGVhbih0aGlzLmlubmVyRm9ybS5zZWFyY2hDb250ZW50KQ0KDQogICAgICBpZiAodGhpcy5jdXJTZWFyY2ggPT09IDEpIHsNCiAgICAgICAgcmV0dXJuIGFyci5zb21lKGNvZGUgPT4gcm93W2N1cl0gPT09IGNvZGUpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGFyci5sZW5ndGg7IGkrKykgew0KICAgICAgICAgIGNvbnN0IGl0ZW0gPSBhcnJbaV0NCiAgICAgICAgICBpZiAocm93W2N1cl0uaW5jbHVkZXMoaXRlbSkpIHsNCiAgICAgICAgICAgIHJldHVybiB0cnVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQogICAgZmlsdGVyUHJvamVjdE1ldGhvZCh7IG9wdGlvbiwgcm93IH0pIHsNCiAgICAgIGlmIChvcHRpb24uZGF0YSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0NCiAgICAgIHJldHVybiByb3cuUHJvamVjdF9OYW1lID09PSBvcHRpb24uZGF0YQ0KICAgIH0sDQogICAgZmlsdGVyQXJlYU1ldGhvZCh7IG9wdGlvbiwgcm93IH0pIHsNCiAgICAgIGlmIChvcHRpb24uZGF0YSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0NCiAgICAgIHJldHVybiByb3cuQXJlYV9OYW1lID09PSBvcHRpb24uZGF0YQ0KICAgIH0sDQogICAgZmlsdGVySW5zdGFsbE1ldGhvZCh7IG9wdGlvbiwgcm93IH0pIHsNCiAgICAgIGlmIChvcHRpb24uZGF0YSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0NCiAgICAgIHJldHVybiByb3cuSW5zdGFsbFVuaXRfTmFtZSA9PT0gb3B0aW9uLmRhdGENCiAgICB9LA0KICAgIGNvbXBvbmVudFR5cGVGaWx0ZXIoZSkgew0KICAgICAgdGhpcy4kcmVmcz8udHJlZVNlbGVjdENvbXBvbmVudFR5cGUuZmlsdGVyRnVuKGUpDQogICAgfSwNCiAgICBnZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoaWQpIHsNCiAgICAgIGlmICghdGhpcy5hcmVhSWQpIHsNCiAgICAgICAgdGhpcy5pbnN0YWxsVW5pdElkTGlzdCA9IFtdDQogICAgICAgIHRoaXMuZGlzYWJsZWRBZGQgPSBmYWxzZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5kaXNhYmxlZEFkZCA9IHRydWUNCiAgICAgICAgR2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0KHsgQXJlYV9JZDogdGhpcy5hcmVhSWQgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIHRoaXMuaW5zdGFsbFVuaXRJZExpc3QgPSByZXMuRGF0YQ0KICAgICAgICAgIGlmICh0aGlzLmluc3RhbGxVbml0SWRMaXN0Lmxlbmd0aCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtSW5saW5lLkluc3RhbGxVbml0X0lkID0gdGhpcy5pbnN0YWxsVW5pdElkTGlzdFswXS5JZA0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmRpc2FibGVkQWRkID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGluc3RhbGxDaGFuZ2UoKSB7DQogICAgICBpZiAoIXRoaXMudGJEYXRhLmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRyZWZzWydzZWFyY2hGb3JtJ10ucmVzZXRGaWVsZHMoKQ0KICAgICAgICB0aGlzLiRyZWZzLnhUYWJsZS5jbGVhckZpbHRlcigpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgn5YiH5o2i5Yy65Z+f5Y+z5L6n5pWw5o2u5riF56m6LCDmmK/lkKbnoa7orqQ/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy50YkRhdGEgPSBbXQ0KICAgICAgICB0aGlzLnJlc2V0SW5uZXJGb3JtKCkNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtognDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgc2hvd1BhcnRVc2VkUHJvY2Vzcyhyb3cpIHsNCiAgICAgIGlmICh0aGlzLmlzTmVzdCkgew0KICAgICAgICByZXR1cm4gISFyb3cuQ29tcF9JbXBvcnRfRGV0YWlsX0lkDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gIXRoaXMuaXNWaWV3ICYmIHJvdy5UeXBlICE9PSAnRGlyZWN0Jw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgaWYgKCF0aGlzLnRiRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+aaguaXoOaVsOaNricsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy4kcmVmcy54VGFibGUuZXhwb3J0RGF0YSh7DQogICAgICAgIGZpbGVuYW1lOiBg6YOo5Lu25o6S5LqnLSR7dGhpcy5mb3JtSW5saW5lLlNjaGR1bGluZ19Db2RlfSjpg6jku7YpYCwNCiAgICAgICAgdHlwZTogJ3hsc3gnLA0KICAgICAgICBkYXRhOiB0aGlzLnRiRGF0YQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlRHJhd2VyKCkgew0KICAgICAgdGhpcy5kcmF3ZXIgPSBmYWxzZQ0KICAgIH0sDQogICAgZmlsZVZpZXcoKSB7DQogICAgICB0aGlzLmlmcmFtZUtleSA9IHV1aWR2NCgpDQogICAgICB0aGlzLmlmcmFtZVVybCA9IGAkew0KICAgICAgICB0aGlzLmJhc2VDYWRVcmwNCiAgICAgIH0/cm91dGVyPTEmaWZyYW1lSWQ9MTEmYmFzZVVybD0ke2Jhc2VVcmwoKX0mdG9rZW49JHtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgNCiAgICAgICAgJ1Rva2VuJw0KICAgICAgKX0mYXV0aF9pZD0ke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJyl9YA0KICAgICAgdGhpcy5kcmF3ZXIgPSB0cnVlDQogICAgfSwNCiAgICByZW5kZXJJZnJhbWUoKSB7DQogICAgICBjb25zdCBFeHRlbnNpb25OYW1lID0gdGhpcy5leHRlbnNpb25OYW1lDQogICAgICBjb25zdCBmaWxlQmltID0gdGhpcy5maWxlQmltDQogICAgICB0aGlzLmlmcmFtZVVybCA9IGAkew0KICAgICAgICB0aGlzLmJhc2VDYWRVcmwNCiAgICAgIH0/cm91dGVyPTEmaWZyYW1lSWQ9MTEmYmFzZVVybD0ke2Jhc2VVcmwoKX0mdG9rZW49JHtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgNCiAgICAgICAgJ1Rva2VuJw0KICAgICAgKX0mYXV0aF9pZD0ke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJyl9YA0KICAgICAgdGhpcy5mdWxsc2NyZWVuaWQgPSBFeHRlbnNpb25OYW1lDQogICAgICB0aGlzLmZ1bGxiaW1pZCA9IGZpbGVCaW0NCiAgICB9LA0KICAgIGZ1bGxzY3JlZW4odikgew0KICAgICAgdGhpcy50ZW1wbGF0ZVVybCA9IGAkew0KICAgICAgICB0aGlzLmJhc2VDYWRVcmwNCiAgICAgIH0/cm91dGVyPTEmaWZyYW1lSWQ9MTMmYmFzZVVybD0ke2Jhc2VVcmwoKX0mdG9rZW49JHtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgNCiAgICAgICAgJ1Rva2VuJw0KICAgICAgKX0mYXV0aF9pZD0ke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJyl9YA0KICAgICAgdGhpcy5kcmF3ZXJzdWxsID0gdHJ1ZQ0KICAgIH0sDQogICAgZnJhbWVMaXN0ZW5lcih7IGRhdGEgfSkgew0KICAgICAgaWYgKGRhdGEudHlwZSA9PT0gJ2xvYWRlZCcpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ2RhdGEnLCBkYXRhKQ0KICAgICAgICBjb25zb2xlLmVycm9yKA0KICAgICAgICAgICdkYXRhLmRhdGEuaWZyYW1lSWQnLA0KICAgICAgICAgIGRhdGEuZGF0YS5pZnJhbWVJZCwNCiAgICAgICAgICB0eXBlb2YgZGF0YS5kYXRhLmlmcmFtZUlkDQogICAgICAgICkNCiAgICAgICAgaWYgKGRhdGEuZGF0YS5pZnJhbWVJZCA9PT0gJzExJykgew0KICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdmcmFtZScpLmNvbnRlbnRXaW5kb3cucG9zdE1lc3NhZ2UoDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIHR5cGU6ICdyb3V0ZXInLA0KICAgICAgICAgICAgICBwYXRoOiAnL21vZGVsQ2FkJywNCiAgICAgICAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICAgICAgICAvLyBiYXNlVXJsOiBiYXNlVXJsKCksDQogICAgICAgICAgICAgICAgY2FkSWQ6IHRoaXMuZmlsZUJpbSwNCiAgICAgICAgICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuY2FkUm93UHJvamVjdElkLA0KICAgICAgICAgICAgICAgIHN0ZWVsTmFtZTogdGhpcy5jYWRSb3dDb2RlLA0KICAgICAgICAgICAgICAgIHNob3dDYWQ6IHRoaXMuSXNVcGxvYWRDYWQsDQogICAgICAgICAgICAgICAgaXNTdWJBc3NlbWJseTogdHJ1ZSwNCiAgICAgICAgICAgICAgICBpc1BhcnQ6IHRydWUNCiAgICAgICAgICAgICAgICAvLyBjYWRJZDogdGhpcy5maWxlQmltDQogICAgICAgICAgICAgICAgLy8gdG9rZW46IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdUb2tlbicpLA0KICAgICAgICAgICAgICAgIC8vIGF1dGhfaWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJykNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICcqJw0KICAgICAgICAgICkNCiAgICAgICAgfSBlbHNlIGlmIChkYXRhLmRhdGEuaWZyYW1lSWQgPT09ICcxMycpIHsNCiAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnZnVsbEZyYW1lJykuY29udGVudFdpbmRvdy5wb3N0TWVzc2FnZSgNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgdHlwZTogJ3JvdXRlcicsDQogICAgICAgICAgICAgIHBhdGg6ICcvbW9kZWxDYWQnLA0KICAgICAgICAgICAgICBxdWVyeTogew0KICAgICAgICAgICAgICAgIC8vIGJhc2VVcmw6IGJhc2VVcmwoKSwNCiAgICAgICAgICAgICAgICBjYWRJZDogdGhpcy5maWxlQmltLA0KICAgICAgICAgICAgICAgIHByb2plY3RJZDogdGhpcy5jYWRSb3dQcm9qZWN0SWQsDQogICAgICAgICAgICAgICAgc3RlZWxOYW1lOiB0aGlzLmNhZFJvd0NvZGUsDQogICAgICAgICAgICAgICAgc2hvd0NhZDogdGhpcy5Jc1VwbG9hZENhZCwNCiAgICAgICAgICAgICAgICBpc1N1YkFzc2VtYmx5OiB0cnVlLA0KICAgICAgICAgICAgICAgIGlzUGFydDogdHJ1ZQ0KICAgICAgICAgICAgICAgIC8vIHRva2VuOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnVG9rZW4nKSwNCiAgICAgICAgICAgICAgICAvLyBhdXRoX2lkOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnTGFzdF9Xb3JraW5nX09iamVjdF9JZCcpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAnKicNCiAgICAgICAgICApDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["draft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2oBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "draft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-unit-part", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 flex-row\">\r\n    <div class=\"cs-right\">\r\n      <el-card v-loading=\"pgLoading\" class=\"box-card h100\" element-loading-text=\"正在处理...\">\r\n        <h4 class=\"topTitle\"><span />基本信息</h4>\r\n        <el-form\r\n          ref=\"formInline\"\r\n          :inline=\"true\"\r\n          :model=\"formInline\"\r\n          class=\"demo-form-inline\"\r\n        >\r\n          <el-form-item v-if=\"!isAdd&&!isNest\" label=\"排产单号\" prop=\"Schduling_Code\">\r\n            <span v-if=\"isView\">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>\r\n            <el-input v-else v-model=\"formInline.Schduling_Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"计划员\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ formInline.Create_UserName }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Create_UserName\"\r\n              disabled\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"要求完成时间\"\r\n            prop=\"Finish_Date\"\r\n            :rules=\"{ required: true, message: '请选择', trigger: 'change' }\"\r\n          >\r\n            <span v-if=\"isView\">{{ formInline.Finish_Date | timeFormat }}</span>\r\n            <el-date-picker\r\n              v-else\r\n              v-model=\"formInline.Finish_Date\"\r\n              :picker-options=\"pickerOptions\"\r\n              :disabled=\"isView\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"date\"\r\n              placeholder=\"选择日期\"\r\n            />\r\n          </el-form-item>\r\n          <!--          <el-form-item v-if=\"!isNest\" label=\"批次\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ installName }}</span>\r\n            <el-select\r\n              v-else\r\n              v-model=\"formInline.InstallUnit_Id\"\r\n              :disabled=\"!isAdd\"\r\n              filterable\r\n              placeholder=\"请选择\"\r\n              @change=\"installChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in installUnitIdList\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>-->\r\n          <el-form-item label=\"备注\" prop=\"Remark\">\r\n            <span v-if=\"isView\">{{ formInline.Remark }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Remark\"\r\n              :disabled=\"isView\"\r\n              style=\"width: 320px\"\r\n              placeholder=\"请输入\"\r\n            />\r\n          </el-form-item>\r\n\r\n        </el-form>\r\n        <el-divider class=\"elDivder\" />\r\n        <div v-if=\"!isView\">\r\n          <div ref=\"searchDom\" class=\"search-container\">\r\n\r\n            <el-form ref=\"searchForm\" :model=\"innerForm\">\r\n              <el-row>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"项目名称\" prop=\"projectName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.projectName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in projectList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"区域\" prop=\"areaName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.areaName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in areaList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"批次\" prop=\"installName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.installName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in installList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"90px\" prop=\"searchContent\" :label=\"`${levelName}名称`\">\r\n                    <el-input\r\n                      v-model=\"innerForm.searchContent\"\r\n                      clearable\r\n                      class=\"input-with-select w100\"\r\n                      placeholder=\"请输入内容\"\r\n                      size=\"small\"\r\n                    >\r\n                      <el-select\r\n                        slot=\"prepend\"\r\n                        v-model=\"curSearch\"\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100px\"\r\n                      >\r\n                        <el-option label=\"精准查询\" :value=\"1\" />\r\n                        <el-option label=\"模糊查询\" :value=\"0\" />\r\n                      </el-select>\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"规格\" prop=\"searchSpecSearch\">\r\n                    <el-input v-model=\"innerForm.searchSpecSearch\" class=\"w100\" placeholder=\"请输入\" clearable=\"\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item v-if=\"isCom\" label-width=\"80px\" label=\"是否直发件\" prop=\"searchDirect\">\r\n                    <el-select v-model=\"innerForm.searchDirect\" class=\"w100\" placeholder=\"请选择\" clearable>\r\n                      <el-option label=\"是\" :value=\"true\" />\r\n                      <el-option label=\"否\" :value=\"false\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"20px\">\r\n                    <el-button type=\"primary\" @click=\"innerFilter\">搜索</el-button>\r\n                    <el-button @click=\"resetInnerForm\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        <vxe-toolbar\r\n          ref=\"xToolbar1\"\r\n        >\r\n          <template #buttons>\r\n            <div v-if=\"!isView\" class=\"btn-x\">\r\n              <el-button v-if=\"!isNest\" type=\"primary\" @click=\"handleAddDialog()\">添加</el-button>\r\n\r\n              <el-button\r\n                v-if=\"workshopEnabled\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchWorkshop(1)\"\r\n              >分配车间\r\n              </el-button>\r\n\r\n              <el-dropdown style=\"margin:0 10px\" @command=\"handleSelectMenu\">\r\n                <el-button :disabled=\"!multipleSelection.length\" type=\"primary\" plain>\r\n                  分配工序<i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    command=\"process\"\r\n                  >批量分配工序\r\n                  </el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    command=\"craft\"\r\n                  >工艺代码分配\r\n                  </el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <el-button\r\n                v-if=\"!isCom && !isOwnerNull\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchOwner(1)\"\r\n              >批量分配领用工序\r\n              </el-button>\r\n              <el-button\r\n                plain\r\n                :disabled=\"!tbData.length\"\r\n                :loading=\"false\"\r\n                @click=\"handleReverse\"\r\n              >反选\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                plain\r\n                :loading=\"deleteLoading\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleDelete\"\r\n              >删除\r\n              </el-button>\r\n            </div>\r\n            <div v-else>\r\n              <el-button style=\"margin-bottom: 8px;\" :disabled=\"!tbData.length\" @click=\"handleExport\">导出</el-button>\r\n            </div>\r\n          </template>\r\n          <template #tools>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </template>\r\n        </vxe-toolbar>\r\n        <div class=\"tb-x\">\r\n          <!--          activeMethod: activeCellMethod,-->\r\n          <vxe-table\r\n            ref=\"xTable\"\r\n            :key=\"tbKey\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :checkbox-config=\"{checkField: 'checked'}\"\r\n            class=\"cs-vxe-table\"\r\n            :row-config=\"{isCurrent: true, isHover: true}\"\r\n            align=\"left\"\r\n            height=\"100%\"\r\n            :filter-config=\"{showIcon:false}\"\r\n            show-overflow\r\n            :loading=\"tbLoading\"\r\n            stripe\r\n            :scroll-y=\"{enabled: true, gt: 20}\"\r\n            size=\"medium\"\r\n            :edit-config=\"{\r\n              trigger: 'click',\r\n              mode: 'cell',\r\n              showIcon: !isView,\r\n\r\n            }\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"isComponentOptions\"\r\n                :filter-method=\"filterComponentMethod\"\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag\r\n                    :type=\"row.Is_Component ? 'danger' : 'success'\"\r\n                  >{{ row.Is_Component ? '否' : '是' }}\r\n                  </el-tag>\r\n                </template>\r\n              </vxe-column>\r\n\r\n              <vxe-column\r\n                v-else-if=\"['Comp_Code','Part_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :filter-method=\"filterCodeMethod\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"filterCodeOption\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin: 8px;\" type=\"danger\">变</el-tag>\r\n                  <el-link v-if=\"row.DwgCount>0\" type=\"primary\" @click.stop=\"handleDwg(row)\"> {{ row[item.Code] | displayValue }}\r\n                  </el-link>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Spec'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"specOptions\"\r\n                :filter-method=\"filterSpecMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Spec | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Project_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"projectOptions\"\r\n                :filter-method=\"filterProjectMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in projectList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Project_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Area_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"areaOptions\"\r\n                :filter-method=\"filterAreaMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in areaList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Area_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'InstallUnit_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"installOptions\"\r\n                :filter-method=\"filterInstallMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in installList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.InstallUnit_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Weight'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  {{ ((row.Schduled_Count * row.Weight).toFixed(2) / 1 ) | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Technology_Path'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n                :show-overflow=\"false\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Technology_Path\" placement=\"top\">\r\n                        <span>{{ row.Technology_Path | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"openBPADialog(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Part_Used_Process'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                :show-overflow=\"false\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Part_Used_Process\" placement=\"top\">\r\n                        <span>{{ row.Part_Used_Process | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"showPartUsedProcess(row)\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchOwner(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Workshop_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :show-overflow=\"false\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Workshop_Name\" placement=\"top\">\r\n                        <span>{{ row.Workshop_Name | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchWorkshop(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Count'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :edit-render=\"{enabled:!isView}\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #edit=\"{ row }\">\r\n                  <vxe-input\r\n                    v-model.number=\"row.Schduled_Count\"\r\n                    type=\"integer\"\r\n                    min=\"0\"\r\n                    :max=\"row.chooseCount\"\r\n                  />\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Schduled_Count | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Id\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n\r\n          </vxe-table>\r\n        </div>\r\n        <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n        <footer v-if=\"!isView\">\r\n          <div class=\"data-info\">\r\n            <el-tag\r\n              size=\"medium\"\r\n              class=\"info-x\"\r\n            >已选 {{ multipleSelection.length }} 条数据\r\n            </el-tag>\r\n            <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{\r\n              tipLabel\r\n            }}\r\n            </el-tag>\r\n          </div>\r\n          <div>\r\n            <el-button v-if=\"workshopEnabled&&!isNest\" type=\"primary\" @click=\"saveWorkShop\">保存车间分配</el-button>\r\n            <el-button\r\n              v-if=\"!isNest\"\r\n              type=\"primary\"\r\n              :loading=\"saveLoading\"\r\n              @click=\"saveDraft(false)\"\r\n            >保存草稿\r\n            </el-button>\r\n            <el-button :disabled=\"deleteLoading || isStopFlag\" @click=\"handleSubmit\">下发任务</el-button>\r\n          </div>\r\n        </footer>\r\n      </el-card>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :is-nest=\"isNest\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :process-list=\"processList\"\r\n        :page-type=\"pageType\"\r\n        :part-type-option=\"typeOption\"\r\n        @close=\"handleClose\"\r\n        @sendProcess=\"sendProcess\"\r\n        @workShop=\"getWorkShop\"\r\n        @refresh=\"fetchData\"\r\n        @setProcessList=\"setProcessList\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      :key=\"addDraftKey\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"openAddDraft\"\r\n      :width=\"dWidth\"\r\n      top=\"7vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <add-draft\r\n        v-if=\"openAddDraft\"\r\n        ref=\"draft\"\r\n        :level-name=\"levelName\"\r\n        :current-ids=\"currentIds\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :install-id=\"formInline.InstallUnit_Id\"\r\n        :schedule-id=\"scheduleId\"\r\n        :show-dialog=\"openAddDraft\"\r\n        :page-type=\"pageType\"\r\n        @addToTbList=\"addToTbList\"\r\n        @sendSelectList=\"mergeSelectList\"\r\n        @setAddTbKey=\"setAddTbKey\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-drawer\r\n      :visible.sync=\"drawer\"\r\n      direction=\"btt\"\r\n      size=\"60%\"\r\n      destroy-on-close\r\n      :before-close=\"handleCloseDrawer\"\r\n      @opened=\"renderIframe\"\r\n    >\r\n      <div style=\"width: 100%; display: flex\">\r\n        <div style=\"margin-left: 20px\">\r\n          <span style=\"display: inline-block; width: 100px\">部件图纸</span>\r\n        </div>\r\n        <el-button\r\n          v-if=\"fileBim\"\r\n          style=\"margin-left: 42%\"\r\n          @click=\"fullscreen(1)\"\r\n        >全屏</el-button>\r\n      </div>\r\n      <iframe\r\n        id=\"frame\"\r\n        :key=\"iframeKey\"\r\n        :src=\"iframeUrl\"\r\n        style=\"width: 100%; border: 0px; margin: 0; height: 60vh\"\r\n      />\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { closeTagView, debounce } from '@/utils'\r\nimport BatchProcessAdjust from './components/BatchProcessAdjust'\r\nimport {\r\n  GetCanSchdulingPartList,\r\n  GetDwg,\r\n  GetSchdulingWorkingTeams,\r\n  SaveUnitSchedulingWorkshopNew,\r\n  SaveSchdulingTaskById, SaveUnitSchdulingDraftNew, GetUnitSchdulingInfoDetail\r\n} from '@/api/PRO/production-task'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport AddDraft from './components/addDraft'\r\nimport OwnerProcess from './components/OwnerProcess'\r\nimport Workshop from './components/Workshop.vue'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { getUnique, uniqueCode } from './constant'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { GetLibListType, GetProcessFlowListWithTechnology, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { AreaGetEntity } from '@/api/plm/projects'\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport moment from 'moment'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\n\r\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\n\r\nimport { getConfigure } from '@/api/user'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { getBomName } from '@/views/PRO/bom-setting/utils'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: { DynamicTableFields, TreeDetail, ExpandableSection, BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },\r\n  data() {\r\n    return {\r\n      drawer: false,\r\n      drawersull: false,\r\n      iframeKey: '',\r\n      fullscreenid: '',\r\n      iframeUrl: '',\r\n      fullbimid: '',\r\n      fileBim: '',\r\n      IsUploadCad: false,\r\n      cadRowCode: '',\r\n      cadRowProjectId: '',\r\n      tbKey: 100,\r\n      isComponentOptions: [\r\n        { label: '是', value: false },\r\n        { label: '否', value: true }\r\n      ],\r\n      specOptions: [{ data: '' }],\r\n      filterTypeOption: [{ data: '' }],\r\n      filterCodeOption: [{ data: '' }],\r\n      projectOptions: [{ data: '' }],\r\n      areaOptions: [{ data: '' }],\r\n      installOptions: [{ data: '' }],\r\n      projectList: [],\r\n      installList: [],\r\n      areaList: [],\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n        }\r\n      },\r\n      innerForm: {\r\n        projectName: '',\r\n        areaName: '',\r\n        installName: '',\r\n        searchContent: '',\r\n        searchSpecSearch: '',\r\n        searchDirect: ''\r\n      },\r\n      curSearch: 1,\r\n      searchType: '',\r\n      formInline: {\r\n        Schduling_Code: '',\r\n        Create_UserName: '',\r\n        Finish_Date: '',\r\n        InstallUnit_Id: '',\r\n        Remark: ''\r\n      },\r\n      total: 0,\r\n      currentIds: '',\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      multipleSelection: [],\r\n      showExpand: true,\r\n      pgLoading: false,\r\n      deleteLoading: false,\r\n      workShopIsOpen: false,\r\n      isOwnerNull: false,\r\n      dialogVisible: false,\r\n      openAddDraft: false,\r\n      saveLoading: false,\r\n      tbLoading: false,\r\n      isCheckAll: false,\r\n      currentComponent: '',\r\n      gridCode: '',\r\n      dWidth: '25%',\r\n      title: '',\r\n      search: () => ({}),\r\n      pageType: undefined,\r\n      tipLabel: '',\r\n      technologyOption: [],\r\n      typeOption: [],\r\n      workingTeam: [],\r\n      pageStatus: undefined,\r\n      scheduleId: '',\r\n      partComOwnerColumn: null,\r\n\r\n      installUnitIdList: [],\r\n      projectId: '',\r\n      areaId: '',\r\n      projectName: '',\r\n      statusType: '',\r\n      expandedKey: '',\r\n      // treeLoading: false,\r\n      treeData: [],\r\n      treeParamsComponentType: {\r\n        'default-expand-all': true,\r\n        'check-strictly': true,\r\n        filterable: true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        collapseTags: true,\r\n        clearable: true\r\n      },\r\n      disabledAdd: true,\r\n      levelName: '',\r\n      projectOption: []\r\n    }\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler(n, o) {\r\n        this.checkOwner()\r\n        this.doFilter()\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    isView() {\r\n      return this.pageStatus === 'view'\r\n    },\r\n    isEdit() {\r\n      return this.pageStatus === 'edit'\r\n    },\r\n    isAdd() {\r\n      return this.pageStatus === 'add'\r\n    },\r\n    addDraftKey() {\r\n      return this.expandedKey + this.formInline.InstallUnit_Id\r\n    },\r\n    // filterText() {\r\n    //   return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    // },\r\n    statusCode() {\r\n      return this.isCom ? 'Comp_Schdule_Status' : 'Part_Schdule_Status'\r\n    },\r\n    installName() {\r\n      const item = this.installUnitIdList.find(v => v.Id === this.formInline.InstallUnit_Id)\r\n      if (item) {\r\n        return item.Name\r\n      } else {\r\n        return ''\r\n      }\r\n    },\r\n    isPartPrepare() {\r\n      return this.getIsPartPrepare && !this.isCom\r\n    },\r\n    isNest() {\r\n      return false\r\n    },\r\n    isStopFlag() {\r\n      return this.tbData.some(item => item.stopFlag)\r\n    },\r\n    ...mapGetters('factoryInfo', ['workshopEnabled', 'getIsPartPrepare']),\r\n    ...mapGetters('schedule', ['processList', 'nestIds'])\r\n  },\r\n  async created() {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      // this.baseCadUrl = 'http://localhost:9529'\r\n      // this.baseCadUrl = 'http://glendale-model.bimtk.com'\r\n      this.baseCadUrl = 'http://glendale-model-dev.bimtk.tech'\r\n    } else {\r\n      getConfigure({ code: 'glendale_url' }).then((res) => {\r\n        this.baseCadUrl = res.Data\r\n      })\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.initProcessList()\r\n    this.tbDataMap = {}\r\n    this.craftCodeMap = {}\r\n    this.pageType = this.$route.query.pg_type\r\n    this.level = this.$route.query.level\r\n    this.pageStatus = this.$route.query.status\r\n    this.model = this.$route.query.model\r\n    this.scheduleId = this.$route.query.pid || ''\r\n    // // this.formInline.Create_UserName = this.$store.getters.name\r\n    // // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage\r\n    this.formInline.Create_UserName = localStorage.getItem('UserAccount')\r\n    // if (!this.isCom) {\r\n    //   this.getPartType()\r\n    // } else {\r\n    // }\r\n\r\n    this.unique = uniqueCode()\r\n    this.checkWorkshopIsOpen()\r\n\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.mergeConfig()\r\n    if (this.isView || this.isEdit) {\r\n      const { areaId, install } = this.$route.query\r\n      // this.areaId = areaId\r\n      // this.formInline.InstallUnit_Id = install\r\n      // this.getInstallUnitIdNameList()\r\n      this.fetchData()\r\n    }\r\n\r\n    if (this.isAdd) {\r\n      // this.fetchTreeData()\r\n      this.getType()\r\n    }\r\n    if (this.isEdit) {\r\n      this.getType()\r\n    }\r\n\r\n    this.levelName = await getBomName(this.level)\r\n\r\n    window.addEventListener('message', this.frameListener)\r\n    this.$once('hook:beforeDestroy', () => {\r\n      console.log('deactivated')\r\n      window.removeEventListener('message', this.frameListener)\r\n    })\r\n  },\r\n  activated() {\r\n    window.addEventListener('message', this.frameListener)\r\n    this.$once('hook:deactivated', () => {\r\n      window.removeEventListener('message', this.frameListener)\r\n    })\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['changeProcessList', 'initProcessList', 'changeAddTbKeys']),\r\n    checkOwner() {\r\n      if (this.isCom) return\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id) && !this.isNest\r\n      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')\r\n      if (this.isOwnerNull) {\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      } else {\r\n        if (idx === -1) {\r\n          if (!this.ownerColumn) {\r\n            this.$message({\r\n              message: '列表配置字段缺少部件领用工序字段',\r\n              type: 'success'\r\n            })\r\n            return\r\n          }\r\n          this.columns.push(this.ownerColumn)\r\n        }\r\n        this.comPart = true\r\n      }\r\n    },\r\n    async mergeConfig() {\r\n      await this.getConfig()\r\n      await this.getWorkTeam()\r\n    },\r\n    doFilter() {\r\n      this.projectList = []\r\n      this.installList = []\r\n      this.areaList = []\r\n      this.tbData.forEach(cur => {\r\n        if (cur.Project_Name && !this.projectList.includes(cur.Project_Name)) {\r\n          this.projectList.push(cur.Project_Name)\r\n        }\r\n        if (cur.InstallUnit_Name && !this.installList.includes(cur.InstallUnit_Name)) {\r\n          this.installList.push(cur.InstallUnit_Name)\r\n        }\r\n        if (cur.Area_Name && !this.areaList.includes(cur.Area_Name)) {\r\n          this.areaList.push(cur.Area_Name)\r\n        }\r\n      })\r\n    },\r\n    async getConfig() {\r\n      let configCode = ''\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          configCode = 'PRONestingScheduleDetail'\r\n        } else {\r\n          configCode = 'PRONestingScheduleConfig'\r\n        }\r\n      } else {\r\n        configCode = (this.isView ? 'PROUnitPartViewPageTbConfig_new' : 'PROUnitPartDraftPageTbConfig_new')\r\n      }\r\n      this.gridCode = configCode\r\n      await this.getTableConfig(configCode)\r\n      if (!this.workshopEnabled) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')\r\n      }\r\n      this.checkOwner()\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n      this.tbKey++\r\n    },\r\n    /*    handleNodeClick(data) {\r\n      console.log('data', data)\r\n      if (this.areaId === data.Id) {\r\n        return\r\n      }\r\n      this.\r\n       = true\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      const initData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n        this.formInline.Finish_Date = ''\r\n        this.formInline.InstallUnit_Id = ''\r\n        this.formInline.Remark = ''\r\n        this.tbData = []\r\n        this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      if (this.tbData.length) {\r\n        this.$confirm('切换区域右侧数据清空，是否确认?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          initData(data)\r\n          this.disabledAdd = false\r\n          this.tbDataMap = {}\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      } else {\r\n        this.disabledAdd = false\r\n        initData(data)\r\n      }\r\n    },*/\r\n\r\n    /* customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },*/\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      let resData = null\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          resData = await this.getPartPageList()\r\n        } else {\r\n          resData = await this.getNestPageList()\r\n        }\r\n      } else {\r\n        resData = await this.getPartPageList()\r\n      }\r\n\r\n      this.initTbData(resData)\r\n      this.tbLoading = false\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    fetchTreeStatus() {\r\n      // this.filterText = this.statusType\r\n    },\r\n    /*    fetchTreeData() {\r\n      this.treeLoading = true\r\n      GetProjectAreaTreeList({ projectName: this.projectName, type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data.map(item => {\r\n          item.Is_Directory = true\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        this.setKey()\r\n        this.treeLoading = false\r\n      })\r\n    },*/\r\n    // setKey() {\r\n    //   const deepFilter = (tree) => {\r\n    //     for (let i = 0; i < tree.length; i++) {\r\n    //       const item = tree[i]\r\n    //       const { Data, Children } = item\r\n    //       console.log(Data)\r\n    //       if (Data.ParentId && !Children?.length) {\r\n    //         this.handleNodeClick(item)\r\n    //         return\r\n    //       } else {\r\n    //         if (Children && Children.length > 0) {\r\n    //           return deepFilter(Children)\r\n    //         }\r\n    //       }\r\n    //     }\r\n    //   }\r\n    //   return deepFilter(this.treeData)\r\n    // },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    checkWorkshopIsOpen() {\r\n      this.workShopIsOpen = true\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    getAreaInfo() {\r\n      this.formInline.Finish_Date = ''\r\n      AreaGetEntity({\r\n        id: this.areaId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            return []\r\n          }\r\n\r\n          const start = moment(res.Data?.Demand_Begin_Date)\r\n          const end = moment(res.Data?.Demand_End_Date)\r\n          this.pickerOptions.disabledDate = (time) => {\r\n            return time.getTime() < start || time.getTime() > end\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.openAddDraft = false\r\n    },\r\n    getNestPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        GetCanSchdulingPartList({\r\n          Ids: this.nestIds\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const _list = res?.Data || []\r\n            const list = _list.map(v => {\r\n              v.Part_Used_Process = v.Scheduled_Used_Process || v.Part_Type_Used_Process\r\n              // v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n              v.Workshop_Id = v.Scheduled_Workshop_Id\r\n              v.Workshop_Name = v.Scheduled_Workshop_Name\r\n              v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n              v.chooseCount = v.Can_Schduling_Count\r\n\r\n              return v\r\n            })\r\n\r\n            resolve(list)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async getPartPageList() {\r\n      const {\r\n        pid\r\n      } = this.$route.query\r\n      const result = await GetUnitSchdulingInfoDetail({\r\n        Schduling_Plan_Id: pid\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const SarePartsModel = res.Data?.SarePartsModel.map(v => {\r\n            if (v.Scheduled_Used_Process) {\r\n              // 已存在操作过数据\r\n              v.Part_Used_Process = v.Scheduled_Used_Process\r\n            } if (v.Unit_Part_Used_Process) {\r\n              // 已存在操作过数据\r\n              v.Part_Used_Process = v.Unit_Part_Used_Process\r\n            }\r\n            v.chooseCount = v.Can_Schduling_Count\r\n            return v\r\n          })\r\n          this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)\r\n          res.Data?.Process_List.forEach(item => {\r\n            const plist = {\r\n              key: item.Process_Code,\r\n              value: item\r\n            }\r\n            this.changeProcessList(plist)\r\n          })\r\n\r\n          this.getStopList(SarePartsModel)\r\n          return SarePartsModel\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          return []\r\n        }\r\n      })\r\n      console.log('result', result)\r\n      return result || []\r\n    },\r\n    async getStopList(list) {\r\n      console.log('getStopList', list)\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 3\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      console.log(5, JSON.parse(JSON.stringify(list)))\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        row.uuid = uuidv4()\r\n        this.addElementToTbData(row)\r\n        if (row[teamKey]) {\r\n          const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n          newData.forEach((ele, index) => {\r\n            const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            row[code] = ele.Count\r\n            row[max] = 0\r\n          })\r\n        }\r\n        this.setInputMax(row)\r\n        return row\r\n      })\r\n      let ids = ''\r\n      if (this.isCom) {\r\n        ids = this.tbData.map(v => v.Comp_Import_Detail_Id).toString()\r\n      } else {\r\n        ids = this.tbData.map(v => v.Part_Aggregate_Id).toString()\r\n      }\r\n      this.currentIds = ids\r\n    },\r\n    async addToTbList(newList) {\r\n      await this.mergeSelectList(newList)\r\n      this.setAddTbKey()\r\n    },\r\n    async mergeSelectList(newList) {\r\n      console.time('fff')\r\n      await this.mergeCraftProcess(newList)\r\n      let hasUsedPartFlag = true\r\n      newList.forEach((element, index) => {\r\n        const cur = this.getMergeUniqueRow(element)\r\n        console.log(1, JSON.parse(JSON.stringify(element)))\r\n        if (!element.Technology_Path) {\r\n          const curPathArr = this.craftCodeMap[element.Technology_Code]\r\n          if (this.craftCodeMap[element.Technology_Code] instanceof Array) {\r\n            if (element.Unit_Part_Used_Process) {\r\n              const partUsedProcessArr = element.Unit_Part_Used_Process.split(',')\r\n              const allPartsIncluded = partUsedProcessArr.every(part => curPathArr.includes(part))\r\n\r\n              if (!allPartsIncluded) {\r\n                hasUsedPartFlag = false\r\n              } else {\r\n                element.Technology_Path = curPathArr.join('/')\r\n              }\r\n            } else {\r\n              element.Technology_Path = curPathArr.join('/')\r\n            }\r\n          }\r\n        }\r\n        if (!cur) {\r\n          element.puuid = element.uuid\r\n          element.Schduled_Count = element.chooseCount\r\n          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')\r\n          this.tbData.push(element)\r\n          this.addElementToTbData(element)\r\n          return\r\n        }\r\n\r\n        cur.puuid = element.uuid\r\n\r\n        cur.Schduled_Count += element.chooseCount\r\n        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')\r\n        if (!cur.Technology_Path) {\r\n          return\r\n        }\r\n        this.setInputMax(cur)\r\n      })\r\n      console.log('hasUsedPartFlag', hasUsedPartFlag)\r\n      this.showCraftUsedPartResult(hasUsedPartFlag)\r\n      // if (this.isCom) {\r\n      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      // } else {\r\n      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))\r\n      // }\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.timeEnd('fff')\r\n      console.log('this.tbDataMap', this.tbDataMap, this.tbData)\r\n    },\r\n    showCraftUsedPartResult(hasUsedPart) {\r\n      if (hasUsedPart) return true\r\n      setTimeout(() => {\r\n        this.$alert('部分部件工序路径内不包含零件领用工序请手动排产', '提示', {\r\n          confirmButtonText: '确定'\r\n        })\r\n      }, 200)\r\n      return false\r\n    },\r\n    addElementToTbData(element) {\r\n      const key = this.getUniKey(element)\r\n      this.tbDataMap[key] = element\r\n    },\r\n    getMergeUniqueRow(element) {\r\n      const key = this.getUniKey(element)\r\n      return this.tbDataMap[key]\r\n    },\r\n    getUniKey(element) {\r\n      return getUnique(this.isCom, element)\r\n    },\r\n    checkForm() {\r\n      let isValidate = true\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) isValidate = false\r\n      })\r\n      return isValidate\r\n    },\r\n    async saveDraft(isOrder = false) {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return false\r\n      if (!isOrder) {\r\n        this.saveLoading = true\r\n      }\r\n\r\n      const isSuccess = await this.handleSaveDraft(tableData, isOrder)\r\n      console.log('isSuccess', isSuccess)\r\n      if (!isSuccess) return false\r\n      if (isOrder) return isSuccess\r\n      this.$refs['draft']?.fetchData()\r\n      this.saveLoading = false\r\n    },\r\n    async saveWorkShop() {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const obj = {}\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'success'\r\n        })\r\n        return\r\n      }\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = this.tbData\r\n      } else {\r\n        obj.SarePartsModel = this.tbData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      this.pgLoading = true\r\n\r\n      SaveUnitSchedulingWorkshopNew(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.pgLoading = false\r\n        }\r\n      })\r\n    },\r\n    getSubmitTbInfo() {\r\n      // 处理上传的数据\r\n      let tableData = JSON.parse(JSON.stringify(this.tbData))\r\n      tableData = tableData.filter(item => item.Schduled_Count > 0)\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        let list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (this.isPartPrepare && !element.Part_Used_Process && element.Type !== 'Direct' && this.comPart) {\r\n          const msg = '领用工序不能为空'\r\n          if (this.isNest) {\r\n            if (element.Comp_Import_Detail_Id) {\r\n              this.$message({\r\n                message: msg,\r\n                type: 'warning'\r\n              })\r\n              return { status: false }\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: msg,\r\n              type: 'warning'\r\n            })\r\n            return { status: false }\r\n          }\r\n        }\r\n        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {\r\n        //   // 零构件 零件单独排产\r\n        //   this.$message({\r\n        //     message: '零件领用工序不能为空',\r\n        //     type: 'warning'\r\n        //   })\r\n        //   return { status: false }\r\n        // }\r\n        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.isCom ? '构件' : '部件'}保持工序一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同部件领用工序保持一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        // processList.forEach(code => {\r\n        //   const groups = element.Allocation_Teams.filter(v => v.Process_Code === code)\r\n        //    const groupsList = groups.map(group => {\r\n        //   const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n        //   const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n        //   const obj = {\r\n        //     Team_Task_Id: element.Team_Task_Id,\r\n        //     Comp_Code: element.Comp_Code,\r\n        //     Again_Count: +element[uCode] || 0, // 不填，后台让传0\r\n        //     Part_Code: this.isCom ? null : '',\r\n        //     Process_Code: code,\r\n        //     Technology_Path: element.Technology_Path,\r\n        //     Working_Team_Id: group.Working_Team_Id,\r\n        //     Working_Team_Name: group.Working_Team_Name\r\n        //   }\r\n        //   delete element[uCode]\r\n        //   delete element[uMax]\r\n        //   return obj\r\n        // })\r\n        // const againCount = list.reduce((acc, cur) => {\r\n        //   return acc + cur.Again_Count\r\n        // }, 0)\r\n        // if (againCount > element.Schduled_Count) {\r\n        //   element.Allocation_Teams = []\r\n        // }\r\n\r\n        for (let j = 0; j < processList.length; j++) {\r\n          const code = processList[j]\r\n          const schduledCount = element.Schduled_Count || 0\r\n          let groups = []\r\n          if (element.Allocation_Teams) {\r\n            groups = element.Allocation_Teams.filter(v => v.Process_Code === code)\r\n          }\r\n          const againCount = groups.reduce((acc, cur) => {\r\n            return acc + (cur.Again_Count || 0)\r\n          }, 0)\r\n          if (againCount > schduledCount) {\r\n            list = []\r\n            break\r\n          } else {\r\n            list.push(...groups)\r\n          }\r\n        }\r\n\r\n        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))\r\n        hasInput.forEach((item) => {\r\n          delete element[item]\r\n        })\r\n        delete element['uuid']\r\n        delete element['_X_ROW_KEY']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    async handleSaveDraft(tableData, isOrder) {\r\n      console.log('保存草稿')\r\n      const _fun = SaveUnitSchdulingDraftNew\r\n      const obj = {}\r\n      obj.SarePartsModel = tableData\r\n      const p = []\r\n      for (const objKey in this.processList) {\r\n        if (this.processList.hasOwnProperty(objKey)) {\r\n          p.push(this.processList[objKey])\r\n        }\r\n      }\r\n      obj.Process_List = p\r\n\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      let orderSuccess = false\r\n      console.log('obj', obj)\r\n\r\n      await _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!isOrder) {\r\n            this.pgLoading = false\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.templateScheduleCode = res.Data\r\n            orderSuccess = true\r\n            console.log('保存草稿成功 ')\r\n          }\r\n        } else {\r\n          this.saveLoading = false\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log('结束 ')\r\n      return orderSuccess\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))\r\n        this.tbData = this.tbData.filter(item => {\r\n          const isSelected = selectedUuids.has(item.uuid)\r\n          if (isSelected) {\r\n            const key = this.getUniKey(item)\r\n            delete this.tbDataMap[key]\r\n          }\r\n          return !isSelected\r\n        })\r\n        // this.$nextTick(_ => {\r\n        //   const _list = this.multipleSelection.filter(v => v.puuid)\r\n        //   this.$refs['draft']?.mergeData(_list)\r\n        //   this.multipleSelection = []\r\n        // })\r\n        this.deleteLoading = false\r\n      }, 0)\r\n    },\r\n    async getWorkTeam() {\r\n      await GetSchdulingWorkingTeams({\r\n        type: 3\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.workingTeam = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) return\r\n        const { tableData, status } = this.getSubmitTbInfo()\r\n        if (!status) return\r\n        this.$confirm('是否提交当前数据?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.saveDraftDoSubmit(tableData)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      })\r\n    },\r\n    async saveDraftDoSubmit() {\r\n      this.pgLoading = true\r\n      if (this.formInline?.Schduling_Code) {\r\n        const isSuccess = await this.saveDraft(true)\r\n        console.log('saveDraftDoSubmit', isSuccess)\r\n        isSuccess && this.doSubmit(this.formInline.Id)\r\n      } else {\r\n        const isSuccess = await this.saveDraft(true)\r\n        isSuccess && this.doSubmit(this.templateScheduleCode)\r\n      }\r\n    },\r\n    doSubmit(scheduleCode) {\r\n      SaveSchdulingTaskById({\r\n        schdulingPlanId: scheduleCode\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '下达成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.pgLoading = false\r\n      }).catch(_ => {\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    getWorkShop(value) {\r\n      console.log('value', value)\r\n      const {\r\n        origin,\r\n        row,\r\n        workShop: {\r\n          Id,\r\n          Display_Name\r\n        }\r\n      } = value\r\n      if (origin === 2) {\r\n        if (value.workShop?.Id) {\r\n          row.Workshop_Name = Display_Name\r\n          row.Workshop_Id = Id\r\n          this.setPath(row, Id)\r\n        } else {\r\n          row.Workshop_Name = ''\r\n          row.Workshop_Id = ''\r\n        }\r\n      } else {\r\n        this.multipleSelection.forEach(item => {\r\n          if (value.workShop?.Id) {\r\n            item.Workshop_Name = Display_Name\r\n            item.Workshop_Id = Id\r\n            this.setPath(item, Id)\r\n          } else {\r\n            item.Workshop_Name = ''\r\n            item.Workshop_Id = ''\r\n          }\r\n        })\r\n      }\r\n    },\r\n    setPath(row, Id) {\r\n      if (row?.Scheduled_Workshop_Id) {\r\n        if (row.Scheduled_Workshop_Id !== Id) {\r\n          row.Technology_Path = ''\r\n        }\r\n      } else {\r\n        row.Technology_Path = ''\r\n      }\r\n    },\r\n    handleBatchWorkshop(origin, row) {\r\n      this.title = origin === 1 ? '批量分配车间' : '分配车间'\r\n      this.currentComponent = 'Workshop'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].fetchData(origin, row)\r\n      })\r\n    },\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 3\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const process = res.Data.map(v => v.Code)\r\n            resolve(process)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    setLibType(code, workshopId) {\r\n      return new Promise((resolve) => {\r\n        const obj = {\r\n          Component_type: code,\r\n          type: 1\r\n        }\r\n        if (this.workshopEnabled) {\r\n          obj.workshopId = workshopId\r\n        }\r\n        GetLibListType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            if (res.Data.Data && res.Data.Data.length) {\r\n              const info = res.Data.Data[0]\r\n              const workCode = info.WorkCode && info.WorkCode.replace(/\\\\/g, '/')\r\n              resolve(workCode)\r\n            } else {\r\n              resolve(undefined)\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n      })\r\n    },\r\n    sendProcess({ arr, str }) {\r\n      let isSuccess = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (item.originalPath && item.originalPath !== str) {\r\n          isSuccess = false\r\n          break\r\n        }\r\n        item.Technology_Path = str\r\n      }\r\n      if (!isSuccess) {\r\n        this.$message({\r\n          message: '请和该区域批次下已排产同部件保持工序一致',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    resetWorkTeamMax(row, str) {\r\n      if (str) {\r\n        row.Technology_Path = str\r\n      } else {\r\n        str = row.Technology_Path\r\n      }\r\n      const list = str?.split('/') || []\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const cur = list.some(k => k === element.Process_Code)\r\n        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        if (cur) {\r\n          if (!row[code]) {\r\n            this.$set(row, code, 0)\r\n            this.$set(row, max, row.Schduled_Count)\r\n          }\r\n        } else {\r\n          this.$delete(row, code)\r\n          this.$delete(row, max)\r\n        }\r\n      })\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')\r\n          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')\r\n          this.columns = this.setColumnDisplay(list)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setColumnDisplay(list) {\r\n      return list.filter(v => v.Is_Display)\r\n      // .map(item => {\r\n      //   if (FIX_COLUMN.includes(item.Code)) {\r\n      //     item.fixed = 'left'\r\n      //   }\r\n      //   return item\r\n      // })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    openBPADialog(type, row) {\r\n      if (this.workshopEnabled) {\r\n        if (type === 1) {\r\n          const IsUnique = this.checkIsUniqueWorkshop()\r\n          if (!IsUnique) return\r\n        }\r\n      }\r\n      this.title = type === 2 ? '工序调整' : '批量工序调整'\r\n      this.currentComponent = 'BatchProcessAdjust'\r\n      this.dWidth = '50%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')\r\n      })\r\n    },\r\n    checkIsUniqueWorkshop() {\r\n      let isUnique = true\r\n      const firstV = this.multipleSelection[0].Workshop_Name\r\n      for (let i = 1; i < this.multipleSelection.length; i++) {\r\n        const item = this.multipleSelection[i]\r\n        if (item.Workshop_Name !== firstV) {\r\n          isUnique = false\r\n          break\r\n        }\r\n      }\r\n      if (!isUnique) {\r\n        this.$message({\r\n          message: '批量分配工序时只有相同车间下的才可一起批量分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return isUnique\r\n    },\r\n    checkHasWorkShop(type, arr) {\r\n      let hasWorkShop = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (!item.Workshop_Name) {\r\n          hasWorkShop = false\r\n          break\r\n        }\r\n      }\r\n      if (!hasWorkShop) {\r\n        this.$message({\r\n          message: '请先选择车间后再进行工序分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return hasWorkShop\r\n    },\r\n    handleAddDialog(type = 'add') {\r\n      this.title = '添加部件'\r\n\r\n      this.currentComponent = 'AddDraft'\r\n      this.dWidth = '96%'\r\n      this.openAddDraft = true\r\n\r\n      this.setAddTbKey()\r\n\r\n      this.$nextTick(_ => {\r\n        this.$refs['draft'].initData()\r\n      })\r\n    },\r\n    setAddTbKey() {\r\n      const selectKeys = this.tbData.filter(cur => cur.puuid).map(v => v.puuid)\r\n      this.changeAddTbKeys(selectKeys)\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    handleSelectMenu(v) {\r\n      if (v === 'process') {\r\n        this.openBPADialog(1)\r\n      } else if (v === 'craft') {\r\n        this.handleSetCraftProcess()\r\n      }\r\n    },\r\n    async mergeCraftProcess(list) {\r\n      let codes = [...new Set(list.map(v => v.Technology_Code))]\r\n      for (const key in this.craftCodeMap) {\r\n        if (this.craftCodeMap.hasOwnProperty(key)) {\r\n          codes = codes.filter(code => code !== key)\r\n        }\r\n      }\r\n      const _craftCodeMap = await this.getCraftProcess(codes)\r\n      Object.assign(this.craftCodeMap, _craftCodeMap)\r\n    },\r\n    async handleSetCraftProcess() {\r\n      const showSuccess = () => {\r\n        this.$message({\r\n          message: '已分配成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n      const rowList = this.multipleSelection.map(v => v.Technology_Code).filter(v => !!v)\r\n      if (!rowList.length) {\r\n        this.$message({\r\n          message: '工艺代码不存在',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      await this.mergeCraftProcess(this.multipleSelection)\r\n      const workshopIds = Array.from(new Set(this.multipleSelection.map(v => v.Workshop_Id).filter(v => !!v)))\r\n      const w_process = []\r\n      if (workshopIds.length) {\r\n        workshopIds.forEach(workshopId => {\r\n          w_process.push(this.getProcessOption(workshopId).then(result => ({\r\n            [workshopId]: result\r\n          })))\r\n        })\r\n        const workshopPromise = Promise.all(w_process).then((values) => {\r\n          return Object.assign({}, ...values)\r\n        })\r\n        workshopPromise.then(workshop => {\r\n          let flag = true\r\n          for (let i = 0; i < this.multipleSelection.length; i++) {\r\n            const curRow = this.multipleSelection[i]\r\n            const workshopProcess = workshop[curRow.Workshop_Id]\r\n            const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n            if (craftArray) {\r\n              const isIncluded = craftArray.every(process => workshopProcess.includes(process))\r\n              if (!isIncluded) {\r\n                flag = false\r\n                continue\r\n              }\r\n              curRow.Technology_Path = craftArray.join('/')\r\n            }\r\n          }\r\n          if (!flag) {\r\n            setTimeout(() => {\r\n              this.$alert('所选车间下班组加工工序不包含工艺代码工序请手动排产', '提示', {\r\n                confirmButtonText: '确定'\r\n              })\r\n            }, 200)\r\n          }\r\n\r\n          flag && showSuccess()\r\n        })\r\n      } else {\r\n        this.multipleSelection.forEach((curRow) => {\r\n          const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n          if (craftArray) {\r\n            curRow.Technology_Path = craftArray.join('/')\r\n          }\r\n        })\r\n        showSuccess()\r\n      }\r\n    },\r\n    handleBatchOwner(type, row) {\r\n      this.title = '批量分配领用工序'\r\n      this.currentComponent = 'OwnerProcess'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)\r\n      })\r\n    },\r\n    getCraftProcess(gyGroup = []) {\r\n      gyGroup = gyGroup.filter(v => !!v)\r\n      if (!gyGroup.length) return Promise.resolve({})\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessFlowListWithTechnology({\r\n          TechnologyCodes: gyGroup,\r\n          Type: 3\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const gyList = res.Data || []\r\n            const gyMap = gyList.reduce((acc, item) => {\r\n              acc[item.Code] = item.Technology_Path\r\n              return acc\r\n            }, {})\r\n            console.log('gyMap', gyMap)\r\n            resolve(gyMap)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handleReverse() {\r\n      const cur = []\r\n      this.tbData.forEach((element, idx) => {\r\n        element.checked = !element.checked\r\n        if (element.checked) {\r\n          cur.push(element)\r\n        }\r\n      })\r\n      this.multipleSelection = cur\r\n      if (this.multipleSelection.length === this.tbData.length) {\r\n        this.$refs['xTable'].setAllCheckboxRow(true)\r\n      }\r\n      if (this.multipleSelection.length === 0) {\r\n        this.$refs['xTable'].setAllCheckboxRow(false)\r\n      }\r\n    },\r\n    // tbFilterChange() {\r\n    //   const xTable = this.$refs.xTable\r\n    //   const column = xTable.getColumnByField('Type_Name')\r\n    //   if (!column?.filters?.length) return\r\n    //   column.filters.forEach(d => {\r\n    //     d.checked = d.value === this.searchType\r\n    //   })\r\n    //   xTable.updateData()\r\n    // },\r\n    getType() {\r\n      const getCompTree = () => {\r\n        const fun = this.isCom ? GetCompTypeTree : GetPartTypeList\r\n        fun({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            let result = res.Data\r\n            if (!this.isCom) {\r\n              result = result\r\n                .map((v, idx) => {\r\n                  return {\r\n                    Data: v.Name,\r\n                    Label: v.Name\r\n                  }\r\n                })\r\n            }\r\n            this.treeParamsComponentType.data = result\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectComponentType?.treeDataUpdateFun(result)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      getCompTree()\r\n    },\r\n    // 查看图纸\r\n    handleDwg(row) {\r\n      console.log('row', row)\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Comp_Id = row.Comp_Import_Detail_Id\r\n      } else {\r\n        obj.Part_Id = row.Part_Aggregate_Id\r\n      }\r\n      GetSteelCadAndBimId({ importDetailId: obj.Part_Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.extensionName = res.Data[0].ExtensionName\r\n          this.fileBim = res.Data[0].fileBim\r\n          this.IsUploadCad = res.Data[0].IsUpload\r\n          this.cadRowCode = row.Part_Code\r\n          this.cadRowProjectId = row.Sys_Project_Id\r\n          this.fileView()\r\n        }\r\n      })\r\n\r\n      // GetDwg(obj).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     const fileurl = res?.Data?.length && res.Data[0].File_Url\r\n      //     window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl), '_blank')\r\n      //   } else {\r\n      //     this.$message({\r\n      //       message: res.Message,\r\n      //       type: 'error'\r\n      //     })\r\n      //   }\r\n      // })\r\n    },\r\n    setProcessList(info) {\r\n      this.changeProcessList(info)\r\n    },\r\n    resetInnerForm() {\r\n      this.$refs['searchForm'].resetFields()\r\n      this.$refs.xTable.clearFilter()\r\n    },\r\n    innerFilter() {\r\n      this.multipleSelection = []\r\n      const arr = []\r\n      if (this.isCom) {\r\n        arr.push('Type', 'Comp_Code', 'Spec', 'Is_Component')\r\n      } else {\r\n        arr.push('Part_Code', 'Spec', 'Type_Name', 'Project_Name', 'Area_Name', 'InstallUnit_Name')\r\n      }\r\n\r\n      const xTable = this.$refs.xTable\r\n      xTable.clearCheckboxRow()\r\n      arr.forEach((element, idx) => {\r\n        const column = xTable.getColumnByField(element)\r\n        if (element === 'Is_Component') {\r\n          column.filters.forEach((option, idx) => {\r\n            option.checked = idx === (this.innerForm.searchDirect ? 0 : 1)\r\n          })\r\n        }\r\n        if (element === 'Spec') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchSpecSearch\r\n          option.checked = true\r\n        }\r\n\r\n        if (element === 'Comp_Code' || element === 'Part_Code') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchContent\r\n          option.checked = true\r\n        }\r\n        if (element === 'Project_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.projectName\r\n          option.checked = true\r\n        }\r\n        if (element === 'Area_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.areaName\r\n          option.checked = true\r\n        }\r\n        if (element === 'InstallUnit_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.installName\r\n          option.checked = true\r\n        }\r\n      })\r\n      xTable.updateData()\r\n    },\r\n    filterComponentMethod({ option, row }) {\r\n      if (this.innerForm.searchDirect === '') {\r\n        return true\r\n      }\r\n      return row.Is_Component === !this.innerForm.searchDirect\r\n    },\r\n    filterSpecMethod({ option, row }) {\r\n      if (this.innerForm.searchSpecSearch.trim() === '') {\r\n        return true\r\n      }\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n      const specArray = splitAndClean(this.innerForm.searchSpecSearch)\r\n      return specArray.some(code => (row.Spec || '').includes(code))\r\n    },\r\n\r\n    filterCodeMethod({ option, row }) {\r\n      if (this.innerForm.searchContent.trim() === '') {\r\n        return true\r\n      }\r\n\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      const cur = this.isCom ? 'Comp_Code' : 'Part_Code'\r\n\r\n      const arr = splitAndClean(this.innerForm.searchContent)\r\n\r\n      if (this.curSearch === 1) {\r\n        return arr.some(code => row[cur] === code)\r\n      } else {\r\n        for (let i = 0; i < arr.length; i++) {\r\n          const item = arr[i]\r\n          if (row[cur].includes(item)) {\r\n            return true\r\n          }\r\n        }\r\n        return false\r\n      }\r\n    },\r\n    filterProjectMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.Project_Name === option.data\r\n    },\r\n    filterAreaMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.Area_Name === option.data\r\n    },\r\n    filterInstallMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.InstallUnit_Name === option.data\r\n    },\r\n    componentTypeFilter(e) {\r\n      this.$refs?.treeSelectComponentType.filterFun(e)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n        this.disabledAdd = false\r\n      } else {\r\n        this.disabledAdd = true\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data\r\n          if (this.installUnitIdList.length) {\r\n            this.formInline.InstallUnit_Id = this.installUnitIdList[0].Id\r\n          }\r\n          this.disabledAdd = false\r\n        })\r\n      }\r\n    },\r\n    installChange() {\r\n      if (!this.tbData.length) {\r\n        this.$refs['searchForm'].resetFields()\r\n        this.$refs.xTable.clearFilter()\r\n        return\r\n      }\r\n      this.$confirm('切换区域右侧数据清空, 是否确认?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbData = []\r\n        this.resetInnerForm()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    showPartUsedProcess(row) {\r\n      if (this.isNest) {\r\n        return !!row.Comp_Import_Detail_Id\r\n      } else {\r\n        return !this.isView && row.Type !== 'Direct'\r\n      }\r\n    },\r\n    handleExport() {\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '暂无数据',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$refs.xTable.exportData({\r\n        filename: `部件排产-${this.formInline.Schduling_Code}(部件)`,\r\n        type: 'xlsx',\r\n        data: this.tbData\r\n      })\r\n    },\r\n    handleCloseDrawer() {\r\n      this.drawer = false\r\n    },\r\n    fileView() {\r\n      this.iframeKey = uuidv4()\r\n      this.iframeUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=11&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.drawer = true\r\n    },\r\n    renderIframe() {\r\n      const ExtensionName = this.extensionName\r\n      const fileBim = this.fileBim\r\n      this.iframeUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=11&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.fullscreenid = ExtensionName\r\n      this.fullbimid = fileBim\r\n    },\r\n    fullscreen(v) {\r\n      this.templateUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=13&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.drawersull = true\r\n    },\r\n    frameListener({ data }) {\r\n      if (data.type === 'loaded') {\r\n        console.log('data', data)\r\n        console.error(\r\n          'data.data.iframeId',\r\n          data.data.iframeId,\r\n          typeof data.data.iframeId\r\n        )\r\n        if (data.data.iframeId === '11') {\r\n          document.getElementById('frame').contentWindow.postMessage(\r\n            {\r\n              type: 'router',\r\n              path: '/modelCad',\r\n              query: {\r\n                // baseUrl: baseUrl(),\r\n                cadId: this.fileBim,\r\n                projectId: this.cadRowProjectId,\r\n                steelName: this.cadRowCode,\r\n                showCad: this.IsUploadCad,\r\n                isSubAssembly: true,\r\n                isPart: true\r\n                // cadId: this.fileBim\r\n                // token: localStorage.getItem('Token'),\r\n                // auth_id: localStorage.getItem('Last_Working_Object_Id')\r\n              }\r\n            },\r\n            '*'\r\n          )\r\n        } else if (data.data.iframeId === '13') {\r\n          document.getElementById('fullFrame').contentWindow.postMessage(\r\n            {\r\n              type: 'router',\r\n              path: '/modelCad',\r\n              query: {\r\n                // baseUrl: baseUrl(),\r\n                cadId: this.fileBim,\r\n                projectId: this.cadRowProjectId,\r\n                steelName: this.cadRowCode,\r\n                showCad: this.IsUploadCad,\r\n                isSubAssembly: true,\r\n                isPart: true\r\n                // token: localStorage.getItem('Token'),\r\n                // auth_id: localStorage.getItem('Last_Working_Object_Id')\r\n              }\r\n            },\r\n            '*'\r\n          )\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.flex-row {\r\n  display: flex;\r\n\r\n  .cs-left {\r\n    background-color: #ffffff;\r\n    margin-right: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n    .cs-tree-wrapper {\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      overflow: hidden;\r\n      padding: 16px;\r\n\r\n      .tree-search {\r\n        display: flex;\r\n\r\n        .search-select {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .el-tree {\r\n        flex: 1;\r\n        overflow: auto;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.btn-x {\r\n  //margin-bottom: 16px;\r\n\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.demo-form-inline {\r\n  ::v-deep {\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-column-row{\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .cs-ell{\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n\r\n  }\r\n}\r\n</style>\r\n"]}]}
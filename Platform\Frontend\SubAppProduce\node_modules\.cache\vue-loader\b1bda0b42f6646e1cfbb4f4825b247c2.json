{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\partRecognitionConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\partRecognitionConfig.vue", "mtime": 1757468113429}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["partRecognitionConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "partRecognitionConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-x\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n        <el-form-item label=\"是否启用\" prop=\"enable\">\r\n          <el-radio-group v-model=\"form.enable\">\r\n            <el-radio :label=\"false\">否</el-radio>\r\n            <el-radio :label=\"true\">是</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <template v-if=\"form.enable\">\r\n          <el-form-item label=\"识别类型\" prop=\"identifyAttr\">\r\n            <el-radio-group v-model=\"form.identifyAttr\">\r\n              <el-radio :label=\"1\">{{ currentBomName }}名称前缀</el-radio>\r\n              <el-radio :label=\"2\">{{ currentBomName }}规格前缀</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-for=\"(element,index) in list\" :key=\"index\" :show-message=\"false\" :label=\"element.Part_Type_Name\" prop=\"mainPart\">\r\n            <el-input v-model.trim=\"form['item'+index]\" :placeholder=\"`请输入（多个使用'${splitSymbol}'隔开），单个配置不超过10个字符`\" clearable @blur=\"mainBlur\" />\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { uniqueArr } from '@/utils'\r\nimport {\r\n  GetFactoryPartTypeIndentifySetting,\r\n  SavePartTypeIdentifySetting\r\n} from '@/api/PRO/partType'\r\n\r\nconst SPLITVALUE = '|'\r\n\r\nexport default {\r\n  props: {\r\n    bomList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    level: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      form: {\r\n        enable: false,\r\n        identifyAttr: 1 // 默认为零件名称前缀\r\n      },\r\n      list: [],\r\n      splitSymbol: SPLITVALUE,\r\n      btnLoading: false\r\n    }\r\n  },\r\n  computed: {\r\n    currentBomName() {\r\n      return this.bomList.find(item => +item.Code === this.level)?.Display_Name\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    async getTypeList() {\r\n      GetFactoryPartTypeIndentifySetting({\r\n        Part_Grade: 0\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const { Is_Enabled, Setting_List } = res.Data\r\n          this.form.enable = Is_Enabled\r\n          this.list = Setting_List.map((v, index) => {\r\n            this.$set(this.form, 'item' + index, v.Prefixs || '')\r\n            return v\r\n          })\r\n\r\n          // 获取Setting_List中的Identify_Attr，如果有效（值为1或2）则使用，否则默认为1\r\n          if (Setting_List.length > 0) {\r\n            const identifyAttr = Setting_List[0].Identify_Attr\r\n            this.form.identifyAttr = (identifyAttr === 1 || identifyAttr === 2) ? identifyAttr : 1\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      if (this.form.enable) {\r\n        const arr = []\r\n        console.log('this.form', this.form)\r\n        for (let i = 0; i < this.list.length; i++) {\r\n          const regex = /^(?!.*\\|\\|)(?!.*\\|$)(?!^\\|)[^|]{1,10}(?:\\|[^|]{1,10})*$/\r\n          if (!regex.test(this.form[`item${i}`])) {\r\n            this.$message({\r\n              message: `${this.list[i].Part_Type_Name}配置不符合要求`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          const item = this.form[`item${i}`].split(this.splitSymbol).filter(v => !!v)\r\n\r\n          if (item.length === 0) {\r\n            this.$message({\r\n              message: `${this.list[i].Part_Type_Name}不能为空`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          for (let j = 0; j < item.length; j++) {\r\n            const d = item[j]\r\n            if (d.length > 10) {\r\n              this.$message({\r\n                message: `${this.list[i].Part_Type_Name}单个配置，不能超过10个字符`,\r\n                type: 'warning'\r\n              })\r\n              return\r\n            }\r\n          }\r\n\r\n          arr.push(...item)\r\n        }\r\n        const uniArr = uniqueArr(arr)\r\n        if (uniArr.length !== arr.length) {\r\n          this.$message({\r\n            message: '配置不能相同',\r\n            type: 'warning'\r\n          })\r\n          return\r\n        }\r\n      }\r\n      this.btnLoading = true\r\n      SavePartTypeIdentifySetting({\r\n        Is_Enabled: this.form.enable,\r\n        Setting_List: this.list.map((v, i) => {\r\n          return {\r\n            ...v,\r\n            Prefixs: this.form[`item${i}`],\r\n            Identify_Attr: this.form.identifyAttr\r\n          }\r\n        })\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    mainBlur(e) {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.form-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  max-height: 70vh;\r\n  .form-x{\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n  }\r\n  .btn-x {\r\n    padding-top: 16px;\r\n    text-align: right;\r\n  }\r\n\r\n}\r\n</style>\r\n"]}]}
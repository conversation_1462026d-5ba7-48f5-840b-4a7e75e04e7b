{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\BatchEditor.vue?vue&type=template&id=7b7d95d2&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\BatchEditor.vue", "mtime": 1758266753107}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJkaXYiLAogICAgWwogICAgICBfdm0uX2woX3ZtLmxpc3QsIGZ1bmN0aW9uIChpbmZvLCBpbmRleCkgewogICAgICAgIHJldHVybiBfYygiZWwtcm93IiwgeyBrZXk6IGluZm8uaWQsIHN0YXRpY0NsYXNzOiAiaXRlbS14IiB9LCBbCiAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogIml0ZW0iIH0sIFsKICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgImxhYmVsIiwKICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICBfdm0uX3YoIiDlsZ7mgKflkI3np7AgIiksCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImVsLXNlbGVjdCIsCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyB3aWR0aDogImNhbGMoMTAwJSAtIDY1cHgpIiB9LAogICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGNsZWFyYWJsZTogIiIsIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oupIiB9LAogICAgICAgICAgICAgICAgICAgIG1vZGVsOiB7CiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogaW5mby5rZXksCiAgICAgICAgICAgICAgICAgICAgICBjYWxsYmFjazogZnVuY3Rpb24gKCQkdikgewogICAgICAgICAgICAgICAgICAgICAgICBfdm0uJHNldChpbmZvLCAia2V5IiwgJCR2KQogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJpbmZvLmtleSIsCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgX3ZtLl9sKF92bS5maWx0ZXJPcHRpb24oaW5mby5rZXkpLCBmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgIHJldHVybiBfYygiZWwtb3B0aW9uIiwgewogICAgICAgICAgICAgICAgICAgICAga2V5OiBpdGVtLmtleSwKICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGxhYmVsOiBpdGVtLmxhYmVsLCB2YWx1ZTogaXRlbS5rZXkgfSwKICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgIDEKICAgICAgICAgICAgKSwKICAgICAgICAgIF0pLAogICAgICAgICAgX2MoCiAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiaXRlbSIsIHN0YXRpY1N0eWxlOiB7ICJsaW5lLWhlaWdodCI6ICIzMnB4IiB9IH0sCiAgICAgICAgICAgIFsKICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICJsYWJlbCIsCiAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgIF92bS5fdigi6K+36L6T5YWl5YC8ICIpLAogICAgICAgICAgICAgICAgICBfdm0uY2hlY2tUeXBlKGluZm8ua2V5LCAibnVtYmVyIikKICAgICAgICAgICAgICAgICAgICA/IF9jKCJlbC1pbnB1dC1udW1iZXIiLCB7CiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAiY3MtbnVtYmVyLWJ0bi1oaWRkZW4iLAogICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBtaW46IDAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgbW9kZWw6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogaW5mby52YWwsCiAgICAgICAgICAgICAgICAgICAgICAgICAgY2FsbGJhY2s6IGZ1bmN0aW9uICgkJHYpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS4kc2V0KGluZm8sICJ2YWwiLCAkJHYpCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBleHByZXNzaW9uOiAiaW5mby52YWwiLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICBfdm0uY2hlY2tUeXBlKGluZm8ua2V5LCAic3RyaW5nIikKICAgICAgICAgICAgICAgICAgICA/IF9jKCJlbC1pbnB1dCIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgbW9kZWw6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogaW5mby52YWwsCiAgICAgICAgICAgICAgICAgICAgICAgICAgY2FsbGJhY2s6IGZ1bmN0aW9uICgkJHYpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS4kc2V0KGluZm8sICJ2YWwiLCAkJHYpCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBleHByZXNzaW9uOiAiaW5mby52YWwiLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICBfdm0uY2hlY2tUeXBlKGluZm8ua2V5LCAiYXJyYXkiKSAmJiBpbmZvLmtleSA9PT0gIklzX01haW4iCiAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgImVsLXNlbGVjdCIsCiAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBjbGVhcmFibGU6ICIiLCBwbGFjZWhvbGRlcjogIuivt+mAieaLqSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBtb2RlbDogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGluZm8udmFsLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FsbGJhY2s6IGZ1bmN0aW9uICgkJHYpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLiRzZXQoaW5mbywgInZhbCIsICQkdikKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleHByZXNzaW9uOiAiaW5mby52YWwiLAogICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fbChfdm0uSXNfTWFpbl9EYXRhLCBmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfYygiZWwtb3B0aW9uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiBpdGVtLklkLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgbGFiZWw6IGl0ZW0uTmFtZSwgdmFsdWU6IGl0ZW0uTmFtZSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgXQogICAgICAgICAgKSwKICAgICAgICAgIGluZGV4ID09PSAwCiAgICAgICAgICAgID8gX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAiaXRlbS1zcGFuIiB9LCBbCiAgICAgICAgICAgICAgICBfYygiaSIsIHsKICAgICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJlbC1pY29uLWNpcmNsZS1wbHVzLW91dGxpbmUiLAogICAgICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLmhhbmRsZUFkZCB9LAogICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgOiBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJpdGVtLXNwYW4iIH0sIFsKICAgICAgICAgICAgICAgIF9jKCJpIiwgewogICAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogImVsLWljb24tY2lyY2xlLXBsdXMtb3V0bGluZSIsCiAgICAgICAgICAgICAgICAgIG9uOiB7IGNsaWNrOiBfdm0uaGFuZGxlQWRkIH0sCiAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgIF9jKCJpIiwgewogICAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogImVsLWljb24tcmVtb3ZlLW91dGxpbmUgdHh0LXJlZCIsCiAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0uaGFuZGxlRGVsZXRlKGluZGV4KQogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICBdKSwKICAgICAgICBdKQogICAgICB9KSwKICAgICAgX2MoCiAgICAgICAgImRpdiIsCiAgICAgICAgewogICAgICAgICAgc3RhdGljU3R5bGU6IHsKICAgICAgICAgICAgInRleHQtYWxpZ24iOiAicmlnaHQiLAogICAgICAgICAgICB3aWR0aDogIjEwMCUiLAogICAgICAgICAgICBwYWRkaW5nOiAiMjBweCAyJSAwIDAiLAogICAgICAgICAgfSwKICAgICAgICB9LAogICAgICAgIFsKICAgICAgICAgIF9jKAogICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgewogICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLiRlbWl0KCJjbG9zZSIpCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIFtfdm0uX3YoIuWPlua2iCIpXQogICAgICAgICAgKSwKICAgICAgICAgIF9jKAogICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgewogICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJwcmltYXJ5IiwgbG9hZGluZzogX3ZtLmJ0bkxvYWRpbmcgfSwKICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLm9uU3VibWl0IH0sCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIFtfdm0uX3YoIuehruWumiIpXQogICAgICAgICAgKSwKICAgICAgICBdLAogICAgICAgIDEKICAgICAgKSwKICAgIF0sCiAgICAyCiAgKQp9CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbXQpyZW5kZXIuX3dpdGhTdHJpcHBlZCA9IHRydWUKCmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH0="}]}
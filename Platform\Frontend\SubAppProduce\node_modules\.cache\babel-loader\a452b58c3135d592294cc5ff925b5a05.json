{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\draft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\draft.vue", "mtime": 1757468127993}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["closeTagView", "debounce", "BatchProcessAdjust", "GetCanSchdulingPartList", "GetCompSchdulingInfoDetail", "GetDwg", "GetPartSchdulingInfoDetail", "GetSchdulingWorkingTeams", "SaveComponentSchedulingWorkshop", "SaveCompSchdulingDraft", "SavePartSchdulingDraftNew", "SavePartSchedulingWorkshopNew", "SaveSchdulingTaskById", "GetStopList", "AddDraft", "OwnerProcess", "Workshop", "GetGridByCode", "getUnique", "uniqueCode", "v4", "uuidv4", "numeral", "GetLibListType", "GetProcessFlowListWithTechnology", "GetProcessListBase", "AreaGetEntity", "mapActions", "mapGetters", "GetPartTypeList", "moment", "ExpandableSection", "TreeDetail", "GetInstallUnitIdNameList", "GetProjectAreaTreeList", "GetCompTypeTree", "parseOssUrl", "DynamicTableFields", "getConfigure", "baseUrl", "GetSteelCadAndBimId", "GetBOMInfo", "SPLIT_SYMBOL", "components", "data", "drawer", "drawersull", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fullscreenid", "iframeUrl", "fullbimid", "fileBim", "IsUploadCad", "cadRowCode", "cadRowProjectId", "tb<PERSON><PERSON>", "isComponentOptions", "label", "value", "specOptions", "filterTypeOption", "filterCodeOption", "projectOptions", "areaOptions", "installOptions", "projectList", "installList", "areaList", "pickerOptions", "disabledDate", "time", "innerForm", "projectName", "areaName", "installName", "searchContent", "searchComTypeSearch", "searchSpecSearch", "searchDirect", "cur<PERSON><PERSON>ch", "searchType", "formInline", "Schduling_Code", "Create_UserName", "Finish_Date", "InstallUnit_Id", "Remark", "total", "currentIds", "columns", "tbData", "tbConfig", "TotalCount", "multipleSelection", "showExpand", "pgLoading", "deleteLoading", "workShopIsOpen", "isOwnerNull", "dialogVisible", "openAddDraft", "saveLoading", "tbLoading", "isCheckAll", "currentComponent", "gridCode", "dWidth", "title", "search", "pageType", "undefined", "tipLabel", "technologyOption", "typeOption", "workingTeam", "pageStatus", "scheduleId", "partComOwnerColumn", "installUnitIdList", "projectId", "areaId", "statusType", "expandedKey", "treeData", "treeParamsComponentType", "filterable", "clickParent", "props", "children", "treeSelectParams", "placeholder", "collapseTags", "clearable", "disabledAdd", "projectOption", "comName", "partName", "bomList", "watch", "handler", "n", "o", "checkOwner", "<PERSON><PERSON><PERSON><PERSON>", "immediate", "computed", "_objectSpread", "isCom", "<PERSON><PERSON><PERSON><PERSON>", "isEdit", "isAdd", "addDraftKey", "statusCode", "_this", "item", "find", "v", "Id", "Name", "isPartPrepare", "getIsPartPrepare", "isNest", "$route", "query", "type", "hasCraft", "isVersionFour", "hasUnitPart", "created", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "process", "env", "NODE_ENV", "baseCadUrl", "code", "then", "res", "Data", "stop", "mounted", "_this3", "_callee2", "_yield$GetBOMInfo", "list", "_this3$$route$query", "install", "_callee2$", "_context2", "sent", "initProcessList", "tbDataMap", "craftCodeMap", "pg_type", "status", "model", "pid", "localStorage", "getItem", "unique", "checkWorkshopIsOpen", "fetchData", "mergeConfig", "getType", "window", "addEventListener", "frameListener", "$once", "console", "log", "removeEventListener", "activated", "_this4", "methods", "every", "Comp_Import_Detail_Id", "idx", "findIndex", "Code", "splice", "ownerColumn", "$message", "message", "concat", "push", "comPart", "_this5", "_callee3", "_callee3$", "_context3", "getConfig", "getWorkTeam", "_this6", "for<PERSON>ach", "cur", "Project_Name", "includes", "InstallUnit_Name", "Area_Name", "_this7", "_callee4", "configCode", "_callee4$", "_context4", "getTableConfig", "workshopEnabled", "filter", "changeColumn", "_this8", "_callee5", "_callee5$", "_context5", "_this9", "_callee6", "resData", "_callee6$", "_context6", "getPartPageList", "getNestPageList", "initTbData", "fetchTreeDataLocal", "fetchTreeStatus", "<PERSON><PERSON><PERSON><PERSON>", "$store", "tbSelectChange", "array", "records", "getAreaInfo", "_this0", "id", "IsSucceed", "_res$Data", "_res$Data2", "start", "Demand_Begin_Date", "end", "Demand_End_Date", "getTime", "Message", "handleClose", "_this1", "Promise", "resolve", "reject", "Ids", "nestIds", "_list", "map", "Part_Used_Process", "Scheduled_Used_Process", "Part_Type_Used_Process", "Workshop_Id", "Scheduled_Workshop_Id", "Workshop_Name", "Scheduled_Workshop_Name", "Technology_Path", "Scheduled_Technology_Path", "chooseCount", "Can_Schduling_Count", "getComPageList", "_this10", "Schduling_Plan_Id", "_res$Data3", "Schduling_Plan", "Sch<PERSON>ling_Comps", "Process_List", "Object", "assign", "plist", "key", "Process_Code", "changeProcessList", "_this11", "_callee7", "result", "_callee7$", "_context7", "_res$Data4", "_res$Data5", "SarePartsModel", "getStopList", "abrupt", "_this12", "_callee8", "submitObj", "_callee8$", "_context8", "Part_Aggregate_Id", "Type", "stopMap", "Is_Stop", "row", "$set", "_this13", "teamKey", "arguments", "length", "JSON", "parse", "stringify", "_row$Technology_Path", "processList", "split", "uuid", "addElementToTbData", "newData", "r", "p", "ele", "index", "getRowUnique", "Working_Team_Id", "max", "getRowUniqueMax", "Count", "setInputMax", "ids", "toString", "mergeCraftProcess", "_this14", "_callee9", "codes", "_loop", "_craftCodeMap", "_callee9$", "_context0", "_toConsumableArray", "Set", "Technology_Code", "_loop$", "_context9", "hasOwnProperty", "t0", "keys", "t1", "done", "<PERSON><PERSON><PERSON>", "getCraftProcess", "_this15", "gyGroup", "TechnologyCodes", "gyList", "gyMap", "reduce", "acc", "mergeSelectList", "newList", "_this16", "_callee0", "hasUsedPartFlag", "_callee0$", "_context1", "element", "getMergeUniqueRow", "pu<PERSON>", "<PERSON><PERSON><PERSON><PERSON>_Count", "Schduled_Weight", "Weight", "format", "Array", "cur<PERSON><PERSON><PERSON><PERSON>", "join", "add", "sort", "a", "b", "initRowIndex", "timeEnd", "getUniKey", "checkForm", "isValidate", "$refs", "validate", "valid", "saveDraft", "_arguments", "_this17", "_callee1", "_this17$$refs$draft", "isOrder", "checkSuccess", "_this17$getSubmitTbIn", "tableData", "isSuccess", "_callee1$", "_context10", "getSubmitTbInfo", "handleSaveDraft", "saveWorkShop", "_this18", "_callee10", "obj", "_fun", "_callee10$", "_context11", "Project_Id", "Area_Id", "Schduling_Model", "_this19", "_loop2", "i", "msg", "from", "_loop3", "j", "schduledCount", "groups", "Allocation_Teams", "againCount", "Again_Count", "_list2", "apply", "hasInput", "_", "startsWith", "_ret", "_this20", "_callee11", "obj<PERSON><PERSON>", "orderSuccess", "_callee11$", "_context12", "templateScheduleCode", "handleDelete", "_this21", "setTimeout", "selectedUuids", "isSelected", "has", "_this22", "_callee12", "_callee12$", "_context13", "handleSubmit", "_this23", "_this23$getSubmitTbIn", "$confirm", "confirmButtonText", "cancelButtonText", "saveDraftDoSubmit", "catch", "_this24", "_callee13", "_this24$formInline", "_isSuccess", "_callee13$", "_context14", "doSubmit", "scheduleCode", "_this25", "schdulingPlanId", "finally", "getWorkShop", "_this26", "origin", "_value$workShop", "workShop", "Display_Name", "_value$workShop2", "set<PERSON>ath", "_value$workShop3", "handleBatchWorkshop", "_this27", "$nextTick", "getProcessOption", "workshopId", "_this28", "setLibType", "_this29", "Component_type", "info", "workCode", "WorkCode", "replace", "inputChange", "inputValuesKeys", "endsWith", "val", "curCode", "otherTotal", "x", "sendProcess", "_ref", "arr", "str", "originalPath", "resetWorkTeamMax", "_str", "_this30", "some", "k", "$delete", "checkPermissionTeam", "processStr", "processCode", "_this31", "_callee14", "_callee14$", "_context15", "Grid", "ColumnList", "ownerColumn2", "setColumnDisplay", "Is_Display", "activeCellMethod", "_ref2", "_column$field", "column", "columnIndex", "field", "openBPADialog", "_this32", "IsUnique", "checkIsUniqueWorkshop", "setData", "isUnique", "firstV", "checkHasWorkShop", "hasWorkShop", "handleAddDialog", "_this33", "setAddTbKey", "initData", "addToTbList", "_this34", "_callee15", "_callee15$", "_context16", "selectKeys", "changeAddTbKeys", "workingId", "handleSelectMenu", "handleSetCraftProcess", "_this35", "_callee16", "showSuccess", "rowList", "workshopIds", "w_process", "workshopPromise", "_callee16$", "_context17", "_defineProperty", "all", "values", "workshop", "flag", "_loop4", "curRow", "workshopProcess", "craftArray", "isIncluded", "$alert", "handleBatchOwner", "_this36", "setOption", "handleReverse", "checked", "setAllCheckboxRow", "_this37", "getCompTree", "fun", "Label", "_this37$$refs$treeSel", "treeSelectComponentType", "treeDataUpdateFun", "handleDwg", "_this38", "Comp_Id", "Part_Id", "importDetailId", "extensionName", "ExtensionName", "IsUpload", "Part_Code", "Sys_Project_Id", "fileView", "setProcessList", "resetInnerForm", "resetFields", "xTable", "clearFilter", "innerFilter", "_this39", "clearCheckboxRow", "getColumnByField", "filters", "option", "updateData", "filterComponentMethod", "_ref4", "Is_Component", "filterSpecMethod", "_ref5", "trim", "splitAndClean", "input", "specArray", "Spec", "filterTypeMethod", "_ref6", "filterCodeMethod", "_ref7", "filterProjectMethod", "_ref8", "filterAreaMethod", "_ref9", "filterInstallMethod", "_ref0", "componentTypeFilter", "e", "_this$$refs", "filterFun", "getInstallUnitIdNameList", "_this40", "installChange", "_this41", "showPartUsedProcess", "handleExport", "exportData", "filename", "handleCloseDrawer", "renderIframe", "fullscreen", "templateUrl", "_ref1", "error", "iframeId", "_typeof", "document", "getElementById", "contentWindow", "postMessage", "path", "cadId", "steelName", "showCad", "isSubAssembly", "isPart"], "sources": ["src/views/PRO/plan-production/schedule-production-new-part/draft.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 flex-row\">\r\n    <div class=\"cs-right\">\r\n      <el-card v-loading=\"pgLoading\" class=\"box-card h100\" element-loading-text=\"正在处理...\">\r\n        <h4 class=\"topTitle\"><span />基本信息</h4>\r\n        <el-form\r\n          ref=\"formInline\"\r\n          :inline=\"true\"\r\n          :model=\"formInline\"\r\n          class=\"demo-form-inline\"\r\n        >\r\n          <el-form-item v-if=\"!isAdd&&!isNest\" label=\"排产单号\" prop=\"Schduling_Code\">\r\n            <span v-if=\"isView\">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>\r\n            <el-input v-else v-model=\"formInline.Schduling_Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"计划员\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ formInline.Create_UserName }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Create_UserName\"\r\n              disabled\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"要求完成时间\"\r\n            prop=\"Finish_Date\"\r\n            :rules=\"{ required: true, message: '请选择', trigger: 'change' }\"\r\n          >\r\n            <span v-if=\"isView\">{{ formInline.Finish_Date | timeFormat }}</span>\r\n            <el-date-picker\r\n              v-else\r\n              v-model=\"formInline.Finish_Date\"\r\n              :picker-options=\"pickerOptions\"\r\n              :disabled=\"isView\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"date\"\r\n              placeholder=\"选择日期\"\r\n            />\r\n          </el-form-item>\r\n          <!--          <el-form-item v-if=\"!isNest\" label=\"批次\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ installName }}</span>\r\n            <el-select\r\n              v-else\r\n              v-model=\"formInline.InstallUnit_Id\"\r\n              :disabled=\"!isAdd\"\r\n              filterable\r\n              placeholder=\"请选择\"\r\n              @change=\"installChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in installUnitIdList\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>-->\r\n          <el-form-item label=\"备注\" prop=\"Remark\">\r\n            <span v-if=\"isView\">{{ formInline.Remark }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Remark\"\r\n              :disabled=\"isView\"\r\n              style=\"width: 320px\"\r\n              placeholder=\"请输入\"\r\n            />\r\n          </el-form-item>\r\n\r\n        </el-form>\r\n        <el-divider class=\"elDivder\" />\r\n        <div v-if=\"!isView\">\r\n          <div ref=\"searchDom\" class=\"search-container\">\r\n\r\n            <el-form ref=\"searchForm\" :model=\"innerForm\">\r\n              <el-row>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"项目名称\" prop=\"projectName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.projectName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in projectList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"区域\" prop=\"areaName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.areaName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in areaList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"批次\" prop=\"installName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.installName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in installList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"90px\" prop=\"searchContent\" :label=\"`${partName}名称` \">\r\n                    <el-input\r\n                      v-model=\"innerForm.searchContent\"\r\n                      clearable\r\n                      class=\"input-with-select w100\"\r\n                      placeholder=\"请输入(空格区分/多个搜索)\"\r\n                      size=\"small\"\r\n                    >\r\n                      <el-select\r\n                        slot=\"prepend\"\r\n                        v-model=\"curSearch\"\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100px\"\r\n                      >\r\n                        <el-option label=\"精准查询\" :value=\"1\" />\r\n                        <el-option label=\"模糊查询\" :value=\"0\" />\r\n                      </el-select>\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" :label=\"`${partName}类型`\" prop=\"searchComTypeSearch\">\r\n                    <el-tree-select\r\n                      v-if=\"$route.query.status!=='view'\"\r\n                      ref=\"treeSelectComponentType\"\r\n                      v-model=\"innerForm.searchComTypeSearch\"\r\n                      placeholder=\"请选择\"\r\n                      :select-params=\"treeSelectParams\"\r\n                      class=\"cs-tree-x\"\r\n                      :tree-params=\"treeParamsComponentType\"\r\n                      @searchFun=\"componentTypeFilter\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"规格\" prop=\"searchSpecSearch\">\r\n                    <el-input v-model=\"innerForm.searchSpecSearch\" class=\"w100\" placeholder=\"请输入\" clearable=\"\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item v-if=\"isCom\" label-width=\"80px\" label=\"是否直发件\" prop=\"searchDirect\">\r\n                    <el-select v-model=\"innerForm.searchDirect\" class=\"w100\" placeholder=\"请选择\" clearable>\r\n                      <el-option label=\"是\" :value=\"true\" />\r\n                      <el-option label=\"否\" :value=\"false\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"20px\">\r\n                    <el-button type=\"primary\" @click=\"innerFilter\">搜索</el-button>\r\n                    <el-button @click=\"resetInnerForm\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        <vxe-toolbar\r\n          ref=\"xToolbar1\"\r\n        >\r\n          <template #buttons>\r\n            <div v-if=\"!isView\" class=\"btn-x\">\r\n              <el-button v-if=\"!isNest\" type=\"primary\" @click=\"handleAddDialog()\">添加</el-button>\r\n\r\n              <el-button\r\n                v-if=\"workshopEnabled\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchWorkshop(1)\"\r\n              >分配车间\r\n              </el-button>\r\n\r\n              <el-dropdown v-if=\"hasCraft\" style=\"margin:0 10px\" @command=\"handleSelectMenu\">\r\n                <el-button :disabled=\"!multipleSelection.length\" type=\"primary\" plain>\r\n                  分配工序<i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    command=\"process\"\r\n                  >批量分配工序\r\n                  </el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    command=\"craft\"\r\n                  >工艺代码分配\r\n                  </el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <el-button\r\n                v-else\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleSelectMenu('process')\"\r\n              >分配工序\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"!isCom && !isOwnerNull\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchOwner(1)\"\r\n              >批量分配领用工序\r\n              </el-button>\r\n              <el-button\r\n                plain\r\n                :disabled=\"!tbData.length\"\r\n                :loading=\"false\"\r\n                @click=\"handleReverse\"\r\n              >反选\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                plain\r\n                :loading=\"deleteLoading\"\r\n                :disabled=\"!multipleSelection.length || multipleSelection.some(item=>item.stopFlag)\"\r\n                @click=\"handleDelete\"\r\n              >删除\r\n              </el-button>\r\n            </div>\r\n            <div v-else>\r\n              <el-button style=\"margin-bottom: 8px;\" :disabled=\"!tbData.length\" @click=\"handleExport\">导出</el-button>\r\n            </div>\r\n          </template>\r\n          <template #tools>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </template>\r\n        </vxe-toolbar>\r\n        <div class=\"tb-x\">\r\n          <!--          activeMethod: activeCellMethod,-->\r\n          <vxe-table\r\n            ref=\"xTable\"\r\n            :key=\"tbKey\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :checkbox-config=\"{checkField: 'checked'}\"\r\n            class=\"cs-vxe-table\"\r\n            :row-config=\"{isCurrent: true, isHover: true}\"\r\n            align=\"left\"\r\n            height=\"100%\"\r\n            :filter-config=\"{showIcon:false}\"\r\n            show-overflow\r\n            :loading=\"tbLoading\"\r\n            stripe\r\n            :scroll-y=\"{enabled: true, gt: 20}\"\r\n            size=\"medium\"\r\n            :edit-config=\"{\r\n              trigger: 'click',\r\n              mode: 'cell',\r\n              showIcon: !isView,\r\n\r\n            }\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"isComponentOptions\"\r\n                :filter-method=\"filterComponentMethod\"\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag\r\n                    :type=\"row.Is_Component ? 'danger' : 'success'\"\r\n                  >{{ row.Is_Component ? '否' : '是' }}\r\n                  </el-tag>\r\n                </template>\r\n              </vxe-column>\r\n\r\n              <vxe-column\r\n                v-else-if=\"['Type','Type_Name'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filter-method=\"filterTypeMethod\"\r\n                :field=\"item.Code\"\r\n                :filters=\"filterTypeOption\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row[item.Code] | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Comp_Code','Part_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :filter-method=\"filterCodeMethod\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"filterCodeOption\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin: 8px;\" type=\"danger\">变</el-tag>\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <el-link v-if=\"row.DwgCount>0\" type=\"primary\" @click.stop=\"handleDwg(row)\"> {{ row[item.Code] | displayValue }}\r\n                  </el-link>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Spec'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"specOptions\"\r\n                :filter-method=\"filterSpecMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Spec | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Project_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"projectOptions\"\r\n                :filter-method=\"filterProjectMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in projectList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Project_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Area_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"areaOptions\"\r\n                :filter-method=\"filterAreaMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in areaList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Area_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'InstallUnit_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"installOptions\"\r\n                :filter-method=\"filterInstallMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in installList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.InstallUnit_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Weight'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  {{ (row.Schduled_Count * row.Weight).toFixed(2) / 1 }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Technology_Path'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :show-overflow=\"false\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Technology_Path\" placement=\"top\">\r\n                        <span>{{ row.Technology_Path | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"openBPADialog(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Part_Used_Process'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :show-overflow=\"false\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Part_Used_Process\" placement=\"top\">\r\n                        <span>{{ row.Part_Used_Process | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"showPartUsedProcess(row)\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchOwner(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Workshop_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :show-overflow=\"false\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Workshop_Name\" placement=\"top\">\r\n                        <span>{{ row.Workshop_Name | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchWorkshop(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Count'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :edit-render=\"{enabled:!isView}\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #edit=\"{ row }\">\r\n                  <vxe-input\r\n                    v-model.number=\"row.Schduled_Count\"\r\n                    type=\"integer\"\r\n                    min=\"0\"\r\n                    :max=\"row.chooseCount\"\r\n                  />\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Schduled_Count | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Id\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n\r\n          </vxe-table>\r\n        </div>\r\n        <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n        <footer v-if=\"!isView\">\r\n          <div class=\"data-info\">\r\n            <el-tag\r\n              size=\"medium\"\r\n              class=\"info-x\"\r\n            >已选 {{ multipleSelection.length }} 条数据\r\n            </el-tag>\r\n            <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{\r\n              tipLabel\r\n            }}\r\n            </el-tag>\r\n          </div>\r\n          <div>\r\n            <el-button v-if=\"workshopEnabled&&!isNest\" type=\"primary\" :disabled=\"tbData.some(item=>item.stopFlag)\" @click=\"saveWorkShop\">保存车间分配</el-button>\r\n            <el-button\r\n              v-if=\"!isNest\"\r\n              type=\"primary\"\r\n              :disabled=\"tbData.some(item=>item.stopFlag)\"\r\n              :loading=\"saveLoading\"\r\n              @click=\"saveDraft(false)\"\r\n            >保存草稿\r\n            </el-button>\r\n            <el-button :disabled=\"deleteLoading || tbData.some(item=>item.stopFlag)\" @click=\"handleSubmit\">下发任务</el-button>\r\n          </div>\r\n        </footer>\r\n      </el-card>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :is-nest=\"isNest\"\r\n        :part-name=\"partName\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :process-list=\"processList\"\r\n        :page-type=\"pageType\"\r\n        :has-unit-part=\"hasUnitPart\"\r\n        :part-type-option=\"typeOption\"\r\n        @close=\"handleClose\"\r\n        @sendProcess=\"sendProcess\"\r\n        @workShop=\"getWorkShop\"\r\n        @refresh=\"fetchData\"\r\n        @setProcessList=\"setProcessList\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      :key=\"addDraftKey\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"openAddDraft\"\r\n      :width=\"dWidth\"\r\n      top=\"7vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <add-draft\r\n        v-if=\"openAddDraft\"\r\n        ref=\"draft\"\r\n        :com-name=\"comName\"\r\n        :part-name=\"partName\"\r\n        :current-ids=\"currentIds\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :install-id=\"formInline.InstallUnit_Id\"\r\n        :schedule-id=\"scheduleId\"\r\n        :show-dialog=\"openAddDraft\"\r\n        :page-type=\"pageType\"\r\n        @addToTbList=\"addToTbList\"\r\n        @sendSelectList=\"mergeSelectList\"\r\n        @setAddTbKey=\"setAddTbKey\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-drawer\r\n      :visible.sync=\"drawer\"\r\n      direction=\"btt\"\r\n      size=\"60%\"\r\n      destroy-on-close\r\n      :before-close=\"handleCloseDrawer\"\r\n      @opened=\"renderIframe\"\r\n    >\r\n      <div style=\"width: 100%; display: flex\">\r\n        <div style=\"margin-left: 20px\">\r\n          <span style=\"display: inline-block; width: 100px\">{{ partName }}图纸</span>\r\n        </div>\r\n        <el-button\r\n          v-if=\"fileBim\"\r\n          style=\"margin-left: 42%\"\r\n          @click=\"fullscreen(1)\"\r\n        >全屏</el-button>\r\n      </div>\r\n      <iframe\r\n        id=\"frame\"\r\n        :key=\"iframeKey\"\r\n        :src=\"iframeUrl\"\r\n        style=\"width: 100%; border: 0px; margin: 0; height: 60vh\"\r\n      />\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { closeTagView, debounce } from '@/utils'\r\nimport BatchProcessAdjust from './components/BatchProcessAdjust'\r\nimport {\r\n  GetCanSchdulingPartList,\r\n  GetCompSchdulingInfoDetail, GetDwg,\r\n  GetPartSchdulingInfoDetail,\r\n  GetSchdulingWorkingTeams,\r\n  SaveComponentSchedulingWorkshop,\r\n  SaveCompSchdulingDraft,\r\n  SavePartSchdulingDraftNew,\r\n  SavePartSchedulingWorkshopNew,\r\n  SaveSchdulingTaskById\r\n} from '@/api/PRO/production-task'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport AddDraft from './components/addDraft'\r\nimport OwnerProcess from './components/OwnerProcess'\r\nimport Workshop from './components/Workshop.vue'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { getUnique, uniqueCode } from './constant'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { GetLibListType, GetProcessFlowListWithTechnology, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { AreaGetEntity } from '@/api/plm/projects'\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport moment from 'moment'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\n\r\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\n\r\nimport { getConfigure } from '@/api/user'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: { DynamicTableFields, TreeDetail, ExpandableSection, BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },\r\n  data() {\r\n    return {\r\n      drawer: false,\r\n      drawersull: false,\r\n      iframeKey: '',\r\n      fullscreenid: '',\r\n      iframeUrl: '',\r\n      fullbimid: '',\r\n      fileBim: '',\r\n      IsUploadCad: false,\r\n      cadRowCode: '',\r\n      cadRowProjectId: '',\r\n      tbKey: 100,\r\n      isComponentOptions: [\r\n        { label: '是', value: false },\r\n        { label: '否', value: true }\r\n      ],\r\n      specOptions: [{ data: '' }],\r\n      filterTypeOption: [{ data: '' }],\r\n      filterCodeOption: [{ data: '' }],\r\n      projectOptions: [{ data: '' }],\r\n      areaOptions: [{ data: '' }],\r\n      installOptions: [{ data: '' }],\r\n      projectList: [],\r\n      installList: [],\r\n      areaList: [],\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n        }\r\n      },\r\n      innerForm: {\r\n        projectName: '',\r\n        areaName: '',\r\n        installName: '',\r\n        searchContent: '',\r\n        searchComTypeSearch: '',\r\n        searchSpecSearch: '',\r\n        searchDirect: ''\r\n      },\r\n      curSearch: 1,\r\n      searchType: '',\r\n      formInline: {\r\n        Schduling_Code: '',\r\n        Create_UserName: '',\r\n        Finish_Date: '',\r\n        InstallUnit_Id: '',\r\n        Remark: ''\r\n      },\r\n      total: 0,\r\n      currentIds: '',\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      multipleSelection: [],\r\n      showExpand: true,\r\n      pgLoading: false,\r\n      deleteLoading: false,\r\n      workShopIsOpen: false,\r\n      isOwnerNull: false,\r\n      dialogVisible: false,\r\n      openAddDraft: false,\r\n      saveLoading: false,\r\n      tbLoading: false,\r\n      isCheckAll: false,\r\n      currentComponent: '',\r\n      gridCode: '',\r\n      dWidth: '25%',\r\n      title: '',\r\n      search: () => ({}),\r\n      pageType: undefined,\r\n      tipLabel: '',\r\n      technologyOption: [],\r\n      typeOption: [],\r\n      workingTeam: [],\r\n      pageStatus: undefined,\r\n      scheduleId: '',\r\n      partComOwnerColumn: null,\r\n\r\n      installUnitIdList: [],\r\n      projectId: '',\r\n      areaId: '',\r\n      projectName: '',\r\n      statusType: '',\r\n      expandedKey: '',\r\n      // treeLoading: false,\r\n      treeData: [],\r\n      treeParamsComponentType: {\r\n        'default-expand-all': true,\r\n        'check-strictly': true,\r\n        filterable: true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        collapseTags: true,\r\n        clearable: true\r\n      },\r\n      disabledAdd: true,\r\n      projectOption: [],\r\n      comName: '',\r\n      partName: '',\r\n      bomList: []\r\n    }\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler(n, o) {\r\n        this.checkOwner()\r\n        this.doFilter()\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    isView() {\r\n      return this.pageStatus === 'view'\r\n    },\r\n    isEdit() {\r\n      return this.pageStatus === 'edit'\r\n    },\r\n    isAdd() {\r\n      return this.pageStatus === 'add'\r\n    },\r\n    addDraftKey() {\r\n      return this.expandedKey + this.formInline.InstallUnit_Id\r\n    },\r\n    // filterText() {\r\n    //   return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    // },\r\n    statusCode() {\r\n      return this.isCom ? 'Comp_Schdule_Status' : 'Part_Schdule_Status'\r\n    },\r\n    installName() {\r\n      const item = this.installUnitIdList.find(v => v.Id === this.formInline.InstallUnit_Id)\r\n      if (item) {\r\n        return item.Name\r\n      } else {\r\n        return ''\r\n      }\r\n    },\r\n    isPartPrepare() {\r\n      return this.getIsPartPrepare && !this.isCom\r\n    },\r\n    isNest() {\r\n      return this.$route.query.type === '1'\r\n    },\r\n    hasCraft() {\r\n      return !!this.isVersionFour\r\n    },\r\n    hasUnitPart() {\r\n      return !!this.isVersionFour\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    ...mapGetters('factoryInfo', ['workshopEnabled', 'getIsPartPrepare']),\r\n    ...mapGetters('schedule', ['processList', 'nestIds'])\r\n  },\r\n  async created() {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      // this.baseCadUrl = 'http://localhost:9529'\r\n      // this.baseCadUrl = 'http://glendale-model.bimtk.com'\r\n      this.baseCadUrl = 'http://glendale-model-dev.bimtk.tech'\r\n    } else {\r\n      getConfigure({ code: 'glendale_url' }).then((res) => {\r\n        this.baseCadUrl = res.Data\r\n      })\r\n    }\r\n  },\r\n  async mounted() {\r\n    const { list, partName, comName } = await GetBOMInfo(0)\r\n    this.bomList = list || []\r\n    this.partName = partName\r\n    this.comName = comName\r\n    this.initProcessList()\r\n    this.tbDataMap = {}\r\n    this.craftCodeMap = {}\r\n    this.pageType = this.$route.query.pg_type\r\n    this.pageStatus = this.$route.query.status\r\n    this.model = this.$route.query.model\r\n    this.scheduleId = this.$route.query.pid || ''\r\n    // // this.formInline.Create_UserName = this.$store.getters.name\r\n    // // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage\r\n    this.formInline.Create_UserName = localStorage.getItem('UserAccount')\r\n    // if (!this.isCom) {\r\n    //   this.getPartType()\r\n    // } else {\r\n    // }\r\n\r\n    this.unique = uniqueCode()\r\n    this.checkWorkshopIsOpen()\r\n\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.mergeConfig()\r\n    if (this.isView || this.isEdit) {\r\n      const { areaId, install } = this.$route.query\r\n      // this.areaId = areaId\r\n      // this.formInline.InstallUnit_Id = install\r\n      // this.getInstallUnitIdNameList()\r\n      this.fetchData()\r\n    }\r\n\r\n    if (this.isAdd) {\r\n      // this.fetchTreeData()\r\n      this.getType()\r\n    }\r\n    if (this.isEdit) {\r\n      this.getType()\r\n    }\r\n\r\n    window.addEventListener('message', this.frameListener)\r\n    this.$once('hook:beforeDestroy', () => {\r\n      console.log('deactivated')\r\n      window.removeEventListener('message', this.frameListener)\r\n    })\r\n  },\r\n  activated() {\r\n    window.addEventListener('message', this.frameListener)\r\n    this.$once('hook:deactivated', () => {\r\n      window.removeEventListener('message', this.frameListener)\r\n    })\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['changeProcessList', 'initProcessList', 'changeAddTbKeys']),\r\n    checkOwner() {\r\n      if (this.isCom) return\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id) && !this.isNest\r\n      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')\r\n      if (this.isOwnerNull) {\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      } else {\r\n        if (idx === -1) {\r\n          if (!this.ownerColumn) {\r\n            this.$message({\r\n              message: `列表配置字段缺少${this.partName}领用工序字段`,\r\n              type: 'success'\r\n            })\r\n            return\r\n          }\r\n          this.columns.push(this.ownerColumn)\r\n        }\r\n        this.comPart = true\r\n      }\r\n    },\r\n    async mergeConfig() {\r\n      await this.getConfig()\r\n      await this.getWorkTeam()\r\n    },\r\n    doFilter() {\r\n      this.projectList = []\r\n      this.installList = []\r\n      this.areaList = []\r\n      this.tbData.forEach(cur => {\r\n        if (cur.Project_Name && !this.projectList.includes(cur.Project_Name)) {\r\n          this.projectList.push(cur.Project_Name)\r\n        }\r\n        if (cur.InstallUnit_Name && !this.installList.includes(cur.InstallUnit_Name)) {\r\n          this.installList.push(cur.InstallUnit_Name)\r\n        }\r\n        if (cur.Area_Name && !this.areaList.includes(cur.Area_Name)) {\r\n          this.areaList.push(cur.Area_Name)\r\n        }\r\n      })\r\n    },\r\n    async getConfig() {\r\n      let configCode = ''\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          configCode = 'PRONestingScheduleDetail'\r\n        } else {\r\n          configCode = 'PRONestingScheduleConfig'\r\n        }\r\n      } else {\r\n        configCode = this.isCom\r\n          ? (this.isView ? 'PROComViewPageTbConfig' : 'PROComDraftPageTbConfig')\r\n          : (this.isView ? 'PROPartViewPageTbConfig_new' : 'PROPartDraftPageTbConfig_new')\r\n      }\r\n      this.gridCode = configCode\r\n      await this.getTableConfig(configCode)\r\n      if (!this.workshopEnabled) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')\r\n      }\r\n      if (!this.hasCraft) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Technology_Code')\r\n      }\r\n      this.checkOwner()\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n      this.tbKey++\r\n    },\r\n    /*    handleNodeClick(data) {\r\n      console.log('data', data)\r\n      if (this.areaId === data.Id) {\r\n        return\r\n      }\r\n      this.\r\n       = true\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      const initData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n        this.formInline.Finish_Date = ''\r\n        this.formInline.InstallUnit_Id = ''\r\n        this.formInline.Remark = ''\r\n        this.tbData = []\r\n        this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      if (this.tbData.length) {\r\n        this.$confirm('切换区域右侧数据清空，是否确认?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          initData(data)\r\n          this.disabledAdd = false\r\n          this.tbDataMap = {}\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      } else {\r\n        this.disabledAdd = false\r\n        initData(data)\r\n      }\r\n    },*/\r\n\r\n    /* customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },*/\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      let resData = []\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          resData = await this.getPartPageList()\r\n        } else {\r\n          resData = await this.getNestPageList()\r\n        }\r\n      } else {\r\n        resData = await this.getPartPageList()\r\n      }\r\n\r\n      this.initTbData(resData)\r\n      this.tbLoading = false\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    fetchTreeStatus() {\r\n      // this.filterText = this.statusType\r\n    },\r\n    /*    fetchTreeData() {\r\n      this.treeLoading = true\r\n      GetProjectAreaTreeList({ projectName: this.projectName, type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data.map(item => {\r\n          item.Is_Directory = true\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        this.setKey()\r\n        this.treeLoading = false\r\n      })\r\n    },*/\r\n    // setKey() {\r\n    //   const deepFilter = (tree) => {\r\n    //     for (let i = 0; i < tree.length; i++) {\r\n    //       const item = tree[i]\r\n    //       const { Data, Children } = item\r\n    //       console.log(Data)\r\n    //       if (Data.ParentId && !Children?.length) {\r\n    //         this.handleNodeClick(item)\r\n    //         return\r\n    //       } else {\r\n    //         if (Children && Children.length > 0) {\r\n    //           return deepFilter(Children)\r\n    //         }\r\n    //       }\r\n    //     }\r\n    //   }\r\n    //   return deepFilter(this.treeData)\r\n    // },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    checkWorkshopIsOpen() {\r\n      this.workShopIsOpen = true\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    getAreaInfo() {\r\n      this.formInline.Finish_Date = ''\r\n      AreaGetEntity({\r\n        id: this.areaId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            return []\r\n          }\r\n\r\n          const start = moment(res.Data?.Demand_Begin_Date)\r\n          const end = moment(res.Data?.Demand_End_Date)\r\n          this.pickerOptions.disabledDate = (time) => {\r\n            return time.getTime() < start || time.getTime() > end\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.openAddDraft = false\r\n    },\r\n    getNestPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        GetCanSchdulingPartList({\r\n          Ids: this.nestIds\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const _list = res?.Data || []\r\n            const list = _list.map(v => {\r\n              v.Part_Used_Process = v.Scheduled_Used_Process || v.Part_Type_Used_Process\r\n              // v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n              v.Workshop_Id = v.Scheduled_Workshop_Id\r\n              v.Workshop_Name = v.Scheduled_Workshop_Name\r\n              v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n              v.chooseCount = v.Can_Schduling_Count\r\n\r\n              return v\r\n            })\r\n\r\n            resolve(list)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getComPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        const {\r\n          pid\r\n        } = this.$route.query\r\n        GetCompSchdulingInfoDetail({\r\n          Schduling_Plan_Id: pid\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const { Schduling_Plan, Schduling_Comps, Process_List } = res.Data\r\n            this.formInline = Object.assign(this.formInline, Schduling_Plan)\r\n            Process_List.forEach(item => {\r\n              const plist = {\r\n                key: item.Process_Code,\r\n                value: item\r\n              }\r\n              this.changeProcessList(plist)\r\n            })\r\n            const list = Schduling_Comps.map(v => {\r\n              v.chooseCount = v.Can_Schduling_Count\r\n              return v\r\n            })\r\n            resolve(list || [])\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async getPartPageList() {\r\n      const {\r\n        pid\r\n      } = this.$route.query\r\n      const result = await GetPartSchdulingInfoDetail({\r\n        Schduling_Plan_Id: pid\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const SarePartsModel = res.Data?.SarePartsModel.map(v => {\r\n            if (v.Scheduled_Used_Process) {\r\n              // 已存在操作过数据\r\n              v.Part_Used_Process = v.Scheduled_Used_Process\r\n            }\r\n            v.chooseCount = v.Can_Schduling_Count\r\n            return v\r\n          })\r\n          this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)\r\n          this.getStopList(SarePartsModel)\r\n          return SarePartsModel || []\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      return result || []\r\n    },\r\n    async getStopList(list) {\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 1\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      console.log(5, JSON.parse(JSON.stringify(list)))\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        row.uuid = uuidv4()\r\n        this.addElementToTbData(row)\r\n        if (row[teamKey]) {\r\n          const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n          newData.forEach((ele, index) => {\r\n            const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            row[code] = ele.Count\r\n            row[max] = 0\r\n          })\r\n        }\r\n        this.setInputMax(row)\r\n        return row\r\n      })\r\n      let ids = ''\r\n      if (this.isCom) {\r\n        ids = this.tbData.map(v => v.Comp_Import_Detail_Id).toString()\r\n      } else {\r\n        ids = this.tbData.map(v => v.Part_Aggregate_Id).toString()\r\n      }\r\n      this.currentIds = ids\r\n    },\r\n    async mergeCraftProcess(list) {\r\n      let codes = [...new Set(list.map(v => v.Technology_Code))]\r\n      for (const key in this.craftCodeMap) {\r\n        if (this.craftCodeMap.hasOwnProperty(key)) {\r\n          codes = codes.filter(code => code !== key)\r\n        }\r\n      }\r\n      const _craftCodeMap = await this.getCraftProcess(codes)\r\n      Object.assign(this.craftCodeMap, _craftCodeMap)\r\n    },\r\n    getCraftProcess(gyGroup = []) {\r\n      gyGroup = gyGroup.filter(v => !!v)\r\n      if (!gyGroup.length) return Promise.resolve({})\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessFlowListWithTechnology({\r\n          TechnologyCodes: gyGroup,\r\n          Type: 2\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const gyList = res.Data || []\r\n            const gyMap = gyList.reduce((acc, item) => {\r\n              acc[item.Code] = item.Technology_Path\r\n              return acc\r\n            }, {})\r\n            console.log('gyMap', gyMap)\r\n            resolve(gyMap)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async mergeSelectList(newList) {\r\n      if (this.hasCraft) {\r\n        await this.mergeCraftProcess(newList)\r\n      }\r\n      console.time('mergeSelectListTime')\r\n      let hasUsedPartFlag = true\r\n\r\n      newList.forEach((element, index) => {\r\n        const cur = this.getMergeUniqueRow(element)\r\n\r\n        if (!cur) {\r\n          console.log('element', element)\r\n          element.puuid = element.uuid\r\n          element.Schduled_Count = element.chooseCount\r\n          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')\r\n\r\n          if (this.hasCraft && !element.Technology_Path) {\r\n            if (this.craftCodeMap[element.Technology_Code] && this.craftCodeMap[element.Technology_Code] instanceof Array) {\r\n              const curPathArr = this.craftCodeMap[element.Technology_Code]\r\n              if (element.Part_Used_Process && !curPathArr.includes(element.Part_Used_Process)) {\r\n                hasUsedPartFlag = false\r\n              } else {\r\n                element.Technology_Path = curPathArr.join('/')\r\n              }\r\n            }\r\n          }\r\n\r\n          this.tbData.push(element)\r\n          this.addElementToTbData(element)\r\n          return\r\n        }\r\n\r\n        cur.puuid = element.uuid\r\n\r\n        cur.Schduled_Count += element.chooseCount\r\n        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')\r\n        if (!cur.Technology_Path) {\r\n          return\r\n        }\r\n        this.setInputMax(cur)\r\n      })\r\n\r\n      // if (this.isCom) {\r\n      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      // } else {\r\n      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))\r\n      // }\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.timeEnd('fff')\r\n      console.log('this.tbDataMap', this.tbDataMap, this.tbData)\r\n    },\r\n    addElementToTbData(element) {\r\n      const key = this.getUniKey(element)\r\n      this.tbDataMap[key] = element\r\n    },\r\n    getMergeUniqueRow(element) {\r\n      const key = this.getUniKey(element)\r\n      return this.tbDataMap[key]\r\n    },\r\n    getUniKey(element) {\r\n      return getUnique(this.isCom, element)\r\n    },\r\n    checkForm() {\r\n      let isValidate = true\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) isValidate = false\r\n      })\r\n      return isValidate\r\n    },\r\n    async saveDraft(isOrder = false) {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return false\r\n      if (!isOrder) {\r\n        this.saveLoading = true\r\n      }\r\n\r\n      const isSuccess = await this.handleSaveDraft(tableData, isOrder)\r\n      console.log('isSuccess', isSuccess)\r\n      if (!isSuccess) return false\r\n      if (isOrder) return isSuccess\r\n      this.$refs['draft']?.fetchData()\r\n      this.saveLoading = false\r\n    },\r\n    async saveWorkShop() {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const obj = {}\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'success'\r\n        })\r\n        return\r\n      }\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = this.tbData\r\n      } else {\r\n        obj.SarePartsModel = this.tbData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      this.pgLoading = true\r\n      const _fun = this.isCom ? SaveComponentSchedulingWorkshop : SavePartSchedulingWorkshopNew\r\n      _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.pgLoading = false\r\n        }\r\n      })\r\n    },\r\n    getSubmitTbInfo() {\r\n      // 处理上传的数据\r\n      let tableData = JSON.parse(JSON.stringify(this.tbData))\r\n      tableData = tableData.filter(item => item.Schduled_Count > 0)\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        let list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (this.isPartPrepare && !element.Part_Used_Process && element.Type !== 'Direct' && this.comPart) {\r\n          const msg = '领用工序不能为空'\r\n          if (this.isNest) {\r\n            if (element.Comp_Import_Detail_Id) {\r\n              this.$message({\r\n                message: msg,\r\n                type: 'warning'\r\n              })\r\n              return { status: false }\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: msg,\r\n              type: 'warning'\r\n            })\r\n            return { status: false }\r\n          }\r\n        }\r\n        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {\r\n        //   // 零构件 零件单独排产\r\n        //   this.$message({\r\n        //     message: '零件领用工序不能为空',\r\n        //     type: 'warning'\r\n        //   })\r\n        //   return { status: false }\r\n        // }\r\n        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.partName}保持工序一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.partName}领用工序保持一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        // processList.forEach(code => {\r\n        // const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n        // const groupsList = groups.map(group => {\r\n        //   const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n        //   const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n        //   const obj = {\r\n        //     Team_Task_Id: element.Team_Task_Id,\r\n        //     Comp_Code: element.Comp_Code,\r\n        //     Again_Count: +element[uCode] || 0, // 不填，后台让传0\r\n        //     Part_Code: this.isCom ? null : '',\r\n        //     Process_Code: code,\r\n        //     Technology_Path: element.Technology_Path,\r\n        //     Working_Team_Id: group.Working_Team_Id,\r\n        //     Working_Team_Name: group.Working_Team_Name\r\n        //   }\r\n        //   delete element[uCode]\r\n        //   delete element[uMax]\r\n        //   return obj\r\n        // })\r\n        // list.push(...groupsList)\r\n        // })\r\n        for (let j = 0; j < processList.length; j++) {\r\n          const code = processList[j]\r\n          const schduledCount = element.Schduled_Count || 0\r\n          let groups = []\r\n          if (element.Allocation_Teams) {\r\n            groups = element.Allocation_Teams.filter(v => v.Process_Code === code)\r\n          }\r\n          const againCount = groups.reduce((acc, cur) => {\r\n            return acc + (cur.Again_Count || 0)\r\n          }, 0)\r\n          if (againCount > schduledCount) {\r\n            list = []\r\n            break\r\n          } else {\r\n            list.push(...groups)\r\n          }\r\n        }\r\n        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))\r\n        hasInput.forEach((item) => {\r\n          delete element[item]\r\n        })\r\n        delete element['uuid']\r\n        delete element['_X_ROW_KEY']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    async handleSaveDraft(tableData, isOrder) {\r\n      console.log('保存草稿')\r\n      const _fun = this.isCom ? SaveCompSchdulingDraft : SavePartSchdulingDraftNew\r\n      const obj = {}\r\n      // if (this.isCom) {\r\n      // obj.Schduling_Comps = tableData\r\n      const p = []\r\n      for (const objKey in this.processList) {\r\n        if (this.processList.hasOwnProperty(objKey)) {\r\n          p.push(this.processList[objKey])\r\n        }\r\n      }\r\n      obj.Process_List = p\r\n      // } else {\r\n      obj.SarePartsModel = tableData\r\n      // }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      let orderSuccess = false\r\n      console.log('obj', obj)\r\n\r\n      await _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!isOrder) {\r\n            this.pgLoading = false\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.templateScheduleCode = res.Data\r\n            orderSuccess = true\r\n            console.log('保存草稿成功 ')\r\n          }\r\n        } else {\r\n          this.saveLoading = false\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log('结束 ')\r\n      return orderSuccess\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))\r\n        this.tbData = this.tbData.filter(item => {\r\n          const isSelected = selectedUuids.has(item.uuid)\r\n          if (isSelected) {\r\n            const key = this.getUniKey(item)\r\n            delete this.tbDataMap[key]\r\n          }\r\n          return !isSelected\r\n        })\r\n        // this.$nextTick(_ => {\r\n        //   const _list = this.multipleSelection.filter(v => v.puuid)\r\n        //   this.$refs['draft']?.mergeData(_list)\r\n        //   this.multipleSelection = []\r\n        // })\r\n        this.deleteLoading = false\r\n      }, 0)\r\n    },\r\n    async getWorkTeam() {\r\n      await GetSchdulingWorkingTeams({\r\n        type: this.isCom ? 1 : 2\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.workingTeam = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) return\r\n        const { tableData, status } = this.getSubmitTbInfo()\r\n        if (!status) return\r\n        this.$confirm('是否提交当前数据?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.saveDraftDoSubmit(tableData)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      })\r\n    },\r\n    async saveDraftDoSubmit() {\r\n      this.pgLoading = true\r\n      if (this.formInline?.Schduling_Code) {\r\n        const isSuccess = await this.saveDraft(true)\r\n        console.log('saveDraftDoSubmit', isSuccess)\r\n        isSuccess && this.doSubmit(this.formInline.Id)\r\n      } else {\r\n        const isSuccess = await this.saveDraft(true)\r\n        isSuccess && this.doSubmit(this.templateScheduleCode)\r\n      }\r\n    },\r\n    doSubmit(scheduleCode) {\r\n      SaveSchdulingTaskById({\r\n        schdulingPlanId: scheduleCode\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '下达成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.pgLoading = false\r\n      }).catch(_ => {\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    getWorkShop(value) {\r\n      console.log('value', value)\r\n      const {\r\n        origin,\r\n        row,\r\n        workShop: {\r\n          Id,\r\n          Display_Name\r\n        }\r\n      } = value\r\n      if (origin === 2) {\r\n        if (value.workShop?.Id) {\r\n          row.Workshop_Name = Display_Name\r\n          row.Workshop_Id = Id\r\n          this.setPath(row, Id)\r\n        } else {\r\n          row.Workshop_Name = ''\r\n          row.Workshop_Id = ''\r\n        }\r\n      } else {\r\n        this.multipleSelection.forEach(item => {\r\n          if (value.workShop?.Id) {\r\n            item.Workshop_Name = Display_Name\r\n            item.Workshop_Id = Id\r\n            this.setPath(item, Id)\r\n          } else {\r\n            item.Workshop_Name = ''\r\n            item.Workshop_Id = ''\r\n          }\r\n        })\r\n      }\r\n    },\r\n    setPath(row, Id) {\r\n      if (row?.Scheduled_Workshop_Id) {\r\n        if (row.Scheduled_Workshop_Id !== Id) {\r\n          row.Technology_Path = ''\r\n        }\r\n      } else {\r\n        row.Technology_Path = ''\r\n      }\r\n    },\r\n    handleBatchWorkshop(origin, row) {\r\n      this.title = origin === 1 ? '批量分配车间' : '分配车间'\r\n      this.currentComponent = 'Workshop'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].fetchData(origin, row)\r\n      })\r\n    },\r\n\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const process = res.Data.map(v => v.Code)\r\n            resolve(process)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    setLibType(code, workshopId) {\r\n      return new Promise((resolve) => {\r\n        const obj = {\r\n          Component_type: code,\r\n          type: 1\r\n        }\r\n        if (this.workshopEnabled) {\r\n          obj.workshopId = workshopId\r\n        }\r\n        GetLibListType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            if (res.Data.Data && res.Data.Data.length) {\r\n              const info = res.Data.Data[0]\r\n              const workCode = info.WorkCode && info.WorkCode.replace(/\\\\/g, '/')\r\n              resolve(workCode)\r\n            } else {\r\n              resolve(undefined)\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n      })\r\n    },\r\n    sendProcess({ arr, str }) {\r\n      let isSuccess = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (item.originalPath && item.originalPath !== str) {\r\n          isSuccess = false\r\n          break\r\n        }\r\n        item.Technology_Path = str\r\n      }\r\n      if (!isSuccess) {\r\n        this.$message({\r\n          message: '请和该区域批次下已排产同构件保持工序一致',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    resetWorkTeamMax(row, str) {\r\n      if (str) {\r\n        row.Technology_Path = str\r\n      } else {\r\n        str = row.Technology_Path\r\n      }\r\n      const list = str?.split('/') || []\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const cur = list.some(k => k === element.Process_Code)\r\n        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        if (cur) {\r\n          if (!row[code]) {\r\n            this.$set(row, code, 0)\r\n            this.$set(row, max, row.Schduled_Count)\r\n          }\r\n        } else {\r\n          this.$delete(row, code)\r\n          this.$delete(row, max)\r\n        }\r\n      })\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')\r\n          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')\r\n          this.columns = this.setColumnDisplay(list)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setColumnDisplay(list) {\r\n      return list.filter(v => v.Is_Display)\r\n      // .map(item => {\r\n      //   if (FIX_COLUMN.includes(item.Code)) {\r\n      //     item.fixed = 'left'\r\n      //   }\r\n      //   return item\r\n      // })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    openBPADialog(type, row) {\r\n      if (this.workshopEnabled) {\r\n        if (type === 1) {\r\n          const IsUnique = this.checkIsUniqueWorkshop()\r\n          if (!IsUnique) return\r\n        }\r\n      }\r\n      this.title = type === 2 ? '工序调整' : '批量工序调整'\r\n      this.currentComponent = 'BatchProcessAdjust'\r\n      this.dWidth = '50%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')\r\n      })\r\n    },\r\n    checkIsUniqueWorkshop() {\r\n      let isUnique = true\r\n      const firstV = this.multipleSelection[0].Workshop_Name\r\n      for (let i = 1; i < this.multipleSelection.length; i++) {\r\n        const item = this.multipleSelection[i]\r\n        if (item.Workshop_Name !== firstV) {\r\n          isUnique = false\r\n          break\r\n        }\r\n      }\r\n      if (!isUnique) {\r\n        this.$message({\r\n          message: '批量分配工序时只有相同车间下的才可一起批量分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return isUnique\r\n    },\r\n    checkHasWorkShop(type, arr) {\r\n      let hasWorkShop = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (!item.Workshop_Name) {\r\n          hasWorkShop = false\r\n          break\r\n        }\r\n      }\r\n      if (!hasWorkShop) {\r\n        this.$message({\r\n          message: '请先选择车间后再进行工序分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return hasWorkShop\r\n    },\r\n    handleAddDialog(type = 'add') {\r\n      this.title = `添加${this.partName}`\r\n\r\n      this.currentComponent = 'AddDraft'\r\n      this.dWidth = '96%'\r\n      this.openAddDraft = true\r\n\r\n      this.setAddTbKey()\r\n\r\n      this.$nextTick(_ => {\r\n        this.$refs['draft'].initData()\r\n      })\r\n    },\r\n    async addToTbList(newList) {\r\n      await this.mergeSelectList(newList)\r\n      this.setAddTbKey()\r\n    },\r\n    setAddTbKey() {\r\n      const selectKeys = this.tbData.filter(cur => cur.puuid).map(v => v.puuid)\r\n      this.changeAddTbKeys(selectKeys)\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    handleSelectMenu(v) {\r\n      if (v === 'process') {\r\n        this.openBPADialog(1)\r\n      } else if (v === 'craft') {\r\n        this.handleSetCraftProcess()\r\n      }\r\n    },\r\n    async handleSetCraftProcess() {\r\n      const showSuccess = () => {\r\n        this.$message({\r\n          message: '已分配成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n      const rowList = this.multipleSelection.map(v => v.Technology_Code).filter(v => !!v)\r\n      if (!rowList.length) {\r\n        this.$message({\r\n          message: '工艺代码不存在',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      await this.mergeCraftProcess(this.multipleSelection)\r\n      const workshopIds = Array.from(new Set(this.multipleSelection.map(v => v.Workshop_Id).filter(v => !!v)))\r\n      const w_process = []\r\n      if (workshopIds.length) {\r\n        workshopIds.forEach(workshopId => {\r\n          w_process.push(this.getProcessOption(workshopId).then(result => ({\r\n            [workshopId]: result\r\n          })))\r\n        })\r\n        const workshopPromise = Promise.all(w_process).then((values) => {\r\n          return Object.assign({}, ...values)\r\n        })\r\n        workshopPromise.then(workshop => {\r\n          let flag = true\r\n          for (let i = 0; i < this.multipleSelection.length; i++) {\r\n            const curRow = this.multipleSelection[i]\r\n            const workshopProcess = workshop[curRow.Workshop_Id]\r\n            const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n            if (craftArray) {\r\n              const isIncluded = craftArray.every(process => workshopProcess.includes(process))\r\n              if (!isIncluded) {\r\n                flag = false\r\n                continue\r\n              }\r\n              curRow.Technology_Path = craftArray.join('/')\r\n            }\r\n          }\r\n          if (!flag) {\r\n            setTimeout(() => {\r\n              this.$alert('所选车间下班组加工工序不包含工艺代码工序请手动排产', '提示', {\r\n                confirmButtonText: '确定'\r\n              })\r\n            }, 200)\r\n          }\r\n\r\n          flag && showSuccess()\r\n        })\r\n      } else {\r\n        this.multipleSelection.forEach((curRow) => {\r\n          const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n          if (craftArray) {\r\n            curRow.Technology_Path = craftArray.join('/')\r\n          }\r\n        })\r\n        showSuccess()\r\n      }\r\n    },\r\n\r\n    handleBatchOwner(type, row) {\r\n      this.title = `${type === 1 ? '批量' : ''}分配领用工序`\r\n      this.currentComponent = 'OwnerProcess'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)\r\n      })\r\n    },\r\n    handleReverse() {\r\n      const cur = []\r\n      this.tbData.forEach((element, idx) => {\r\n        element.checked = !element.checked\r\n        if (element.checked) {\r\n          cur.push(element)\r\n        }\r\n      })\r\n      this.multipleSelection = cur\r\n      if (this.multipleSelection.length === this.tbData.length) {\r\n        this.$refs['xTable'].setAllCheckboxRow(true)\r\n      }\r\n      if (this.multipleSelection.length === 0) {\r\n        this.$refs['xTable'].setAllCheckboxRow(false)\r\n      }\r\n    },\r\n    // tbFilterChange() {\r\n    //   const xTable = this.$refs.xTable\r\n    //   const column = xTable.getColumnByField('Type_Name')\r\n    //   if (!column?.filters?.length) return\r\n    //   column.filters.forEach(d => {\r\n    //     d.checked = d.value === this.searchType\r\n    //   })\r\n    //   xTable.updateData()\r\n    // },\r\n    getType() {\r\n      const getCompTree = () => {\r\n        const fun = this.isCom ? GetCompTypeTree : GetPartTypeList\r\n        fun({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            let result = res.Data\r\n            if (!this.isCom) {\r\n              result = result\r\n                .map((v, idx) => {\r\n                  return {\r\n                    Data: v.Name,\r\n                    Label: v.Name\r\n                  }\r\n                })\r\n            }\r\n            this.treeParamsComponentType.data = result\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectComponentType?.treeDataUpdateFun(result)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      getCompTree()\r\n    },\r\n    // 查看图纸\r\n    handleDwg(row) {\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Comp_Id = row.Comp_Import_Detail_Id\r\n      } else {\r\n        obj.Part_Id = row.Part_Aggregate_Id\r\n      }\r\n\r\n      GetSteelCadAndBimId({ importDetailId: obj.Part_Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.extensionName = res.Data[0].ExtensionName\r\n          this.fileBim = res.Data[0].fileBim\r\n          this.IsUploadCad = res.Data[0].IsUpload\r\n          this.cadRowCode = row.Part_Code\r\n          this.cadRowProjectId = row.Sys_Project_Id\r\n          this.fileView()\r\n        }\r\n      })\r\n      // GetDwg(obj).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     const fileurl = res?.Data?.length && res.Data[0].File_Url\r\n      //     window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl), '_blank')\r\n      //   } else {\r\n      //     this.$message({\r\n      //       message: res.Message,\r\n      //       type: 'error'\r\n      //     })\r\n      //   }\r\n      // })\r\n    },\r\n    setProcessList(info) {\r\n      this.changeProcessList(info)\r\n    },\r\n    resetInnerForm() {\r\n      this.$refs['searchForm'].resetFields()\r\n      this.$refs.xTable.clearFilter()\r\n    },\r\n    innerFilter() {\r\n      this.multipleSelection = []\r\n      const arr = []\r\n      if (this.isCom) {\r\n        arr.push('Type', 'Comp_Code', 'Spec', 'Is_Component')\r\n      } else {\r\n        arr.push('Part_Code', 'Spec', 'Type_Name', 'Project_Name', 'Area_Name', 'InstallUnit_Name')\r\n      }\r\n\r\n      const xTable = this.$refs.xTable\r\n      xTable.clearCheckboxRow()\r\n      arr.forEach((element, idx) => {\r\n        const column = xTable.getColumnByField(element)\r\n        if (element === 'Is_Component') {\r\n          column.filters.forEach((option, idx) => {\r\n            option.checked = idx === (this.innerForm.searchDirect ? 0 : 1)\r\n          })\r\n        }\r\n        if (element === 'Spec') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchSpecSearch\r\n          option.checked = true\r\n        }\r\n        if (element === 'Type' || element === 'Type_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchComTypeSearch\r\n          option.checked = true\r\n        }\r\n        if (element === 'Comp_Code' || element === 'Part_Code') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchContent\r\n          option.checked = true\r\n        }\r\n        if (element === 'Project_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.projectName\r\n          option.checked = true\r\n        }\r\n        if (element === 'Area_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.areaName\r\n          option.checked = true\r\n        }\r\n        if (element === 'InstallUnit_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.installName\r\n          option.checked = true\r\n        }\r\n      })\r\n      xTable.updateData()\r\n    },\r\n    filterComponentMethod({ option, row }) {\r\n      if (this.innerForm.searchDirect === '') {\r\n        return true\r\n      }\r\n      return row.Is_Component === !this.innerForm.searchDirect\r\n    },\r\n    filterSpecMethod({ option, row }) {\r\n      if (this.innerForm.searchSpecSearch.trim() === '') {\r\n        return true\r\n      }\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n      const specArray = splitAndClean(this.innerForm.searchSpecSearch)\r\n      return specArray.some(code => (row.Spec || '').includes(code))\r\n    },\r\n\r\n    filterTypeMethod({ option, row }) {\r\n      if (this.innerForm.searchComTypeSearch === '') {\r\n        return true\r\n      }\r\n      const cur = this.isCom ? 'Type' : 'Type_Name'\r\n      return row[cur] === this.innerForm.searchComTypeSearch\r\n    },\r\n    filterCodeMethod({ option, row }) {\r\n      if (this.innerForm.searchContent.trim() === '') {\r\n        return true\r\n      }\r\n\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      const cur = this.isCom ? 'Comp_Code' : 'Part_Code'\r\n\r\n      const arr = splitAndClean(this.innerForm.searchContent)\r\n\r\n      if (this.curSearch === 1) {\r\n        return arr.some(code => row[cur] === code)\r\n      } else {\r\n        for (let i = 0; i < arr.length; i++) {\r\n          const item = arr[i]\r\n          if (row[cur].includes(item)) {\r\n            return true\r\n          }\r\n        }\r\n        return false\r\n      }\r\n    },\r\n    filterProjectMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.Project_Name === option.data\r\n    },\r\n    filterAreaMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.Area_Name === option.data\r\n    },\r\n    filterInstallMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.InstallUnit_Name === option.data\r\n    },\r\n    componentTypeFilter(e) {\r\n      this.$refs?.treeSelectComponentType.filterFun(e)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n        this.disabledAdd = false\r\n      } else {\r\n        this.disabledAdd = true\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data\r\n          if (this.installUnitIdList.length) {\r\n            this.formInline.InstallUnit_Id = this.installUnitIdList[0].Id\r\n          }\r\n          this.disabledAdd = false\r\n        })\r\n      }\r\n    },\r\n    installChange() {\r\n      if (!this.tbData.length) {\r\n        this.$refs['searchForm'].resetFields()\r\n        this.$refs.xTable.clearFilter()\r\n        return\r\n      }\r\n      this.$confirm('切换区域右侧数据清空, 是否确认?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbData = []\r\n        this.resetInnerForm()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    showPartUsedProcess(row) {\r\n      if (this.isNest) {\r\n        return !!row.Comp_Import_Detail_Id\r\n      } else {\r\n        return !this.isView && row.Type !== 'Direct'\r\n      }\r\n    },\r\n    handleExport() {\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '暂无数据',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$refs.xTable.exportData({\r\n        filename: `${this.partName}排产-${this.formInline.Schduling_Code}(${this.partName})`,\r\n        type: 'xlsx',\r\n        data: this.tbData\r\n      })\r\n    },\r\n    handleCloseDrawer() {\r\n      this.drawer = false\r\n    },\r\n    fileView() {\r\n      this.iframeKey = uuidv4()\r\n      this.iframeUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=11&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.drawer = true\r\n    },\r\n    renderIframe() {\r\n      const ExtensionName = this.extensionName\r\n      const fileBim = this.fileBim\r\n      this.iframeUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=11&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.fullscreenid = ExtensionName\r\n      this.fullbimid = fileBim\r\n    },\r\n    fullscreen(v) {\r\n      this.templateUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=13&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.drawersull = true\r\n    },\r\n    frameListener({ data }) {\r\n      if (data.type === 'loaded') {\r\n        console.log('data', data)\r\n        console.error(\r\n          'data.data.iframeId',\r\n          data.data.iframeId,\r\n          typeof data.data.iframeId\r\n        )\r\n        if (data.data.iframeId === '11') {\r\n          document.getElementById('frame').contentWindow.postMessage(\r\n            {\r\n              type: 'router',\r\n              path: '/modelCad',\r\n              query: {\r\n                // baseUrl: baseUrl(),\r\n                cadId: this.fileBim,\r\n                projectId: this.cadRowProjectId,\r\n                steelName: this.cadRowCode,\r\n                showCad: this.IsUploadCad,\r\n                isSubAssembly: false,\r\n                isPart: true\r\n                // cadId: this.fileBim\r\n                // token: localStorage.getItem('Token'),\r\n                // auth_id: localStorage.getItem('Last_Working_Object_Id')\r\n              }\r\n            },\r\n            '*'\r\n          )\r\n        } else if (data.data.iframeId === '13') {\r\n          document.getElementById('fullFrame').contentWindow.postMessage(\r\n            {\r\n              type: 'router',\r\n              path: '/modelCad',\r\n              query: {\r\n                // baseUrl: baseUrl(),\r\n                cadId: this.fileBim,\r\n                projectId: this.cadRowProjectId,\r\n                steelName: this.cadRowCode,\r\n                showCad: this.IsUploadCad,\r\n                isSubAssembly: false,\r\n                isPart: true\r\n                // token: localStorage.getItem('Token'),\r\n                // auth_id: localStorage.getItem('Last_Working_Object_Id')\r\n              }\r\n            },\r\n            '*'\r\n          )\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.flex-row {\r\n  display: flex;\r\n\r\n  .cs-left {\r\n    background-color: #ffffff;\r\n    margin-right: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n    .cs-tree-wrapper {\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      overflow: hidden;\r\n      padding: 16px;\r\n\r\n      .tree-search {\r\n        display: flex;\r\n\r\n        .search-select {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .el-tree {\r\n        flex: 1;\r\n        overflow: auto;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.btn-x {\r\n  //margin-bottom: 16px;\r\n\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.demo-form-inline {\r\n  ::v-deep {\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-column-row{\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .cs-ell{\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4rBA,SAAAA,YAAA,EAAAC,QAAA;AACA,OAAAC,kBAAA;AACA,SACAC,uBAAA,EACAC,0BAAA,EAAAC,MAAA,EACAC,0BAAA,EACAC,wBAAA,EACAC,+BAAA,EACAC,sBAAA,EACAC,yBAAA,EACAC,6BAAA,EACAC,qBAAA,QACA;AACA,SAAAC,WAAA;AACA,OAAAC,QAAA;AACA,OAAAC,YAAA;AACA,OAAAC,QAAA;AACA,SAAAC,aAAA;AACA,SAAAC,SAAA,EAAAC,UAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,OAAA;AACA,SAAAC,cAAA,EAAAC,gCAAA,EAAAC,kBAAA;AACA,SAAAC,aAAA;AACA,SAAAC,UAAA,EAAAC,UAAA;AACA,SAAAC,eAAA;AACA,OAAAC,MAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,UAAA;AACA,SAAAC,wBAAA,EAAAC,sBAAA;AAEA,SAAAC,eAAA;AACA,SAAAC,WAAA;AACA,OAAAC,kBAAA;AAEA,SAAAC,YAAA;AACA,SAAAC,OAAA;AACA,SAAAC,mBAAA;AACA,SAAAC,UAAA;AACA,IAAAC,YAAA;AACA;EACAC,UAAA;IAAAN,kBAAA,EAAAA,kBAAA;IAAAL,UAAA,EAAAA,UAAA;IAAAD,iBAAA,EAAAA,iBAAA;IAAA7B,kBAAA,EAAAA,kBAAA;IAAAY,QAAA,EAAAA,QAAA;IAAAE,QAAA,EAAAA,QAAA;IAAAD,YAAA,EAAAA;EAAA;EACA6B,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;MACAC,YAAA;MACAC,SAAA;MACAC,SAAA;MACAC,OAAA;MACAC,WAAA;MACAC,UAAA;MACAC,eAAA;MACAC,KAAA;MACAC,kBAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,WAAA;QAAAf,IAAA;MAAA;MACAgB,gBAAA;QAAAhB,IAAA;MAAA;MACAiB,gBAAA;QAAAjB,IAAA;MAAA;MACAkB,cAAA;QAAAlB,IAAA;MAAA;MACAmB,WAAA;QAAAnB,IAAA;MAAA;MACAoB,cAAA;QAAApB,IAAA;MAAA;MACAqB,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA,GACA;MACA;MACAC,SAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,YAAA;MACA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;QACAC,cAAA;QACAC,eAAA;QACAC,WAAA;QACAC,cAAA;QACAC,MAAA;MACA;MACAC,KAAA;MACAC,UAAA;MACAC,OAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,SAAA;MACAC,aAAA;MACAC,cAAA;MACAC,WAAA;MACAC,aAAA;MACAC,YAAA;MACAC,WAAA;MACAC,SAAA;MACAC,UAAA;MACAC,gBAAA;MACAC,QAAA;MACAC,MAAA;MACAC,KAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,QAAA,EAAAC,SAAA;MACAC,QAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA,EAAAL,SAAA;MACAM,UAAA;MACAC,kBAAA;MAEAC,iBAAA;MACAC,SAAA;MACAC,MAAA;MACAhD,WAAA;MACAiD,UAAA;MACAC,WAAA;MACA;MACAC,QAAA;MACAC,uBAAA;QACA;QACA;QACAC,UAAA;QACAC,WAAA;QACAlF,IAAA;QACAmF,KAAA;UACAC,QAAA;UACAvE,KAAA;UACAC,KAAA;QACA;MACA;MACAuE,gBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,SAAA;MACA;MACAC,WAAA;MACAC,aAAA;MACAC,OAAA;MACAC,QAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,OAAA,WAAAA,QAAAC,CAAA,EAAAC,CAAA;QACA,KAAAC,UAAA;QACA,KAAAC,QAAA;MACA;MACAC,SAAA;IACA;EACA;EAEAC,QAAA,EAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAtC,QAAA;IACA;IACAuC,MAAA,WAAAA,OAAA;MACA,YAAAjC,UAAA;IACA;IACAkC,MAAA,WAAAA,OAAA;MACA,YAAAlC,UAAA;IACA;IACAmC,KAAA,WAAAA,MAAA;MACA,YAAAnC,UAAA;IACA;IACAoC,WAAA,WAAAA,YAAA;MACA,YAAA7B,WAAA,QAAAzC,UAAA,CAAAI,cAAA;IACA;IACA;IACA;IACA;IACAmE,UAAA,WAAAA,WAAA;MACA,YAAAL,KAAA;IACA;IACAzE,WAAA,WAAAA,YAAA;MAAA,IAAA+E,KAAA;MACA,IAAAC,IAAA,QAAApC,iBAAA,CAAAqC,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAJ,KAAA,CAAAxE,UAAA,CAAAI,cAAA;MAAA;MACA,IAAAqE,IAAA;QACA,OAAAA,IAAA,CAAAI,IAAA;MACA;QACA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAAC,gBAAA,UAAAb,KAAA;IACA;IACAc,MAAA,WAAAA,OAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,cAAAC,aAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,cAAAD,aAAA;IACA;EAAA,GACA1I,UAAA,gCACAA,UAAA,2DACAA,UAAA,yCACA;EACA4I,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACA,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;cACA;cACA;cACAZ,MAAA,CAAAa,UAAA;YACA;cACAhJ,YAAA;gBAAAiJ,IAAA;cAAA,GAAAC,IAAA,WAAAC,GAAA;gBACAhB,MAAA,CAAAa,UAAA,GAAAG,GAAA,CAAAC,IAAA;cACA;YACA;UAAA;UAAA;YAAA,OAAAV,QAAA,CAAAW,IAAA;QAAA;MAAA,GAAAd,OAAA;IAAA;EACA;EACAe,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAnB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkB,SAAA;MAAA,IAAAC,iBAAA,EAAAC,IAAA,EAAAxD,QAAA,EAAAD,OAAA,EAAA0D,mBAAA,EAAAzE,MAAA,EAAA0E,OAAA;MAAA,OAAAvB,mBAAA,GAAAG,IAAA,UAAAqB,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAnB,IAAA,GAAAmB,SAAA,CAAAlB,IAAA;UAAA;YAAAkB,SAAA,CAAAlB,IAAA;YAAA,OACAzI,UAAA;UAAA;YAAAsJ,iBAAA,GAAAK,SAAA,CAAAC,IAAA;YAAAL,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YAAAxD,QAAA,GAAAuD,iBAAA,CAAAvD,QAAA;YAAAD,OAAA,GAAAwD,iBAAA,CAAAxD,OAAA;YACAsD,MAAA,CAAApD,OAAA,GAAAuD,IAAA;YACAH,MAAA,CAAArD,QAAA,GAAAA,QAAA;YACAqD,MAAA,CAAAtD,OAAA,GAAAA,OAAA;YACAsD,MAAA,CAAAS,eAAA;YACAT,MAAA,CAAAU,SAAA;YACAV,MAAA,CAAAW,YAAA;YACAX,MAAA,CAAAhF,QAAA,GAAAgF,MAAA,CAAA3B,MAAA,CAAAC,KAAA,CAAAsC,OAAA;YACAZ,MAAA,CAAA1E,UAAA,GAAA0E,MAAA,CAAA3B,MAAA,CAAAC,KAAA,CAAAuC,MAAA;YACAb,MAAA,CAAAc,KAAA,GAAAd,MAAA,CAAA3B,MAAA,CAAAC,KAAA,CAAAwC,KAAA;YACAd,MAAA,CAAAzE,UAAA,GAAAyE,MAAA,CAAA3B,MAAA,CAAAC,KAAA,CAAAyC,GAAA;YACA;YACA;YACAf,MAAA,CAAA5G,UAAA,CAAAE,eAAA,GAAA0H,YAAA,CAAAC,OAAA;YACA;YACA;YACA;YACA;;YAEAjB,MAAA,CAAAkB,MAAA,GAAA5L,UAAA;YACA0K,MAAA,CAAAmB,mBAAA;YAEAnB,MAAA,CAAAjF,MAAA,GAAA3G,QAAA,CAAA4L,MAAA,CAAAoB,SAAA;YAAAb,SAAA,CAAAlB,IAAA;YAAA,OACAW,MAAA,CAAAqB,WAAA;UAAA;YACA,IAAArB,MAAA,CAAAzC,MAAA,IAAAyC,MAAA,CAAAxC,MAAA;cAAA4C,mBAAA,GACAJ,MAAA,CAAA3B,MAAA,CAAAC,KAAA,EAAA3C,MAAA,GAAAyE,mBAAA,CAAAzE,MAAA,EAAA0E,OAAA,GAAAD,mBAAA,CAAAC,OAAA,EACA;cACA;cACA;cACAL,MAAA,CAAAoB,SAAA;YACA;YAEA,IAAApB,MAAA,CAAAvC,KAAA;cACA;cACAuC,MAAA,CAAAsB,OAAA;YACA;YACA,IAAAtB,MAAA,CAAAxC,MAAA;cACAwC,MAAA,CAAAsB,OAAA;YACA;YAEAC,MAAA,CAAAC,gBAAA,YAAAxB,MAAA,CAAAyB,aAAA;YACAzB,MAAA,CAAA0B,KAAA;cACAC,OAAA,CAAAC,GAAA;cACAL,MAAA,CAAAM,mBAAA,YAAA7B,MAAA,CAAAyB,aAAA;YACA;UAAA;UAAA;YAAA,OAAAlB,SAAA,CAAAT,IAAA;QAAA;MAAA,GAAAG,QAAA;IAAA;EACA;EACA6B,SAAA,WAAAA,UAAA;IAAA,IAAAC,MAAA;IACAR,MAAA,CAAAC,gBAAA,iBAAAC,aAAA;IACA,KAAAC,KAAA;MACAH,MAAA,CAAAM,mBAAA,YAAAE,MAAA,CAAAN,aAAA;IACA;EACA;EACAO,OAAA,EAAA3E,aAAA,CAAAA,aAAA,KACAvH,UAAA;IACAmH,UAAA,WAAAA,WAAA;MACA,SAAAK,KAAA;MACA,KAAAjD,WAAA,QAAAR,MAAA,CAAAoI,KAAA,WAAAlE,CAAA;QAAA,QAAAA,CAAA,CAAAmE,qBAAA;MAAA,YAAA9D,MAAA;MACA,IAAA+D,GAAA,QAAAvI,OAAA,CAAAwI,SAAA,WAAArE,CAAA;QAAA,OAAAA,CAAA,CAAAsE,IAAA;MAAA;MACA,SAAAhI,WAAA;QACA8H,GAAA,gBAAAvI,OAAA,CAAA0I,MAAA,CAAAH,GAAA;MACA;QACA,IAAAA,GAAA;UACA,UAAAI,WAAA;YACA,KAAAC,QAAA;cACAC,OAAA,qDAAAC,MAAA,MAAA/F,QAAA;cACA4B,IAAA;YACA;YACA;UACA;UACA,KAAA3E,OAAA,CAAA+I,IAAA,MAAAJ,WAAA;QACA;QACA,KAAAK,OAAA;MACA;IACA;IACAvB,WAAA,WAAAA,YAAA;MAAA,IAAAwB,MAAA;MAAA,OAAAhE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+D,SAAA;QAAA,OAAAhE,mBAAA,GAAAG,IAAA,UAAA8D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAA3D,IAAA;YAAA;cAAA2D,SAAA,CAAA3D,IAAA;cAAA,OACAwD,MAAA,CAAAI,SAAA;YAAA;cAAAD,SAAA,CAAA3D,IAAA;cAAA,OACAwD,MAAA,CAAAK,WAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAlD,IAAA;UAAA;QAAA,GAAAgD,QAAA;MAAA;IACA;IACA5F,QAAA,WAAAA,SAAA;MAAA,IAAAiG,MAAA;MACA,KAAA/K,WAAA;MACA,KAAAC,WAAA;MACA,KAAAC,QAAA;MACA,KAAAuB,MAAA,CAAAuJ,OAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,YAAA,KAAAH,MAAA,CAAA/K,WAAA,CAAAmL,QAAA,CAAAF,GAAA,CAAAC,YAAA;UACAH,MAAA,CAAA/K,WAAA,CAAAuK,IAAA,CAAAU,GAAA,CAAAC,YAAA;QACA;QACA,IAAAD,GAAA,CAAAG,gBAAA,KAAAL,MAAA,CAAA9K,WAAA,CAAAkL,QAAA,CAAAF,GAAA,CAAAG,gBAAA;UACAL,MAAA,CAAA9K,WAAA,CAAAsK,IAAA,CAAAU,GAAA,CAAAG,gBAAA;QACA;QACA,IAAAH,GAAA,CAAAI,SAAA,KAAAN,MAAA,CAAA7K,QAAA,CAAAiL,QAAA,CAAAF,GAAA,CAAAI,SAAA;UACAN,MAAA,CAAA7K,QAAA,CAAAqK,IAAA,CAAAU,GAAA,CAAAI,SAAA;QACA;MACA;IACA;IACAR,SAAA,WAAAA,UAAA;MAAA,IAAAS,MAAA;MAAA,OAAA7E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA4E,SAAA;QAAA,IAAAC,UAAA;QAAA,OAAA9E,mBAAA,GAAAG,IAAA,UAAA4E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1E,IAAA,GAAA0E,SAAA,CAAAzE,IAAA;YAAA;cACAuE,UAAA;cACA,IAAAF,MAAA,CAAAtF,MAAA;gBACA,IAAAsF,MAAA,CAAAnG,MAAA;kBACAqG,UAAA;gBACA;kBACAA,UAAA;gBACA;cACA;gBACAA,UAAA,GAAAF,MAAA,CAAApG,KAAA,GACAoG,MAAA,CAAAnG,MAAA,0DACAmG,MAAA,CAAAnG,MAAA;cACA;cACAmG,MAAA,CAAA9I,QAAA,GAAAgJ,UAAA;cAAAE,SAAA,CAAAzE,IAAA;cAAA,OACAqE,MAAA,CAAAK,cAAA,CAAAH,UAAA;YAAA;cACA,KAAAF,MAAA,CAAAM,eAAA;gBACAN,MAAA,CAAA9J,OAAA,GAAA8J,MAAA,CAAA9J,OAAA,CAAAqK,MAAA,WAAAlG,CAAA;kBAAA,OAAAA,CAAA,CAAAsE,IAAA;gBAAA;cACA;cACA,KAAAqB,MAAA,CAAAlF,QAAA;gBACAkF,MAAA,CAAA9J,OAAA,GAAA8J,MAAA,CAAA9J,OAAA,CAAAqK,MAAA,WAAAlG,CAAA;kBAAA,OAAAA,CAAA,CAAAsE,IAAA;gBAAA;cACA;cACAqB,MAAA,CAAAzG,UAAA;YAAA;YAAA;cAAA,OAAA6G,SAAA,CAAAhE,IAAA;UAAA;QAAA,GAAA6D,QAAA;MAAA;IACA;IACAO,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAAtF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqF,SAAA;QAAA,OAAAtF,mBAAA,GAAAG,IAAA,UAAAoF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlF,IAAA,GAAAkF,SAAA,CAAAjF,IAAA;YAAA;cAAAiF,SAAA,CAAAjF,IAAA;cAAA,OACA8E,MAAA,CAAAJ,cAAA,CAAAI,MAAA,CAAAvJ,QAAA;YAAA;cACAuJ,MAAA,CAAAzM,KAAA;YAAA;YAAA;cAAA,OAAA4M,SAAA,CAAAxE,IAAA;UAAA;QAAA,GAAAsE,QAAA;MAAA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAhD,SAAA,WAAAA,UAAA;MAAA,IAAAmD,MAAA;MAAA,OAAA1F,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyF,SAAA;QAAA,IAAAC,OAAA;QAAA,OAAA3F,mBAAA,GAAAG,IAAA,UAAAyF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvF,IAAA,GAAAuF,SAAA,CAAAtF,IAAA;YAAA;cACAkF,MAAA,CAAA9J,SAAA;cACAgK,OAAA;cAAA,KACAF,MAAA,CAAAnG,MAAA;gBAAAuG,SAAA,CAAAtF,IAAA;gBAAA;cAAA;cAAA,KACAkF,MAAA,CAAAhH,MAAA;gBAAAoH,SAAA,CAAAtF,IAAA;gBAAA;cAAA;cAAAsF,SAAA,CAAAtF,IAAA;cAAA,OACAkF,MAAA,CAAAK,eAAA;YAAA;cAAAH,OAAA,GAAAE,SAAA,CAAAnE,IAAA;cAAAmE,SAAA,CAAAtF,IAAA;cAAA;YAAA;cAAAsF,SAAA,CAAAtF,IAAA;cAAA,OAEAkF,MAAA,CAAAM,eAAA;YAAA;cAAAJ,OAAA,GAAAE,SAAA,CAAAnE,IAAA;YAAA;cAAAmE,SAAA,CAAAtF,IAAA;cAAA;YAAA;cAAAsF,SAAA,CAAAtF,IAAA;cAAA,OAGAkF,MAAA,CAAAK,eAAA;YAAA;cAAAH,OAAA,GAAAE,SAAA,CAAAnE,IAAA;YAAA;cAGA+D,MAAA,CAAAO,UAAA,CAAAL,OAAA;cACAF,MAAA,CAAA9J,SAAA;YAAA;YAAA;cAAA,OAAAkK,SAAA,CAAA7E,IAAA;UAAA;QAAA,GAAA0E,QAAA;MAAA;IACA;IACAO,kBAAA,WAAAA,mBAAA;MACA;IAAA,CACA;IACAC,eAAA,WAAAA,gBAAA;MACA;IAAA,CACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA9Q,YAAA,MAAA+Q,MAAA,OAAA7G,MAAA;IACA;IACA8C,mBAAA,WAAAA,oBAAA;MACA,KAAA/G,cAAA;IACA;IACA+K,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAApL,iBAAA,GAAAoL,KAAA,CAAAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAnM,UAAA,CAAAG,WAAA;MACA1D,aAAA;QACA2P,EAAA,OAAA7J;MACA,GAAAgE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA6F,SAAA;UAAA,IAAAC,SAAA,EAAAC,UAAA;UACA,KAAA/F,GAAA,CAAAC,IAAA;YACA;UACA;UAEA,IAAA+F,KAAA,GAAA3P,MAAA,EAAAyP,SAAA,GAAA9F,GAAA,CAAAC,IAAA,cAAA6F,SAAA,uBAAAA,SAAA,CAAAG,iBAAA;UACA,IAAAC,GAAA,GAAA7P,MAAA,EAAA0P,UAAA,GAAA/F,GAAA,CAAAC,IAAA,cAAA8F,UAAA,uBAAAA,UAAA,CAAAI,eAAA;UACAR,MAAA,CAAAhN,aAAA,CAAAC,YAAA,aAAAC,IAAA;YACA,OAAAA,IAAA,CAAAuN,OAAA,KAAAJ,KAAA,IAAAnN,IAAA,CAAAuN,OAAA,KAAAF,GAAA;UACA;QACA;UACAP,MAAA,CAAA/C,QAAA;YACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;YACA1H,IAAA;UACA;QACA;MACA;IACA;IACA2H,WAAA,WAAAA,YAAA;MACA,KAAA5L,aAAA;MACA,KAAAC,YAAA;IACA;IACAsK,eAAA,WAAAA,gBAAA;MAAA,IAAAsB,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAhS,uBAAA;UACAiS,GAAA,EAAAJ,MAAA,CAAAK;QACA,GAAA7G,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA6F,SAAA;YACA,IAAAgB,KAAA,IAAA7G,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAAC,IAAA;YACA,IAAAM,IAAA,GAAAsG,KAAA,CAAAC,GAAA,WAAA3I,CAAA;cACAA,CAAA,CAAA4I,iBAAA,GAAA5I,CAAA,CAAA6I,sBAAA,IAAA7I,CAAA,CAAA8I,sBAAA;cACA;cACA9I,CAAA,CAAA+I,WAAA,GAAA/I,CAAA,CAAAgJ,qBAAA;cACAhJ,CAAA,CAAAiJ,aAAA,GAAAjJ,CAAA,CAAAkJ,uBAAA;cACAlJ,CAAA,CAAAmJ,eAAA,GAAAnJ,CAAA,CAAAoJ,yBAAA,IAAApJ,CAAA,CAAAmJ,eAAA;cACAnJ,CAAA,CAAAqJ,WAAA,GAAArJ,CAAA,CAAAsJ,mBAAA;cAEA,OAAAtJ,CAAA;YACA;YAEAsI,OAAA,CAAAlG,IAAA;UACA;YACAgG,MAAA,CAAA3D,QAAA;cACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;cACA1H,IAAA;YACA;YACA+H,MAAA;UACA;QACA;MACA;IACA;IACAgB,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,WAAAnB,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA,IACAvF,GAAA,GACAwG,OAAA,CAAAlJ,MAAA,CAAAC,KAAA,CADAyC,GAAA;QAEAxM,0BAAA;UACAiT,iBAAA,EAAAzG;QACA,GAAApB,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA6F,SAAA;YACA,IAAAgC,UAAA,GAAA7H,GAAA,CAAAC,IAAA;cAAA6H,cAAA,GAAAD,UAAA,CAAAC,cAAA;cAAAC,eAAA,GAAAF,UAAA,CAAAE,eAAA;cAAAC,YAAA,GAAAH,UAAA,CAAAG,YAAA;YACAL,OAAA,CAAAnO,UAAA,GAAAyO,MAAA,CAAAC,MAAA,CAAAP,OAAA,CAAAnO,UAAA,EAAAsO,cAAA;YACAE,YAAA,CAAAxE,OAAA,WAAAvF,IAAA;cACA,IAAAkK,KAAA;gBACAC,GAAA,EAAAnK,IAAA,CAAAoK,YAAA;gBACApQ,KAAA,EAAAgG;cACA;cACA0J,OAAA,CAAAW,iBAAA,CAAAH,KAAA;YACA;YACA,IAAA5H,IAAA,GAAAwH,eAAA,CAAAjB,GAAA,WAAA3I,CAAA;cACAA,CAAA,CAAAqJ,WAAA,GAAArJ,CAAA,CAAAsJ,mBAAA;cACA,OAAAtJ,CAAA;YACA;YACAsI,OAAA,CAAAlG,IAAA;UACA;YACAoH,OAAA,CAAA/E,QAAA;cACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;cACA1H,IAAA;YACA;YACA+H,MAAA;UACA;QACA;MACA;IACA;IACA1B,eAAA,WAAAA,gBAAA;MAAA,IAAAuD,OAAA;MAAA,OAAAtJ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqJ,SAAA;QAAA,IAAArH,GAAA,EAAAsH,MAAA;QAAA,OAAAvJ,mBAAA,GAAAG,IAAA,UAAAqJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnJ,IAAA,GAAAmJ,SAAA,CAAAlJ,IAAA;YAAA;cAEA0B,GAAA,GACAoH,OAAA,CAAA9J,MAAA,CAAAC,KAAA,CADAyC,GAAA;cAAAwH,SAAA,CAAAlJ,IAAA;cAAA,OAEA5K,0BAAA;gBACA+S,iBAAA,EAAAzG;cACA,GAAApB,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA6F,SAAA;kBAAA,IAAA+C,UAAA,EAAAC,UAAA;kBACA,IAAAC,cAAA,IAAAF,UAAA,GAAA5I,GAAA,CAAAC,IAAA,cAAA2I,UAAA,uBAAAA,UAAA,CAAAE,cAAA,CAAAhC,GAAA,WAAA3I,CAAA;oBACA,IAAAA,CAAA,CAAA6I,sBAAA;sBACA;sBACA7I,CAAA,CAAA4I,iBAAA,GAAA5I,CAAA,CAAA6I,sBAAA;oBACA;oBACA7I,CAAA,CAAAqJ,WAAA,GAAArJ,CAAA,CAAAsJ,mBAAA;oBACA,OAAAtJ,CAAA;kBACA;kBACAoK,OAAA,CAAA/O,UAAA,GAAAyO,MAAA,CAAAC,MAAA,CAAAK,OAAA,CAAA/O,UAAA,GAAAqP,UAAA,GAAA7I,GAAA,CAAAC,IAAA,cAAA4I,UAAA,uBAAAA,UAAA,CAAAf,cAAA;kBACAS,OAAA,CAAAQ,WAAA,CAAAD,cAAA;kBACA,OAAAA,cAAA;gBACA;kBACAP,OAAA,CAAA3F,QAAA;oBACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;oBACA1H,IAAA;kBACA;gBACA;cACA;YAAA;cArBA8J,MAAA,GAAAE,SAAA,CAAA/H,IAAA;cAAA,OAAA+H,SAAA,CAAAK,MAAA,WAsBAP,MAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAAzI,IAAA;UAAA;QAAA,GAAAsI,QAAA;MAAA;IACA;IACAO,WAAA,WAAAA,YAAAxI,IAAA;MAAA,IAAA0I,OAAA;MAAA,OAAAhK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+J,SAAA;QAAA,IAAAC,SAAA;QAAA,OAAAjK,mBAAA,GAAAG,IAAA,UAAA+J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7J,IAAA,GAAA6J,SAAA,CAAA5J,IAAA;YAAA;cACA0J,SAAA,GAAA5I,IAAA,CAAAuG,GAAA,WAAA7I,IAAA;gBACA;kBACAG,EAAA,EAAAH,IAAA,CAAAqL,iBAAA;kBACAC,IAAA;gBACA;cACA;cAAAF,SAAA,CAAA5J,IAAA;cAAA,OACArK,WAAA,CAAA+T,SAAA,EAAApJ,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA6F,SAAA;kBACA,IAAA2D,OAAA;kBACAxJ,GAAA,CAAAC,IAAA,CAAAuD,OAAA,WAAAvF,IAAA;oBACAuL,OAAA,CAAAvL,IAAA,CAAAG,EAAA,MAAAH,IAAA,CAAAwL,OAAA;kBACA;kBACAlJ,IAAA,CAAAiD,OAAA,WAAAkG,GAAA;oBACA,IAAAF,OAAA,CAAAE,GAAA,CAAAJ,iBAAA;sBACAL,OAAA,CAAAU,IAAA,CAAAD,GAAA,cAAAF,OAAA,CAAAE,GAAA,CAAAJ,iBAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAnJ,IAAA;UAAA;QAAA,GAAAgJ,QAAA;MAAA;IACA;IACAhE,UAAA,WAAAA,WAAA3E,IAAA;MAAA,IAAAqJ,OAAA;MAAA,IAAAC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzO,SAAA,GAAAyO,SAAA;MACA/H,OAAA,CAAAC,GAAA,IAAAgI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA3J,IAAA;MACA,KAAAtG,MAAA,GAAAsG,IAAA,CAAAuG,GAAA,WAAA4C,GAAA;QAAA,IAAAS,oBAAA;QACA,IAAAC,WAAA,KAAAD,oBAAA,GAAAT,GAAA,CAAApC,eAAA,cAAA6C,oBAAA,uBAAAA,oBAAA,CAAAE,KAAA;QACAX,GAAA,CAAAY,IAAA,GAAA1U,MAAA;QACAgU,OAAA,CAAAW,kBAAA,CAAAb,GAAA;QACA,IAAAA,GAAA,CAAAG,OAAA;UACA,IAAAW,OAAA,GAAAd,GAAA,CAAAG,OAAA,EAAAxF,MAAA,WAAAoG,CAAA;YAAA,OAAAL,WAAA,CAAA5H,SAAA,WAAAkI,CAAA;cAAA,OAAAD,CAAA,CAAApC,YAAA,KAAAqC,CAAA;YAAA;UAAA;UACAF,OAAA,CAAAhH,OAAA,WAAAmH,GAAA,EAAAC,KAAA;YACA,IAAA9K,IAAA,GAAA8J,OAAA,CAAAiB,YAAA,CAAAnB,GAAA,CAAAY,IAAA,EAAAK,GAAA,CAAAtC,YAAA,EAAAsC,GAAA,CAAAG,eAAA;YACA,IAAAC,GAAA,GAAAnB,OAAA,CAAAoB,eAAA,CAAAtB,GAAA,CAAAY,IAAA,EAAAK,GAAA,CAAAtC,YAAA,EAAAsC,GAAA,CAAAG,eAAA;YACApB,GAAA,CAAA5J,IAAA,IAAA6K,GAAA,CAAAM,KAAA;YACAvB,GAAA,CAAAqB,GAAA;UACA;QACA;QACAnB,OAAA,CAAAsB,WAAA,CAAAxB,GAAA;QACA,OAAAA,GAAA;MACA;MACA,IAAAyB,GAAA;MACA,SAAAzN,KAAA;QACAyN,GAAA,QAAAlR,MAAA,CAAA6M,GAAA,WAAA3I,CAAA;UAAA,OAAAA,CAAA,CAAAmE,qBAAA;QAAA,GAAA8I,QAAA;MACA;QACAD,GAAA,QAAAlR,MAAA,CAAA6M,GAAA,WAAA3I,CAAA;UAAA,OAAAA,CAAA,CAAAmL,iBAAA;QAAA,GAAA8B,QAAA;MACA;MACA,KAAArR,UAAA,GAAAoR,GAAA;IACA;IACAE,iBAAA,WAAAA,kBAAA9K,IAAA;MAAA,IAAA+K,OAAA;MAAA,OAAArM,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoM,SAAA;QAAA,IAAAC,KAAA,EAAAC,KAAA,EAAArD,GAAA,EAAAsD,aAAA;QAAA,OAAAxM,mBAAA,GAAAG,IAAA,UAAAsM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApM,IAAA,GAAAoM,SAAA,CAAAnM,IAAA;YAAA;cACA+L,KAAA,GAAAK,kBAAA,KAAAC,GAAA,CAAAvL,IAAA,CAAAuG,GAAA,WAAA3I,CAAA;gBAAA,OAAAA,CAAA,CAAA4N,eAAA;cAAA;cAAAN,KAAA,gBAAAvM,mBAAA,GAAAC,IAAA,UAAAsM,MAAArD,GAAA;gBAAA,OAAAlJ,mBAAA,GAAAG,IAAA,UAAA2M,OAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAzM,IAAA,GAAAyM,SAAA,CAAAxM,IAAA;oBAAA;sBAEA,IAAA6L,OAAA,CAAAvK,YAAA,CAAAmL,cAAA,CAAA9D,GAAA;wBACAoD,KAAA,GAAAA,KAAA,CAAAnH,MAAA,WAAAvE,IAAA;0BAAA,OAAAA,IAAA,KAAAsI,GAAA;wBAAA;sBACA;oBAAA;oBAAA;sBAAA,OAAA6D,SAAA,CAAA/L,IAAA;kBAAA;gBAAA,GAAAuL,KAAA;cAAA;cAAAG,SAAA,CAAAO,EAAA,GAAAjN,mBAAA,GAAAkN,IAAA,CAHAd,OAAA,CAAAvK,YAAA;YAAA;cAAA,KAAA6K,SAAA,CAAAS,EAAA,GAAAT,SAAA,CAAAO,EAAA,IAAAG,IAAA;gBAAAV,SAAA,CAAAnM,IAAA;gBAAA;cAAA;cAAA2I,GAAA,GAAAwD,SAAA,CAAAS,EAAA,CAAApU,KAAA;cAAA,OAAA2T,SAAA,CAAAW,aAAA,CAAAd,KAAA,CAAArD,GAAA;YAAA;cAAAwD,SAAA,CAAAnM,IAAA;cAAA;YAAA;cAAAmM,SAAA,CAAAnM,IAAA;cAAA,OAKA6L,OAAA,CAAAkB,eAAA,CAAAhB,KAAA;YAAA;cAAAE,aAAA,GAAAE,SAAA,CAAAhL,IAAA;cACAqH,MAAA,CAAAC,MAAA,CAAAoD,OAAA,CAAAvK,YAAA,EAAA2K,aAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAA1L,IAAA;UAAA;QAAA,GAAAqL,QAAA;MAAA;IACA;IACAiB,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,IAAAC,OAAA,GAAA5C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzO,SAAA,GAAAyO,SAAA;MACA4C,OAAA,GAAAA,OAAA,CAAArI,MAAA,WAAAlG,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,KAAAuO,OAAA,CAAA3C,MAAA,SAAAvD,OAAA,CAAAC,OAAA;MACA,WAAAD,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA3Q,gCAAA;UACA4W,eAAA,EAAAD,OAAA;UACAnD,IAAA;QACA,GAAAxJ,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA6F,SAAA;YACA,IAAA+G,MAAA,GAAA5M,GAAA,CAAAC,IAAA;YACA,IAAA4M,KAAA,GAAAD,MAAA,CAAAE,MAAA,WAAAC,GAAA,EAAA9O,IAAA;cACA8O,GAAA,CAAA9O,IAAA,CAAAwE,IAAA,IAAAxE,IAAA,CAAAqJ,eAAA;cACA,OAAAyF,GAAA;YACA;YACAhL,OAAA,CAAAC,GAAA,UAAA6K,KAAA;YACApG,OAAA,CAAAoG,KAAA;UACA;YACAJ,OAAA,CAAA7J,QAAA;cACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;cACA1H,IAAA;YACA;YACA+H,MAAA;UACA;QACA;MACA;IACA;IACAsG,eAAA,WAAAA,gBAAAC,OAAA;MAAA,IAAAC,OAAA;MAAA,OAAAjO,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgO,SAAA;QAAA,IAAAC,eAAA;QAAA,OAAAlO,mBAAA,GAAAG,IAAA,UAAAgO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9N,IAAA,GAAA8N,SAAA,CAAA7N,IAAA;YAAA;cAAA,KACAyN,OAAA,CAAAtO,QAAA;gBAAA0O,SAAA,CAAA7N,IAAA;gBAAA;cAAA;cAAA6N,SAAA,CAAA7N,IAAA;cAAA,OACAyN,OAAA,CAAA7B,iBAAA,CAAA4B,OAAA;YAAA;cAEAlL,OAAA,CAAAlJ,IAAA;cACAuU,eAAA;cAEAH,OAAA,CAAAzJ,OAAA,WAAA+J,OAAA,EAAA3C,KAAA;gBACA,IAAAnH,GAAA,GAAAyJ,OAAA,CAAAM,iBAAA,CAAAD,OAAA;gBAEA,KAAA9J,GAAA;kBACA1B,OAAA,CAAAC,GAAA,YAAAuL,OAAA;kBACAA,OAAA,CAAAE,KAAA,GAAAF,OAAA,CAAAjD,IAAA;kBACAiD,OAAA,CAAAG,cAAA,GAAAH,OAAA,CAAA/F,WAAA;kBACA+F,OAAA,CAAAI,eAAA,GAAA9X,OAAA,CAAA0X,OAAA,CAAA/F,WAAA,GAAA+F,OAAA,CAAAK,MAAA,EAAAC,MAAA;kBAEA,IAAAX,OAAA,CAAAtO,QAAA,KAAA2O,OAAA,CAAAjG,eAAA;oBACA,IAAA4F,OAAA,CAAAnM,YAAA,CAAAwM,OAAA,CAAAxB,eAAA,KAAAmB,OAAA,CAAAnM,YAAA,CAAAwM,OAAA,CAAAxB,eAAA,aAAA+B,KAAA;sBACA,IAAAC,UAAA,GAAAb,OAAA,CAAAnM,YAAA,CAAAwM,OAAA,CAAAxB,eAAA;sBACA,IAAAwB,OAAA,CAAAxG,iBAAA,KAAAgH,UAAA,CAAApK,QAAA,CAAA4J,OAAA,CAAAxG,iBAAA;wBACAqG,eAAA;sBACA;wBACAG,OAAA,CAAAjG,eAAA,GAAAyG,UAAA,CAAAC,IAAA;sBACA;oBACA;kBACA;kBAEAd,OAAA,CAAAjT,MAAA,CAAA8I,IAAA,CAAAwK,OAAA;kBACAL,OAAA,CAAA3C,kBAAA,CAAAgD,OAAA;kBACA;gBACA;gBAEA9J,GAAA,CAAAgK,KAAA,GAAAF,OAAA,CAAAjD,IAAA;gBAEA7G,GAAA,CAAAiK,cAAA,IAAAH,OAAA,CAAA/F,WAAA;gBACA/D,GAAA,CAAAkK,eAAA,GAAA9X,OAAA,CAAA4N,GAAA,CAAAkK,eAAA,EAAAM,GAAA,CAAAV,OAAA,CAAA/F,WAAA,GAAA+F,OAAA,CAAAK,MAAA,EAAAC,MAAA;gBACA,KAAApK,GAAA,CAAA6D,eAAA;kBACA;gBACA;gBACA4F,OAAA,CAAAhC,WAAA,CAAAzH,GAAA;cACA;;cAEA;cACA;cACA;cACA;cACA;cACAyJ,OAAA,CAAAjT,MAAA,CAAAiU,IAAA,WAAAC,CAAA,EAAAC,CAAA;gBAAA,OAAAD,CAAA,CAAAE,YAAA,GAAAD,CAAA,CAAAC,YAAA;cAAA;cACAtM,OAAA,CAAAuM,OAAA;cACAvM,OAAA,CAAAC,GAAA,mBAAAkL,OAAA,CAAApM,SAAA,EAAAoM,OAAA,CAAAjT,MAAA;YAAA;YAAA;cAAA,OAAAqT,SAAA,CAAApN,IAAA;UAAA;QAAA,GAAAiN,QAAA;MAAA;IACA;IACA5C,kBAAA,WAAAA,mBAAAgD,OAAA;MACA,IAAAnF,GAAA,QAAAmG,SAAA,CAAAhB,OAAA;MACA,KAAAzM,SAAA,CAAAsH,GAAA,IAAAmF,OAAA;IACA;IACAC,iBAAA,WAAAA,kBAAAD,OAAA;MACA,IAAAnF,GAAA,QAAAmG,SAAA,CAAAhB,OAAA;MACA,YAAAzM,SAAA,CAAAsH,GAAA;IACA;IACAmG,SAAA,WAAAA,UAAAhB,OAAA;MACA,OAAA9X,SAAA,MAAAiI,KAAA,EAAA6P,OAAA;IACA;IACAiB,SAAA,WAAAA,UAAA;MACA,IAAAC,UAAA;MACA,KAAAC,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA,EAAAH,UAAA;MACA;MACA,OAAAA,UAAA;IACA;IACAI,SAAA,WAAAA,UAAA;MAAA,IAAAC,UAAA,GAAAhF,SAAA;QAAAiF,OAAA;MAAA,OAAA9P,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6P,SAAA;QAAA,IAAAC,mBAAA;QAAA,IAAAC,OAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,SAAA,EAAApO,MAAA,EAAAqO,SAAA;QAAA,OAAApQ,mBAAA,GAAAG,IAAA,UAAAkQ,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhQ,IAAA,GAAAgQ,UAAA,CAAA/P,IAAA;YAAA;cAAAyP,OAAA,GAAAJ,UAAA,CAAA/E,MAAA,QAAA+E,UAAA,QAAAzT,SAAA,GAAAyT,UAAA;cACAK,YAAA,GAAAJ,OAAA,CAAAP,SAAA;cAAA,IACAW,YAAA;gBAAAK,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,OAAA+P,UAAA,CAAAxG,MAAA;YAAA;cAAAoG,qBAAA,GACAL,OAAA,CAAAU,eAAA,IAAAJ,SAAA,GAAAD,qBAAA,CAAAC,SAAA,EAAApO,MAAA,GAAAmO,qBAAA,CAAAnO,MAAA;cAAA,IACAA,MAAA;gBAAAuO,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,OAAA+P,UAAA,CAAAxG,MAAA;YAAA;cACA,KAAAkG,OAAA;gBACAH,OAAA,CAAAnU,WAAA;cACA;cAAA4U,UAAA,CAAA/P,IAAA;cAAA,OAEAsP,OAAA,CAAAW,eAAA,CAAAL,SAAA,EAAAH,OAAA;YAAA;cAAAI,SAAA,GAAAE,UAAA,CAAA5O,IAAA;cACAmB,OAAA,CAAAC,GAAA,cAAAsN,SAAA;cAAA,IACAA,SAAA;gBAAAE,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,OAAA+P,UAAA,CAAAxG,MAAA;YAAA;cAAA,KACAkG,OAAA;gBAAAM,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,OAAA+P,UAAA,CAAAxG,MAAA,WAAAsG,SAAA;YAAA;cACA,CAAAL,mBAAA,GAAAF,OAAA,CAAAL,KAAA,uBAAAO,mBAAA,eAAAA,mBAAA,CAAAzN,SAAA;cACAuN,OAAA,CAAAnU,WAAA;YAAA;YAAA;cAAA,OAAA4U,UAAA,CAAAtP,IAAA;UAAA;QAAA,GAAA8O,QAAA;MAAA;IACA;IACAW,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,OAAA3Q,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0Q,UAAA;QAAA,IAAAV,YAAA,EAAAW,GAAA,EAAAC,IAAA;QAAA,OAAA7Q,mBAAA,GAAAG,IAAA,UAAA2Q,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAzQ,IAAA,GAAAyQ,UAAA,CAAAxQ,IAAA;YAAA;cACA0P,YAAA,GAAAS,OAAA,CAAApB,SAAA;cAAA,IACAW,YAAA;gBAAAc,UAAA,CAAAxQ,IAAA;gBAAA;cAAA;cAAA,OAAAwQ,UAAA,CAAAjH,MAAA;YAAA;cACA8G,GAAA;cAAA,IACAF,OAAA,CAAA3V,MAAA,CAAA8P,MAAA;gBAAAkG,UAAA,CAAAxQ,IAAA;gBAAA;cAAA;cACAmQ,OAAA,CAAAhN,QAAA;gBACAC,OAAA;gBACAlE,IAAA;cACA;cAAA,OAAAsR,UAAA,CAAAjH,MAAA;YAAA;cAGA,IAAA4G,OAAA,CAAAlS,KAAA;gBACAoS,GAAA,CAAA/H,eAAA,GAAA6H,OAAA,CAAA3V,MAAA;cACA;gBACA6V,GAAA,CAAAhH,cAAA,GAAA8G,OAAA,CAAA3V,MAAA;cACA;cACA,IAAA2V,OAAA,CAAAhS,MAAA;gBACAkS,GAAA,CAAAhI,cAAA,GAAA8H,OAAA,CAAApW,UAAA;cACA;gBACAsW,GAAA,CAAAhI,cAAA,GAAArK,aAAA,CAAAA,aAAA,KACAmS,OAAA,CAAApW,UAAA;kBACA0W,UAAA,EAAAN,OAAA,CAAA9T,SAAA;kBACAqU,OAAA,EAAAP,OAAA,CAAA7T,MAAA;kBACAqU,eAAA,EAAAR,OAAA,CAAA1O,KAAA;gBAAA,EACA;cACA;cACA0O,OAAA,CAAAtV,SAAA;cACAyV,IAAA,GAAAH,OAAA,CAAAlS,KAAA,GAAA3I,+BAAA,GAAAG,6BAAA;cACA6a,IAAA,CAAAD,GAAA,EAAA/P,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA6F,SAAA;kBACA+J,OAAA,CAAAtV,SAAA;kBACAsV,OAAA,CAAAhN,QAAA;oBACAC,OAAA;oBACAlE,IAAA;kBACA;kBACAiR,OAAA,CAAAvK,SAAA;gBACA;kBACAuK,OAAA,CAAAhN,QAAA;oBACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;oBACA1H,IAAA;kBACA;kBACAiR,OAAA,CAAAtV,SAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA2V,UAAA,CAAA/P,IAAA;UAAA;QAAA,GAAA2P,SAAA;MAAA;IACA;IACAJ,eAAA,WAAAA,gBAAA;MAAA,IAAAY,OAAA;MACA;MACA,IAAAhB,SAAA,GAAArF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAjQ,MAAA;MACAoV,SAAA,GAAAA,SAAA,CAAAhL,MAAA,WAAApG,IAAA;QAAA,OAAAA,IAAA,CAAAyP,cAAA;MAAA;MAAA,IAAA4C,MAAA,YAAAA,OAAA,EACA;UACA,IAAA/C,OAAA,GAAA8B,SAAA,CAAAkB,CAAA;UACA,IAAAhQ,IAAA;UACA,KAAAgN,OAAA,CAAAjG,eAAA;YACA+I,OAAA,CAAAzN,QAAA;cACAC,OAAA;cACAlE,IAAA;YACA;YAAA;cAAAR,CAAA,EACA;gBAAA8C,MAAA;cAAA;YAAA;UACA;UACA,IAAAoP,OAAA,CAAA/R,aAAA,KAAAiP,OAAA,CAAAxG,iBAAA,IAAAwG,OAAA,CAAAhE,IAAA,iBAAA8G,OAAA,CAAArN,OAAA;YACA,IAAAwN,GAAA;YACA,IAAAH,OAAA,CAAA7R,MAAA;cACA,IAAA+O,OAAA,CAAAjL,qBAAA;gBACA+N,OAAA,CAAAzN,QAAA;kBACAC,OAAA,EAAA2N,GAAA;kBACA7R,IAAA;gBACA;gBAAA;kBAAAR,CAAA,EACA;oBAAA8C,MAAA;kBAAA;gBAAA;cACA;YACA;cACAoP,OAAA,CAAAzN,QAAA;gBACAC,OAAA,EAAA2N,GAAA;gBACA7R,IAAA;cACA;cAAA;gBAAAR,CAAA,EACA;kBAAA8C,MAAA;gBAAA;cAAA;YACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAAsM,OAAA,CAAAhG,yBAAA,IAAAgG,OAAA,CAAAhG,yBAAA,KAAAgG,OAAA,CAAAjG,eAAA;YACA+I,OAAA,CAAAzN,QAAA;cACAC,OAAA,6EAAAC,MAAA,CAAAuN,OAAA,CAAAtT,QAAA;cACA4B,IAAA;YACA;YAAA;cAAAR,CAAA,EACA;gBAAA8C,MAAA;cAAA;YAAA;UACA;UACA,IAAAsM,OAAA,CAAAvG,sBAAA,IAAAuG,OAAA,CAAAvG,sBAAA,KAAAuG,OAAA,CAAAxG,iBAAA;YACAsJ,OAAA,CAAAzN,QAAA;cACAC,OAAA,6EAAAC,MAAA,CAAAuN,OAAA,CAAAtT,QAAA;cACA4B,IAAA;YACA;YAAA;cAAAR,CAAA,EACA;gBAAA8C,MAAA;cAAA;YAAA;UACA;UACA,IAAAmJ,WAAA,GAAA0D,KAAA,CAAA2C,IAAA,KAAA3E,GAAA,CAAAyB,OAAA,CAAAjG,eAAA,CAAA+C,KAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAAA,IAAAqG,MAAA,YAAAA,OAAA,EACA;YACA,IAAA5Q,IAAA,GAAAsK,WAAA,CAAAuG,CAAA;YACA,IAAAC,aAAA,GAAArD,OAAA,CAAAG,cAAA;YACA,IAAAmD,MAAA;YACA,IAAAtD,OAAA,CAAAuD,gBAAA;cACAD,MAAA,GAAAtD,OAAA,CAAAuD,gBAAA,CAAAzM,MAAA,WAAAlG,CAAA;gBAAA,OAAAA,CAAA,CAAAkK,YAAA,KAAAvI,IAAA;cAAA;YACA;YACA,IAAAiR,UAAA,GAAAF,MAAA,CAAA/D,MAAA,WAAAC,GAAA,EAAAtJ,GAAA;cACA,OAAAsJ,GAAA,IAAAtJ,GAAA,CAAAuN,WAAA;YACA;YACA,IAAAD,UAAA,GAAAH,aAAA;cACArQ,IAAA;cAAA;YAEA;cAAA,IAAA0Q,MAAA;cACA,CAAAA,MAAA,GAAA1Q,IAAA,EAAAwC,IAAA,CAAAmO,KAAA,CAAAD,MAAA,EAAApF,kBAAA,CAAAgF,MAAA;YACA;UACA;UAhBA,SAAAF,CAAA,MAAAA,CAAA,GAAAvG,WAAA,CAAAL,MAAA,EAAA4G,CAAA;YAAA,IAAAD,MAAA,IAYA;UAAA;UAKA,IAAAS,QAAA,GAAAlJ,MAAA,CAAAmE,IAAA,CAAAmB,OAAA,EAAAlJ,MAAA,WAAA+M,CAAA;YAAA,OAAAA,CAAA,CAAAC,UAAA,CAAA9D,OAAA;UAAA;UACA4D,QAAA,CAAA3N,OAAA,WAAAvF,IAAA;YACA,OAAAsP,OAAA,CAAAtP,IAAA;UACA;UACA,OAAAsP,OAAA;UACA,OAAAA,OAAA;UACA,OAAAA,OAAA;UACAA,OAAA,CAAAuD,gBAAA,GAAAvQ,IAAA;QACA;QAAA+Q,IAAA;MAjGA,SAAAf,CAAA,MAAAA,CAAA,GAAAlB,SAAA,CAAAtF,MAAA,EAAAwG,CAAA;QAAAe,IAAA,GAAAhB,MAAA;QAAA,IAAAgB,IAAA,SAAAA,IAAA,CAAAnT,CAAA;MAAA;MAkGA;QAAAkR,SAAA,EAAAA,SAAA;QAAApO,MAAA;MAAA;IACA;IACAyO,eAAA,WAAAA,gBAAAL,SAAA,EAAAH,OAAA;MAAA,IAAAqC,OAAA;MAAA,OAAAtS,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqS,UAAA;QAAA,IAAAzB,IAAA,EAAAD,GAAA,EAAApF,CAAA,EAAA+G,MAAA,EAAAC,YAAA;QAAA,OAAAxS,mBAAA,GAAAG,IAAA,UAAAsS,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApS,IAAA,GAAAoS,UAAA,CAAAnS,IAAA;YAAA;cACAsC,OAAA,CAAAC,GAAA;cACA+N,IAAA,GAAAwB,OAAA,CAAA7T,KAAA,GAAA1I,sBAAA,GAAAC,yBAAA;cACA6a,GAAA,OACA;cACA;cACApF,CAAA;cACA,KAAA+G,MAAA,IAAAF,OAAA,CAAAnH,WAAA;gBACA,IAAAmH,OAAA,CAAAnH,WAAA,CAAA8B,cAAA,CAAAuF,MAAA;kBACA/G,CAAA,CAAA3H,IAAA,CAAAwO,OAAA,CAAAnH,WAAA,CAAAqH,MAAA;gBACA;cACA;cACA3B,GAAA,CAAA9H,YAAA,GAAA0C,CAAA;cACA;cACAoF,GAAA,CAAAhH,cAAA,GAAAuG,SAAA;cACA;cACA,IAAAkC,OAAA,CAAA3T,MAAA;gBACAkS,GAAA,CAAAhI,cAAA,GAAAyJ,OAAA,CAAA/X,UAAA;cACA;gBACAsW,GAAA,CAAAhI,cAAA,GAAArK,aAAA,CAAAA,aAAA,KACA8T,OAAA,CAAA/X,UAAA;kBACA0W,UAAA,EAAAqB,OAAA,CAAAzV,SAAA;kBACAqU,OAAA,EAAAoB,OAAA,CAAAxV,MAAA;kBACAqU,eAAA,EAAAmB,OAAA,CAAArQ,KAAA;gBAAA,EACA;cACA;cACAwQ,YAAA;cACA3P,OAAA,CAAAC,GAAA,QAAA8N,GAAA;cAAA8B,UAAA,CAAAnS,IAAA;cAAA,OAEAsQ,IAAA,CAAAD,GAAA,EAAA/P,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA6F,SAAA;kBACA,KAAAqJ,OAAA;oBACAqC,OAAA,CAAAjX,SAAA;oBACAiX,OAAA,CAAA3O,QAAA;sBACAC,OAAA;sBACAlE,IAAA;oBACA;oBACA4S,OAAA,CAAAlM,SAAA;kBACA;oBACAkM,OAAA,CAAAM,oBAAA,GAAA7R,GAAA,CAAAC,IAAA;oBACAyR,YAAA;oBACA3P,OAAA,CAAAC,GAAA;kBACA;gBACA;kBACAuP,OAAA,CAAA3W,WAAA;kBACA2W,OAAA,CAAAjX,SAAA;kBACAiX,OAAA,CAAA3O,QAAA;oBACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;oBACA1H,IAAA;kBACA;gBACA;cACA;YAAA;cACAoD,OAAA,CAAAC,GAAA;cAAA,OAAA4P,UAAA,CAAA5I,MAAA,WACA0I,YAAA;YAAA;YAAA;cAAA,OAAAE,UAAA,CAAA1R,IAAA;UAAA;QAAA,GAAAsR,SAAA;MAAA;IACA;IACAM,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAxX,aAAA;MACAyX,UAAA;QACA,IAAAC,aAAA,OAAAnG,GAAA,CAAAiG,OAAA,CAAA3X,iBAAA,CAAA0M,GAAA,WAAA3I,CAAA;UAAA,OAAAA,CAAA,CAAAmM,IAAA;QAAA;QACAyH,OAAA,CAAA9X,MAAA,GAAA8X,OAAA,CAAA9X,MAAA,CAAAoK,MAAA,WAAApG,IAAA;UACA,IAAAiU,UAAA,GAAAD,aAAA,CAAAE,GAAA,CAAAlU,IAAA,CAAAqM,IAAA;UACA,IAAA4H,UAAA;YACA,IAAA9J,GAAA,GAAA2J,OAAA,CAAAxD,SAAA,CAAAtQ,IAAA;YACA,OAAA8T,OAAA,CAAAjR,SAAA,CAAAsH,GAAA;UACA;UACA,QAAA8J,UAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACAH,OAAA,CAAAxX,aAAA;MACA;IACA;IACA+I,WAAA,WAAAA,YAAA;MAAA,IAAA8O,OAAA;MAAA,OAAAnT,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkT,UAAA;QAAA,OAAAnT,mBAAA,GAAAG,IAAA,UAAAiT,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/S,IAAA,GAAA+S,UAAA,CAAA9S,IAAA;YAAA;cAAA8S,UAAA,CAAA9S,IAAA;cAAA,OACA3K,wBAAA;gBACA6J,IAAA,EAAAyT,OAAA,CAAA1U,KAAA;cACA,GAAAqC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA6F,SAAA;kBACAuM,OAAA,CAAA3W,WAAA,GAAAuE,GAAA,CAAAC,IAAA;gBACA;kBACAmS,OAAA,CAAAxP,QAAA;oBACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;oBACA1H,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA4T,UAAA,CAAArS,IAAA;UAAA;QAAA,GAAAmS,SAAA;MAAA;IACA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAA/D,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACA,IAAA8D,qBAAA,GAAAD,OAAA,CAAAhD,eAAA;UAAAJ,SAAA,GAAAqD,qBAAA,CAAArD,SAAA;UAAApO,MAAA,GAAAyR,qBAAA,CAAAzR,MAAA;QACA,KAAAA,MAAA;QACAwR,OAAA,CAAAE,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAlU,IAAA;QACA,GAAAoB,IAAA;UACA0S,OAAA,CAAAK,iBAAA,CAAAzD,SAAA;QACA,GAAA0D,KAAA;UACAN,OAAA,CAAA7P,QAAA;YACAjE,IAAA;YACAkE,OAAA;UACA;QACA;MACA;IACA;IACAiQ,iBAAA,WAAAA,kBAAA;MAAA,IAAAE,OAAA;MAAA,OAAA/T,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8T,UAAA;QAAA,IAAAC,kBAAA;QAAA,IAAA5D,SAAA,EAAA6D,UAAA;QAAA,OAAAjU,mBAAA,GAAAG,IAAA,UAAA+T,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7T,IAAA,GAAA6T,UAAA,CAAA5T,IAAA;YAAA;cACAuT,OAAA,CAAA1Y,SAAA;cAAA,OAAA4Y,kBAAA,GACAF,OAAA,CAAAxZ,UAAA,cAAA0Z,kBAAA,eAAAA,kBAAA,CAAAzZ,cAAA;gBAAA4Z,UAAA,CAAA5T,IAAA;gBAAA;cAAA;cAAA4T,UAAA,CAAA5T,IAAA;cAAA,OACAuT,OAAA,CAAAnE,SAAA;YAAA;cAAAS,SAAA,GAAA+D,UAAA,CAAAzS,IAAA;cACAmB,OAAA,CAAAC,GAAA,sBAAAsN,SAAA;cACAA,SAAA,IAAA0D,OAAA,CAAAM,QAAA,CAAAN,OAAA,CAAAxZ,UAAA,CAAA4E,EAAA;cAAAiV,UAAA,CAAA5T,IAAA;cAAA;YAAA;cAAA4T,UAAA,CAAA5T,IAAA;cAAA,OAEAuT,OAAA,CAAAnE,SAAA;YAAA;cAAAS,UAAA,GAAA+D,UAAA,CAAAzS,IAAA;cACA0O,UAAA,IAAA0D,OAAA,CAAAM,QAAA,CAAAN,OAAA,CAAAnB,oBAAA;YAAA;YAAA;cAAA,OAAAwB,UAAA,CAAAnT,IAAA;UAAA;QAAA,GAAA+S,SAAA;MAAA;IAEA;IACAK,QAAA,WAAAA,SAAAC,YAAA;MAAA,IAAAC,OAAA;MACAre,qBAAA;QACAse,eAAA,EAAAF;MACA,GAAAxT,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA6F,SAAA;UACA2N,OAAA,CAAA5Q,QAAA;YACAC,OAAA;YACAlE,IAAA;UACA;UACA6U,OAAA,CAAAnO,SAAA;QACA;UACAmO,OAAA,CAAA5Q,QAAA;YACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;YACA1H,IAAA;UACA;QACA;MACA,GAAA+U,OAAA,WAAAtC,CAAA;QACAoC,OAAA,CAAAlZ,SAAA;MACA,GAAAyY,KAAA,WAAA3B,CAAA;QACAoC,OAAA,CAAAlZ,SAAA;MACA;IACA;IACAqZ,WAAA,WAAAA,YAAA1b,KAAA;MAAA,IAAA2b,OAAA;MACA7R,OAAA,CAAAC,GAAA,UAAA/J,KAAA;MACA,IACA4b,MAAA,GAMA5b,KAAA,CANA4b,MAAA;QACAnK,GAAA,GAKAzR,KAAA,CALAyR,GAAA;QAAAoK,eAAA,GAKA7b,KAAA,CAJA8b,QAAA;QACA3V,EAAA,GAAA0V,eAAA,CAAA1V,EAAA;QACA4V,YAAA,GAAAF,eAAA,CAAAE,YAAA;MAGA,IAAAH,MAAA;QAAA,IAAAI,gBAAA;QACA,KAAAA,gBAAA,GAAAhc,KAAA,CAAA8b,QAAA,cAAAE,gBAAA,eAAAA,gBAAA,CAAA7V,EAAA;UACAsL,GAAA,CAAAtC,aAAA,GAAA4M,YAAA;UACAtK,GAAA,CAAAxC,WAAA,GAAA9I,EAAA;UACA,KAAA8V,OAAA,CAAAxK,GAAA,EAAAtL,EAAA;QACA;UACAsL,GAAA,CAAAtC,aAAA;UACAsC,GAAA,CAAAxC,WAAA;QACA;MACA;QACA,KAAA9M,iBAAA,CAAAoJ,OAAA,WAAAvF,IAAA;UAAA,IAAAkW,gBAAA;UACA,KAAAA,gBAAA,GAAAlc,KAAA,CAAA8b,QAAA,cAAAI,gBAAA,eAAAA,gBAAA,CAAA/V,EAAA;YACAH,IAAA,CAAAmJ,aAAA,GAAA4M,YAAA;YACA/V,IAAA,CAAAiJ,WAAA,GAAA9I,EAAA;YACAwV,OAAA,CAAAM,OAAA,CAAAjW,IAAA,EAAAG,EAAA;UACA;YACAH,IAAA,CAAAmJ,aAAA;YACAnJ,IAAA,CAAAiJ,WAAA;UACA;QACA;MACA;IACA;IACAgN,OAAA,WAAAA,QAAAxK,GAAA,EAAAtL,EAAA;MACA,IAAAsL,GAAA,aAAAA,GAAA,eAAAA,GAAA,CAAAvC,qBAAA;QACA,IAAAuC,GAAA,CAAAvC,qBAAA,KAAA/I,EAAA;UACAsL,GAAA,CAAApC,eAAA;QACA;MACA;QACAoC,GAAA,CAAApC,eAAA;MACA;IACA;IACA8M,mBAAA,WAAAA,oBAAAP,MAAA,EAAAnK,GAAA;MAAA,IAAA2K,OAAA;MACA,KAAAnZ,KAAA,GAAA2Y,MAAA;MACA,KAAA9Y,gBAAA;MACA,KAAAE,MAAA;MACA,KAAAP,aAAA;MACA,KAAA4Z,SAAA,WAAAlD,CAAA;QACAiD,OAAA,CAAA3F,KAAA,YAAAlN,SAAA,CAAAqS,MAAA,EAAAnK,GAAA;MACA;IACA;IAEA6K,gBAAA,WAAAA,iBAAAC,UAAA;MAAA,IAAAC,OAAA;MACA,WAAAjO,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA1Q,kBAAA;UACAwe,UAAA,EAAAA,UAAA;UACA7V,IAAA;QACA,GAAAoB,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA6F,SAAA;YACA,IAAAnG,QAAA,GAAAM,GAAA,CAAAC,IAAA,CAAA6G,GAAA,WAAA3I,CAAA;cAAA,OAAAA,CAAA,CAAAsE,IAAA;YAAA;YACAgE,OAAA,CAAA/G,QAAA;UACA;YACA+U,OAAA,CAAA7R,QAAA;cACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;cACA1H,IAAA;YACA;UACA;QACA;MACA;IACA;IACA+V,UAAA,WAAAA,WAAA5U,IAAA,EAAA0U,UAAA;MAAA,IAAAG,OAAA;MACA,WAAAnO,OAAA,WAAAC,OAAA;QACA,IAAAqJ,GAAA;UACA8E,cAAA,EAAA9U,IAAA;UACAnB,IAAA;QACA;QACA,IAAAgW,OAAA,CAAAvQ,eAAA;UACA0L,GAAA,CAAA0E,UAAA,GAAAA,UAAA;QACA;QACA1e,cAAA,CAAAga,GAAA,EAAA/P,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA6F,SAAA;YACA,IAAA7F,GAAA,CAAAC,IAAA,CAAAA,IAAA,IAAAD,GAAA,CAAAC,IAAA,CAAAA,IAAA,CAAA8J,MAAA;cACA,IAAA8K,IAAA,GAAA7U,GAAA,CAAAC,IAAA,CAAAA,IAAA;cACA,IAAA6U,QAAA,GAAAD,IAAA,CAAAE,QAAA,IAAAF,IAAA,CAAAE,QAAA,CAAAC,OAAA;cACAvO,OAAA,CAAAqO,QAAA;YACA;cACArO,OAAA,CAAApL,SAAA;YACA;UACA;YACAsZ,OAAA,CAAA/R,QAAA;cACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;cACA1H,IAAA;YACA;UACA;QACA;MACA;IACA;IACAsW,WAAA,WAAAA,YAAAvL,GAAA;MACA,KAAAwB,WAAA,CAAAxB,GAAA;IACA;IACAwB,WAAA,WAAAA,YAAAxB,GAAA;MACA,IAAAwL,eAAA,GAAAjN,MAAA,CAAAmE,IAAA,CAAA1C,GAAA,EACArF,MAAA,WAAAlG,CAAA;QAAA,QAAAA,CAAA,CAAAgX,QAAA,WAAAhX,CAAA,CAAAkT,UAAA,CAAA3H,GAAA,CAAAY,IAAA,KAAAnM,CAAA,CAAA4L,MAAA,GAAAL,GAAA,CAAAY,IAAA,CAAAP,MAAA;MAAA;MACAmL,eAAA,CAAA1R,OAAA,WAAA4R,GAAA;QACA,IAAAC,OAAA,GAAAD,GAAA,CAAA/K,KAAA,CAAApT,YAAA;QACA,IAAAqe,UAAA,GAAAJ,eAAA,CAAA7Q,MAAA,WAAAkR,CAAA;UACA,IAAAzV,IAAA,GAAAyV,CAAA,CAAAlL,KAAA,CAAApT,YAAA;UACA,OAAAse,CAAA,KAAAH,GAAA,IAAAtV,IAAA,KAAAuV,OAAA;QACA,GAAAvI,MAAA,WAAAC,GAAA,EAAA9O,IAAA;UACA,OAAA8O,GAAA,GAAAlX,OAAA,CAAA6T,GAAA,CAAAzL,IAAA,GAAAhG,KAAA;QACA;QACAyR,GAAA,CAAA0L,GAAA,GAAAne,YAAA,YAAAyS,GAAA,CAAAgE,cAAA,GAAA4H,UAAA;MACA;IACA;IACAE,WAAA,WAAAA,YAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,GAAA,GAAAF,IAAA,CAAAE,GAAA;MACA,IAAArG,SAAA;MACA,SAAAiB,CAAA,MAAAA,CAAA,GAAAmF,GAAA,CAAA3L,MAAA,EAAAwG,CAAA;QACA,IAAAtS,IAAA,GAAAyX,GAAA,CAAAnF,CAAA;QACA,IAAAtS,IAAA,CAAA2X,YAAA,IAAA3X,IAAA,CAAA2X,YAAA,KAAAD,GAAA;UACArG,SAAA;UACA;QACA;QACArR,IAAA,CAAAqJ,eAAA,GAAAqO,GAAA;MACA;MACA,KAAArG,SAAA;QACA,KAAA1M,QAAA;UACAC,OAAA;UACAlE,IAAA;QACA;MACA;IACA;IACAkX,gBAAA,WAAAA,iBAAAnM,GAAA,EAAAiM,GAAA;MAAA,IAAAG,IAAA;QAAAC,OAAA;MACA,IAAAJ,GAAA;QACAjM,GAAA,CAAApC,eAAA,GAAAqO,GAAA;MACA;QACAA,GAAA,GAAAjM,GAAA,CAAApC,eAAA;MACA;MACA,IAAA/G,IAAA,KAAAuV,IAAA,GAAAH,GAAA,cAAAG,IAAA,uBAAAA,IAAA,CAAAzL,KAAA;MACA,KAAA5O,WAAA,CAAA+H,OAAA,WAAA+J,OAAA,EAAAhL,GAAA;QACA,IAAAkB,GAAA,GAAAlD,IAAA,CAAAyV,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,KAAA1I,OAAA,CAAAlF,YAAA;QAAA;QACA,IAAAvI,IAAA,GAAAiW,OAAA,CAAAlL,YAAA,CAAAnB,GAAA,CAAAY,IAAA,EAAAiD,OAAA,CAAAlF,YAAA,EAAAkF,OAAA,CAAAzC,eAAA;QACA,IAAAC,GAAA,GAAAgL,OAAA,CAAA/K,eAAA,CAAAtB,GAAA,CAAAY,IAAA,EAAAiD,OAAA,CAAAlF,YAAA,EAAAkF,OAAA,CAAAzC,eAAA;QACA,IAAArH,GAAA;UACA,KAAAiG,GAAA,CAAA5J,IAAA;YACAiW,OAAA,CAAApM,IAAA,CAAAD,GAAA,EAAA5J,IAAA;YACAiW,OAAA,CAAApM,IAAA,CAAAD,GAAA,EAAAqB,GAAA,EAAArB,GAAA,CAAAgE,cAAA;UACA;QACA;UACAqI,OAAA,CAAAG,OAAA,CAAAxM,GAAA,EAAA5J,IAAA;UACAiW,OAAA,CAAAG,OAAA,CAAAxM,GAAA,EAAAqB,GAAA;QACA;MACA;IACA;IACAoL,mBAAA,WAAAA,oBAAAC,UAAA,EAAAC,WAAA;MACA,KAAAD,UAAA;MACA,IAAA7V,IAAA,IAAA6V,UAAA,aAAAA,UAAA,uBAAAA,UAAA,CAAA/L,KAAA;MACA,SAAA9J,IAAA,CAAAyV,IAAA,WAAA7X,CAAA;QAAA,OAAAA,CAAA,KAAAkY,WAAA;MAAA;IACA;IAEAlS,cAAA,WAAAA,eAAArE,IAAA;MAAA,IAAAwW,OAAA;MAAA,OAAArX,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoX,UAAA;QAAA,OAAArX,mBAAA,GAAAG,IAAA,UAAAmX,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAjX,IAAA,GAAAiX,UAAA,CAAAhX,IAAA;YAAA;cAAAgX,UAAA,CAAAhX,IAAA;cAAA,OACAjK,aAAA;gBACAsK,IAAA,EAAAA;cACA,GAAAC,IAAA,WAAAC,GAAA;gBACA,IAAA6F,SAAA,GAAA7F,GAAA,CAAA6F,SAAA;kBAAA5F,IAAA,GAAAD,GAAA,CAAAC,IAAA;kBAAAoG,OAAA,GAAArG,GAAA,CAAAqG,OAAA;gBACA,IAAAR,SAAA;kBACAyQ,OAAA,CAAApc,QAAA,GAAA+N,MAAA,CAAAC,MAAA,KAAAoO,OAAA,CAAApc,QAAA,EAAA+F,IAAA,CAAAyW,IAAA;kBACA,IAAAnW,IAAA,GAAAN,IAAA,CAAA0W,UAAA;kBACAL,OAAA,CAAA3T,WAAA,GAAApC,IAAA,CAAArC,IAAA,WAAAD,IAAA;oBAAA,OAAAA,IAAA,CAAAwE,IAAA;kBAAA;kBACA6T,OAAA,CAAAM,YAAA,GAAArW,IAAA,CAAArC,IAAA,WAAAD,IAAA;oBAAA,OAAAA,IAAA,CAAAwE,IAAA;kBAAA;kBACA6T,OAAA,CAAAtc,OAAA,GAAAsc,OAAA,CAAAO,gBAAA,CAAAtW,IAAA;gBACA;kBACA+V,OAAA,CAAA1T,QAAA;oBACAC,OAAA,EAAAwD,OAAA;oBACA1H,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA8X,UAAA,CAAAvW,IAAA;UAAA;QAAA,GAAAqW,SAAA;MAAA;IACA;IACAM,gBAAA,WAAAA,iBAAAtW,IAAA;MACA,OAAAA,IAAA,CAAA8D,MAAA,WAAAlG,CAAA;QAAA,OAAAA,CAAA,CAAA2Y,UAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,aAAA;MAAA,IAAAvN,GAAA,GAAAsN,KAAA,CAAAtN,GAAA;QAAAwN,MAAA,GAAAF,KAAA,CAAAE,MAAA;QAAAC,WAAA,GAAAH,KAAA,CAAAG,WAAA;MACA,SAAAxZ,MAAA;MACA,IAAA0Y,WAAA,IAAAY,aAAA,GAAAC,MAAA,CAAAE,KAAA,cAAAH,aAAA,uBAAAA,aAAA,CAAA5M,KAAA;MACA,YAAA8L,mBAAA,CAAAzM,GAAA,CAAApC,eAAA,EAAA+O,WAAA;IACA;IACAgB,aAAA,WAAAA,cAAA1Y,IAAA,EAAA+K,GAAA;MAAA,IAAA4N,OAAA;MACA,SAAAlT,eAAA;QACA,IAAAzF,IAAA;UACA,IAAA4Y,QAAA,QAAAC,qBAAA;UACA,KAAAD,QAAA;QACA;MACA;MACA,KAAArc,KAAA,GAAAyD,IAAA;MACA,KAAA5D,gBAAA;MACA,KAAAE,MAAA;MACA,KAAAP,aAAA;MACA,KAAA4Z,SAAA,WAAAlD,CAAA;QACAkG,OAAA,CAAA5I,KAAA,YAAA+I,OAAA,CAAA9Y,IAAA,UAAA+K,GAAA,IAAA4N,OAAA,CAAAld,iBAAA,EAAAuE,IAAA,SAAA+K,GAAA,CAAApC,eAAA;MACA;IACA;IACAkQ,qBAAA,WAAAA,sBAAA;MACA,IAAAE,QAAA;MACA,IAAAC,MAAA,QAAAvd,iBAAA,IAAAgN,aAAA;MACA,SAAAmJ,CAAA,MAAAA,CAAA,QAAAnW,iBAAA,CAAA2P,MAAA,EAAAwG,CAAA;QACA,IAAAtS,IAAA,QAAA7D,iBAAA,CAAAmW,CAAA;QACA,IAAAtS,IAAA,CAAAmJ,aAAA,KAAAuQ,MAAA;UACAD,QAAA;UACA;QACA;MACA;MACA,KAAAA,QAAA;QACA,KAAA9U,QAAA;UACAC,OAAA;UACAlE,IAAA;QACA;MACA;MACA,OAAA+Y,QAAA;IACA;IACAE,gBAAA,WAAAA,iBAAAjZ,IAAA,EAAA+W,GAAA;MACA,IAAAmC,WAAA;MACA,SAAAtH,CAAA,MAAAA,CAAA,GAAAmF,GAAA,CAAA3L,MAAA,EAAAwG,CAAA;QACA,IAAAtS,IAAA,GAAAyX,GAAA,CAAAnF,CAAA;QACA,KAAAtS,IAAA,CAAAmJ,aAAA;UACAyQ,WAAA;UACA;QACA;MACA;MACA,KAAAA,WAAA;QACA,KAAAjV,QAAA;UACAC,OAAA;UACAlE,IAAA;QACA;MACA;MACA,OAAAkZ,WAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,IAAApZ,IAAA,GAAAmL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzO,SAAA,GAAAyO,SAAA;MACA,KAAA5O,KAAA,kBAAA4H,MAAA,MAAA/F,QAAA;MAEA,KAAAhC,gBAAA;MACA,KAAAE,MAAA;MACA,KAAAN,YAAA;MAEA,KAAAqd,WAAA;MAEA,KAAA1D,SAAA,WAAAlD,CAAA;QACA2G,OAAA,CAAArJ,KAAA,UAAAuJ,QAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAAjL,OAAA;MAAA,IAAAkL,OAAA;MAAA,OAAAlZ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiZ,UAAA;QAAA,OAAAlZ,mBAAA,GAAAG,IAAA,UAAAgZ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9Y,IAAA,GAAA8Y,UAAA,CAAA7Y,IAAA;YAAA;cAAA6Y,UAAA,CAAA7Y,IAAA;cAAA,OACA0Y,OAAA,CAAAnL,eAAA,CAAAC,OAAA;YAAA;cACAkL,OAAA,CAAAH,WAAA;YAAA;YAAA;cAAA,OAAAM,UAAA,CAAApY,IAAA;UAAA;QAAA,GAAAkY,SAAA;MAAA;IACA;IACAJ,WAAA,WAAAA,YAAA;MACA,IAAAO,UAAA,QAAAte,MAAA,CAAAoK,MAAA,WAAAZ,GAAA;QAAA,OAAAA,GAAA,CAAAgK,KAAA;MAAA,GAAA3G,GAAA,WAAA3I,CAAA;QAAA,OAAAA,CAAA,CAAAsP,KAAA;MAAA;MACA,KAAA+K,eAAA,CAAAD,UAAA;IACA;IACA1N,YAAA,WAAAA,aAAAP,IAAA,EAAA+L,WAAA,EAAAoC,SAAA;MACA,UAAA3V,MAAA,CAAAwH,IAAA,EAAAxH,MAAA,CAAA7L,YAAA,EAAA6L,MAAA,CAAAuT,WAAA,EAAAvT,MAAA,CAAA7L,YAAA,EAAA6L,MAAA,CAAA2V,SAAA;IACA;IACAzN,eAAA,WAAAA,gBAAAV,IAAA,EAAA+L,WAAA,EAAAoC,SAAA;MACA,YAAA5N,YAAA,CAAAP,IAAA,EAAA+L,WAAA,EAAAoC,SAAA,OAAA3V,MAAA,CAAA7L,YAAA;IACA;IACAyhB,gBAAA,WAAAA,iBAAAva,CAAA;MACA,IAAAA,CAAA;QACA,KAAAkZ,aAAA;MACA,WAAAlZ,CAAA;QACA,KAAAwa,qBAAA;MACA;IACA;IACAA,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,OAAA3Z,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0Z,UAAA;QAAA,IAAAC,WAAA,EAAAC,OAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,eAAA;QAAA,OAAAha,mBAAA,GAAAG,IAAA,UAAA8Z,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5Z,IAAA,GAAA4Z,UAAA,CAAA3Z,IAAA;YAAA;cACAqZ,WAAA,YAAAA,YAAA;gBACAF,OAAA,CAAAhW,QAAA;kBACAC,OAAA;kBACAlE,IAAA;gBACA;cACA;cACAoa,OAAA,GAAAH,OAAA,CAAAxe,iBAAA,CAAA0M,GAAA,WAAA3I,CAAA;gBAAA,OAAAA,CAAA,CAAA4N,eAAA;cAAA,GAAA1H,MAAA,WAAAlG,CAAA;gBAAA,SAAAA,CAAA;cAAA;cAAA,IACA4a,OAAA,CAAAhP,MAAA;gBAAAqP,UAAA,CAAA3Z,IAAA;gBAAA;cAAA;cACAmZ,OAAA,CAAAhW,QAAA;gBACAC,OAAA;gBACAlE,IAAA;cACA;cAAA,OAAAya,UAAA,CAAApQ,MAAA;YAAA;cAAAoQ,UAAA,CAAA3Z,IAAA;cAAA,OAGAmZ,OAAA,CAAAvN,iBAAA,CAAAuN,OAAA,CAAAxe,iBAAA;YAAA;cACA4e,WAAA,GAAAlL,KAAA,CAAA2C,IAAA,KAAA3E,GAAA,CAAA8M,OAAA,CAAAxe,iBAAA,CAAA0M,GAAA,WAAA3I,CAAA;gBAAA,OAAAA,CAAA,CAAA+I,WAAA;cAAA,GAAA7C,MAAA,WAAAlG,CAAA;gBAAA,SAAAA,CAAA;cAAA;cACA8a,SAAA;cACA,IAAAD,WAAA,CAAAjP,MAAA;gBACAiP,WAAA,CAAAxV,OAAA,WAAAgR,UAAA;kBACAyE,SAAA,CAAAlW,IAAA,CAAA6V,OAAA,CAAArE,gBAAA,CAAAC,UAAA,EAAAzU,IAAA,WAAA0I,MAAA;oBAAA,OAAA4Q,eAAA,KACA7E,UAAA,EAAA/L,MAAA;kBAAA,CACA;gBACA;gBACAyQ,eAAA,GAAA1S,OAAA,CAAA8S,GAAA,CAAAL,SAAA,EAAAlZ,IAAA,WAAAwZ,MAAA;kBACA,OAAAtR,MAAA,CAAAC,MAAA,CAAAgJ,KAAA,CAAAjJ,MAAA,OAAAnF,MAAA,CAAA+I,kBAAA,CAAA0N,MAAA;gBACA;gBACAL,eAAA,CAAAnZ,IAAA,WAAAyZ,QAAA;kBACA,IAAAC,IAAA;kBAAA,IAAAC,MAAA,YAAAA,OAAA,EACA;oBACA,IAAAC,MAAA,GAAAf,OAAA,CAAAxe,iBAAA,CAAAmW,CAAA;oBACA,IAAAqJ,eAAA,GAAAJ,QAAA,CAAAG,MAAA,CAAAzS,WAAA;oBACA,IAAA2S,UAAA,GAAAjB,OAAA,CAAA7X,YAAA,CAAA4Y,MAAA,CAAA5N,eAAA;oBACA,IAAA8N,UAAA;sBACA,IAAAC,UAAA,GAAAD,UAAA,CAAAxX,KAAA,WAAA3C,OAAA;wBAAA,OAAAka,eAAA,CAAAjW,QAAA,CAAAjE,OAAA;sBAAA;sBACA,KAAAoa,UAAA;wBACAL,IAAA;wBAAA;sBAEA;sBACAE,MAAA,CAAArS,eAAA,GAAAuS,UAAA,CAAA7L,IAAA;oBACA;kBACA;kBAZA,SAAAuC,CAAA,MAAAA,CAAA,GAAAqI,OAAA,CAAAxe,iBAAA,CAAA2P,MAAA,EAAAwG,CAAA;oBAAA,IAAAmJ,MAAA,IAQA;kBAAA;kBAKA,KAAAD,IAAA;oBACAzH,UAAA;sBACA4G,OAAA,CAAAmB,MAAA;wBACAnH,iBAAA;sBACA;oBACA;kBACA;kBAEA6G,IAAA,IAAAX,WAAA;gBACA;cACA;gBACAF,OAAA,CAAAxe,iBAAA,CAAAoJ,OAAA,WAAAmW,MAAA;kBACA,IAAAE,UAAA,GAAAjB,OAAA,CAAA7X,YAAA,CAAA4Y,MAAA,CAAA5N,eAAA;kBACA,IAAA8N,UAAA;oBACAF,MAAA,CAAArS,eAAA,GAAAuS,UAAA,CAAA7L,IAAA;kBACA;gBACA;gBACA8K,WAAA;cACA;YAAA;YAAA;cAAA,OAAAM,UAAA,CAAAlZ,IAAA;UAAA;QAAA,GAAA2Y,SAAA;MAAA;IACA;IAEAmB,gBAAA,WAAAA,iBAAArb,IAAA,EAAA+K,GAAA;MAAA,IAAAuQ,OAAA;MACA,KAAA/e,KAAA,MAAA4H,MAAA,CAAAnE,IAAA;MACA,KAAA5D,gBAAA;MACA,KAAAE,MAAA;MACA,KAAAP,aAAA;MACA,KAAA4Z,SAAA,WAAAlD,CAAA;QACA6I,OAAA,CAAAvL,KAAA,YAAAwL,SAAA,CAAAvb,IAAA,QAAAA,IAAA,UAAA+K,GAAA,IAAAuQ,OAAA,CAAA7f,iBAAA;MACA;IACA;IACA+f,aAAA,WAAAA,cAAA;MACA,IAAA1W,GAAA;MACA,KAAAxJ,MAAA,CAAAuJ,OAAA,WAAA+J,OAAA,EAAAhL,GAAA;QACAgL,OAAA,CAAA6M,OAAA,IAAA7M,OAAA,CAAA6M,OAAA;QACA,IAAA7M,OAAA,CAAA6M,OAAA;UACA3W,GAAA,CAAAV,IAAA,CAAAwK,OAAA;QACA;MACA;MACA,KAAAnT,iBAAA,GAAAqJ,GAAA;MACA,SAAArJ,iBAAA,CAAA2P,MAAA,UAAA9P,MAAA,CAAA8P,MAAA;QACA,KAAA2E,KAAA,WAAA2L,iBAAA;MACA;MACA,SAAAjgB,iBAAA,CAAA2P,MAAA;QACA,KAAA2E,KAAA,WAAA2L,iBAAA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA3Y,OAAA,WAAAA,QAAA;MAAA,IAAA4Y,OAAA;MACA,IAAAC,WAAA,YAAAA,YAAA;QACA,IAAAC,GAAA,GAAAF,OAAA,CAAA5c,KAAA,GAAAhH,eAAA,GAAAN,eAAA;QACAokB,GAAA,KAAAza,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA6F,SAAA;YACA,IAAA4C,MAAA,GAAAzI,GAAA,CAAAC,IAAA;YACA,KAAAqa,OAAA,CAAA5c,KAAA;cACA+K,MAAA,GAAAA,MAAA,CACA3B,GAAA,WAAA3I,CAAA,EAAAoE,GAAA;gBACA;kBACAtC,IAAA,EAAA9B,CAAA,CAAAE,IAAA;kBACAoc,KAAA,EAAAtc,CAAA,CAAAE;gBACA;cACA;YACA;YACAic,OAAA,CAAAne,uBAAA,CAAAhF,IAAA,GAAAsR,MAAA;YACA6R,OAAA,CAAAhG,SAAA,WAAAlD,CAAA;cAAA,IAAAsJ,qBAAA;cACA,CAAAA,qBAAA,GAAAJ,OAAA,CAAA5L,KAAA,CAAAiM,uBAAA,cAAAD,qBAAA,eAAAA,qBAAA,CAAAE,iBAAA,CAAAnS,MAAA;YACA;UACA;YACA6R,OAAA,CAAA1X,QAAA;cACAC,OAAA,EAAA7C,GAAA,CAAAqG,OAAA;cACA1H,IAAA;YACA;UACA;QACA;MACA;MAEA4b,WAAA;IACA;IACA;IACAM,SAAA,WAAAA,UAAAnR,GAAA;MAAA,IAAAoR,OAAA;MACA,IAAAhL,GAAA;MACA,SAAApS,KAAA;QACAoS,GAAA,CAAAiL,OAAA,GAAArR,GAAA,CAAApH,qBAAA;MACA;QACAwN,GAAA,CAAAkL,OAAA,GAAAtR,GAAA,CAAAJ,iBAAA;MACA;MAEAvS,mBAAA;QAAAkkB,cAAA,EAAAnL,GAAA,CAAAkL;MAAA,GAAAjb,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA6F,SAAA;UACAiV,OAAA,CAAAI,aAAA,GAAAlb,GAAA,CAAAC,IAAA,IAAAkb,aAAA;UACAL,OAAA,CAAApjB,OAAA,GAAAsI,GAAA,CAAAC,IAAA,IAAAvI,OAAA;UACAojB,OAAA,CAAAnjB,WAAA,GAAAqI,GAAA,CAAAC,IAAA,IAAAmb,QAAA;UACAN,OAAA,CAAAljB,UAAA,GAAA8R,GAAA,CAAA2R,SAAA;UACAP,OAAA,CAAAjjB,eAAA,GAAA6R,GAAA,CAAA4R,cAAA;UACAR,OAAA,CAAAS,QAAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC,cAAA,WAAAA,eAAA3G,IAAA;MACA,KAAAvM,iBAAA,CAAAuM,IAAA;IACA;IACA4G,cAAA,WAAAA,eAAA;MACA,KAAA/M,KAAA,eAAAgN,WAAA;MACA,KAAAhN,KAAA,CAAAiN,MAAA,CAAAC,WAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,KAAA1hB,iBAAA;MACA,IAAAsb,GAAA;MACA,SAAAhY,KAAA;QACAgY,GAAA,CAAA3S,IAAA;MACA;QACA2S,GAAA,CAAA3S,IAAA;MACA;MAEA,IAAA4Y,MAAA,QAAAjN,KAAA,CAAAiN,MAAA;MACAA,MAAA,CAAAI,gBAAA;MACArG,GAAA,CAAAlS,OAAA,WAAA+J,OAAA,EAAAhL,GAAA;QACA,IAAA2U,MAAA,GAAAyE,MAAA,CAAAK,gBAAA,CAAAzO,OAAA;QACA,IAAAA,OAAA;UACA2J,MAAA,CAAA+E,OAAA,CAAAzY,OAAA,WAAA0Y,MAAA,EAAA3Z,GAAA;YACA2Z,MAAA,CAAA9B,OAAA,GAAA7X,GAAA,MAAAuZ,OAAA,CAAAhjB,SAAA,CAAAO,YAAA;UACA;QACA;QACA,IAAAkU,OAAA;UACA,IAAA2O,MAAA,GAAAhF,MAAA,CAAA+E,OAAA;UACAC,MAAA,CAAA/kB,IAAA,GAAA2kB,OAAA,CAAAhjB,SAAA,CAAAM,gBAAA;UACA8iB,MAAA,CAAA9B,OAAA;QACA;QACA,IAAA7M,OAAA,eAAAA,OAAA;UACA,IAAA2O,OAAA,GAAAhF,MAAA,CAAA+E,OAAA;UACAC,OAAA,CAAA/kB,IAAA,GAAA2kB,OAAA,CAAAhjB,SAAA,CAAAK,mBAAA;UACA+iB,OAAA,CAAA9B,OAAA;QACA;QACA,IAAA7M,OAAA,oBAAAA,OAAA;UACA,IAAA2O,QAAA,GAAAhF,MAAA,CAAA+E,OAAA;UACAC,QAAA,CAAA/kB,IAAA,GAAA2kB,OAAA,CAAAhjB,SAAA,CAAAI,aAAA;UACAgjB,QAAA,CAAA9B,OAAA;QACA;QACA,IAAA7M,OAAA;UACA,IAAA2O,QAAA,GAAAhF,MAAA,CAAA+E,OAAA;UACAC,QAAA,CAAA/kB,IAAA,GAAA2kB,OAAA,CAAAhjB,SAAA,CAAAC,WAAA;UACAmjB,QAAA,CAAA9B,OAAA;QACA;QACA,IAAA7M,OAAA;UACA,IAAA2O,QAAA,GAAAhF,MAAA,CAAA+E,OAAA;UACAC,QAAA,CAAA/kB,IAAA,GAAA2kB,OAAA,CAAAhjB,SAAA,CAAAE,QAAA;UACAkjB,QAAA,CAAA9B,OAAA;QACA;QACA,IAAA7M,OAAA;UACA,IAAA2O,QAAA,GAAAhF,MAAA,CAAA+E,OAAA;UACAC,QAAA,CAAA/kB,IAAA,GAAA2kB,OAAA,CAAAhjB,SAAA,CAAAG,WAAA;UACAijB,QAAA,CAAA9B,OAAA;QACA;MACA;MACAuB,MAAA,CAAAQ,UAAA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,KAAA;MAAA,IAAAH,MAAA,GAAAG,KAAA,CAAAH,MAAA;QAAAxS,GAAA,GAAA2S,KAAA,CAAA3S,GAAA;MACA,SAAA5Q,SAAA,CAAAO,YAAA;QACA;MACA;MACA,OAAAqQ,GAAA,CAAA4S,YAAA,WAAAxjB,SAAA,CAAAO,YAAA;IACA;IACAkjB,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAN,MAAA,GAAAM,KAAA,CAAAN,MAAA;QAAAxS,GAAA,GAAA8S,KAAA,CAAA9S,GAAA;MACA,SAAA5Q,SAAA,CAAAM,gBAAA,CAAAqjB,IAAA;QACA;MACA;MACA,IAAAC,aAAA,YAAAA,cAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAF,IAAA,GAAAzH,OAAA,cAAA3K,KAAA;MAAA;MACA,IAAAuS,SAAA,GAAAF,aAAA,MAAA5jB,SAAA,CAAAM,gBAAA;MACA,OAAAwjB,SAAA,CAAA5G,IAAA,WAAAlW,IAAA;QAAA,QAAA4J,GAAA,CAAAmT,IAAA,QAAAlZ,QAAA,CAAA7D,IAAA;MAAA;IACA;IAEAgd,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAb,MAAA,GAAAa,KAAA,CAAAb,MAAA;QAAAxS,GAAA,GAAAqT,KAAA,CAAArT,GAAA;MACA,SAAA5Q,SAAA,CAAAK,mBAAA;QACA;MACA;MACA,IAAAsK,GAAA,QAAA/F,KAAA;MACA,OAAAgM,GAAA,CAAAjG,GAAA,WAAA3K,SAAA,CAAAK,mBAAA;IACA;IACA6jB,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAf,MAAA,GAAAe,KAAA,CAAAf,MAAA;QAAAxS,GAAA,GAAAuT,KAAA,CAAAvT,GAAA;MACA,SAAA5Q,SAAA,CAAAI,aAAA,CAAAujB,IAAA;QACA;MACA;MAEA,IAAAC,aAAA,YAAAA,cAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAF,IAAA,GAAAzH,OAAA,cAAA3K,KAAA;MAAA;MAEA,IAAA5G,GAAA,QAAA/F,KAAA;MAEA,IAAAgY,GAAA,GAAAgH,aAAA,MAAA5jB,SAAA,CAAAI,aAAA;MAEA,SAAAI,SAAA;QACA,OAAAoc,GAAA,CAAAM,IAAA,WAAAlW,IAAA;UAAA,OAAA4J,GAAA,CAAAjG,GAAA,MAAA3D,IAAA;QAAA;MACA;QACA,SAAAyQ,CAAA,MAAAA,CAAA,GAAAmF,GAAA,CAAA3L,MAAA,EAAAwG,CAAA;UACA,IAAAtS,IAAA,GAAAyX,GAAA,CAAAnF,CAAA;UACA,IAAA7G,GAAA,CAAAjG,GAAA,EAAAE,QAAA,CAAA1F,IAAA;YACA;UACA;QACA;QACA;MACA;IACA;IACAif,mBAAA,WAAAA,oBAAAC,KAAA;MAAA,IAAAjB,MAAA,GAAAiB,KAAA,CAAAjB,MAAA;QAAAxS,GAAA,GAAAyT,KAAA,CAAAzT,GAAA;MACA,IAAAwS,MAAA,CAAA/kB,IAAA;QACA;MACA;MACA,OAAAuS,GAAA,CAAAhG,YAAA,KAAAwY,MAAA,CAAA/kB,IAAA;IACA;IACAimB,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAnB,MAAA,GAAAmB,KAAA,CAAAnB,MAAA;QAAAxS,GAAA,GAAA2T,KAAA,CAAA3T,GAAA;MACA,IAAAwS,MAAA,CAAA/kB,IAAA;QACA;MACA;MACA,OAAAuS,GAAA,CAAA7F,SAAA,KAAAqY,MAAA,CAAA/kB,IAAA;IACA;IACAmmB,mBAAA,WAAAA,oBAAAC,KAAA;MAAA,IAAArB,MAAA,GAAAqB,KAAA,CAAArB,MAAA;QAAAxS,GAAA,GAAA6T,KAAA,CAAA7T,GAAA;MACA,IAAAwS,MAAA,CAAA/kB,IAAA;QACA;MACA;MACA,OAAAuS,GAAA,CAAA9F,gBAAA,KAAAsY,MAAA,CAAA/kB,IAAA;IACA;IACAqmB,mBAAA,WAAAA,oBAAAC,CAAA;MAAA,IAAAC,WAAA;MACA,CAAAA,WAAA,QAAAhP,KAAA,cAAAgP,WAAA,eAAAA,WAAA,CAAA/C,uBAAA,CAAAgD,SAAA,CAAAF,CAAA;IACA;IACAG,wBAAA,WAAAA,yBAAAhY,EAAA;MAAA,IAAAiY,OAAA;MACA,UAAA9hB,MAAA;QACA,KAAAF,iBAAA;QACA,KAAAe,WAAA;MACA;QACA,KAAAA,WAAA;QACApG,wBAAA;UAAA2Z,OAAA,OAAApU;QAAA,GAAAgE,IAAA,WAAAC,GAAA;UACA6d,OAAA,CAAAhiB,iBAAA,GAAAmE,GAAA,CAAAC,IAAA;UACA,IAAA4d,OAAA,CAAAhiB,iBAAA,CAAAkO,MAAA;YACA8T,OAAA,CAAArkB,UAAA,CAAAI,cAAA,GAAAikB,OAAA,CAAAhiB,iBAAA,IAAAuC,EAAA;UACA;UACAyf,OAAA,CAAAjhB,WAAA;QACA;MACA;IACA;IACAkhB,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,UAAA9jB,MAAA,CAAA8P,MAAA;QACA,KAAA2E,KAAA,eAAAgN,WAAA;QACA,KAAAhN,KAAA,CAAAiN,MAAA,CAAAC,WAAA;QACA;MACA;MACA,KAAAjJ,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAlU,IAAA;MACA,GAAAoB,IAAA;QACAge,OAAA,CAAA9jB,MAAA;QACA8jB,OAAA,CAAAtC,cAAA;MACA,GAAA1I,KAAA;QACAgL,OAAA,CAAAnb,QAAA;UACAjE,IAAA;UACAkE,OAAA;QACA;MACA;IACA;IACAmb,mBAAA,WAAAA,oBAAAtU,GAAA;MACA,SAAAlL,MAAA;QACA,SAAAkL,GAAA,CAAApH,qBAAA;MACA;QACA,aAAA3E,MAAA,IAAA+L,GAAA,CAAAH,IAAA;MACA;IACA;IACA0U,YAAA,WAAAA,aAAA;MACA,UAAAhkB,MAAA,CAAA8P,MAAA;QACA,KAAAnH,QAAA;UACAC,OAAA;UACAlE,IAAA;QACA;QACA;MACA;MACA,KAAA+P,KAAA,CAAAiN,MAAA,CAAAuC,UAAA;QACAC,QAAA,KAAArb,MAAA,MAAA/F,QAAA,mBAAA+F,MAAA,MAAAtJ,UAAA,CAAAC,cAAA,OAAAqJ,MAAA,MAAA/F,QAAA;QACA4B,IAAA;QACAxH,IAAA,OAAA8C;MACA;IACA;IACAmkB,iBAAA,WAAAA,kBAAA;MACA,KAAAhnB,MAAA;IACA;IACAmkB,QAAA,WAAAA,SAAA;MACA,KAAAjkB,SAAA,GAAA1B,MAAA;MACA,KAAA4B,SAAA,MAAAsL,MAAA,CACA,KAAAjD,UAAA,oCAAAiD,MAAA,CACAhM,OAAA,eAAAgM,MAAA,CAAA1B,YAAA,CAAAC,OAAA,CACA,OACA,gBAAAyB,MAAA,CAAA1B,YAAA,CAAAC,OAAA;MACA,KAAAjK,MAAA;IACA;IACAinB,YAAA,WAAAA,aAAA;MACA,IAAAlD,aAAA,QAAAD,aAAA;MACA,IAAAxjB,OAAA,QAAAA,OAAA;MACA,KAAAF,SAAA,MAAAsL,MAAA,CACA,KAAAjD,UAAA,oCAAAiD,MAAA,CACAhM,OAAA,eAAAgM,MAAA,CAAA1B,YAAA,CAAAC,OAAA,CACA,OACA,gBAAAyB,MAAA,CAAA1B,YAAA,CAAAC,OAAA;MACA,KAAA9J,YAAA,GAAA4jB,aAAA;MACA,KAAA1jB,SAAA,GAAAC,OAAA;IACA;IACA4mB,UAAA,WAAAA,WAAAngB,CAAA;MACA,KAAAogB,WAAA,MAAAzb,MAAA,CACA,KAAAjD,UAAA,oCAAAiD,MAAA,CACAhM,OAAA,eAAAgM,MAAA,CAAA1B,YAAA,CAAAC,OAAA,CACA,OACA,gBAAAyB,MAAA,CAAA1B,YAAA,CAAAC,OAAA;MACA,KAAAhK,UAAA;IACA;IACAwK,aAAA,WAAAA,cAAA2c,KAAA;MAAA,IAAArnB,IAAA,GAAAqnB,KAAA,CAAArnB,IAAA;MACA,IAAAA,IAAA,CAAAwH,IAAA;QACAoD,OAAA,CAAAC,GAAA,SAAA7K,IAAA;QACA4K,OAAA,CAAA0c,KAAA,CACA,sBACAtnB,IAAA,CAAAA,IAAA,CAAAunB,QAAA,EAAAC,OAAA,CACAxnB,IAAA,CAAAA,IAAA,CAAAunB,QAAA,CACA;QACA,IAAAvnB,IAAA,CAAAA,IAAA,CAAAunB,QAAA;UACAE,QAAA,CAAAC,cAAA,UAAAC,aAAA,CAAAC,WAAA,CACA;YACApgB,IAAA;YACAqgB,IAAA;YACAtgB,KAAA;cACA;cACAugB,KAAA,OAAAvnB,OAAA;cACAoE,SAAA,OAAAjE,eAAA;cACAqnB,SAAA,OAAAtnB,UAAA;cACAunB,OAAA,OAAAxnB,WAAA;cACAynB,aAAA;cACAC,MAAA;cACA;cACA;cACA;YACA;UACA,GACA,GACA;QACA,WAAAloB,IAAA,CAAAA,IAAA,CAAAunB,QAAA;UACAE,QAAA,CAAAC,cAAA,cAAAC,aAAA,CAAAC,WAAA,CACA;YACApgB,IAAA;YACAqgB,IAAA;YACAtgB,KAAA;cACA;cACAugB,KAAA,OAAAvnB,OAAA;cACAoE,SAAA,OAAAjE,eAAA;cACAqnB,SAAA,OAAAtnB,UAAA;cACAunB,OAAA,OAAAxnB,WAAA;cACAynB,aAAA;cACAC,MAAA;cACA;cACA;YACA;UACA,GACA,GACA;QACA;MACA;IACA;EAAA;AAEA", "ignoreList": []}]}
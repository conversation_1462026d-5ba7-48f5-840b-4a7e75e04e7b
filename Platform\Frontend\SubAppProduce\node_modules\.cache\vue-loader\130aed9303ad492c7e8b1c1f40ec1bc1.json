{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\batch-tracking\\index.vue?vue&type=style&index=0&id=153f7c1c&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\batch-tracking\\index.vue", "mtime": 1757468113496}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5jb250YWluZXIgew0KICBwYWRkaW5nOiAxNnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiB1bnNldDsNCn0NCg0KLmZpbHRlci1iYXIgew0KICBwYWRkaW5nLXRvcDogMTZweDsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCn0NCg0KLm1haW4tY29udGVudCB7DQogIHBhZGRpbmc6IDE2cHggMTZweCAwIDE2cHg7DQogIGZsZXg6IDE7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQoNCiAgLnRiLXggew0KICAgIGZsZXg6IDE7DQogICAgaGVpZ2h0OiAwOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogIH0NCg0KICAucGFnaW5hdGlvbi1jb250YWluZXIgew0KICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgIHBhZGRpbmc6IDE2cHg7DQogICAgbWFyZ2luOiAwOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyeA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/quality_Inspection/batch-tracking", "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n    <div class=\"filter-bar\">\r\n      <el-form ref=\"formRef\" :model=\"form\" label-width=\"80px\" inline>\r\n        <el-form-item label=\"项目名称\" prop=\"Project_Id\">\r\n          <el-select v-model=\"form.Project_Id\" placeholder=\"请选择\" clearable @change=\"projectChange\">\r\n            <el-option\r\n              v-for=\"item in projectOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Short_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n\r\n        </el-form-item>\r\n        <el-form-item label=\"区域\" prop=\"Area_Id\">\r\n          <el-tree-select\r\n            ref=\"treeSelectArea\"\r\n            v-model=\"form.Area_Id\"\r\n            :disabled=\"!form.Project_Id\"\r\n            :select-params=\"{\r\n              clearable: true,\r\n            }\"\r\n            class=\"cs-tree-x w100\"\r\n            :tree-params=\"treeParamsArea\"\r\n            @select-clear=\"areaClear\"\r\n            @node-click=\"areaChange\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"批次\" prop=\"InstallUnit_Id\">\r\n          <el-select v-model=\"form.InstallUnit_Id\" clearable placeholder=\"请选择\" :disabled=\"!form.Area_Id\">\r\n            <el-option v-for=\"option in installOption\" :key=\"option.Id\" :label=\"option.Name\" :value=\"option.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作车间\" prop=\"Workshop_Id\">\r\n          <el-select v-model=\"form.Workshop_Id\" clearable class=\"w100\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"item in workshopOptions\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Display_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"收图日期\">\r\n          <el-date-picker\r\n            v-model=\"dateRange\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button :disabled=\"tbLoading\" @click=\"reset\">重置</el-button>\r\n          <el-button type=\"primary\" :disabled=\"tbLoading\" @click=\"search\">搜索</el-button>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n    </div>\r\n\r\n    <div class=\"main-content\">\r\n      <vxe-toolbar>\r\n        <template #buttons>\r\n          <el-button type=\"success\" :loading=\"exportLoading\" @click=\"exportData\">导出数据</el-button>\r\n        </template>\r\n      </vxe-toolbar>\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          :data=\"tableData\"\r\n          class=\"cs-vxe-table\"\r\n          :row-config=\"{ isCurrent: true, isHover: true }\"\r\n          align=\"left\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          stripe\r\n          size=\"medium\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          :edit-config=\"{\r\n            trigger: 'click',\r\n            mode: 'cell',\r\n            showIcon: true\r\n          }\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n        >\r\n          <template v-for=\"(item) in columns\">\r\n            <vxe-column\r\n              :key=\"item.Code\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n              :align=\"item.Align\"\r\n              :edit-render=\"item.Is_Edit ? {} : null\"\r\n            >\r\n              <template v-if=\"item.Is_Edit\" #edit=\"{ row }\">\r\n                <template v-if=\"['Drawing_Receive_Date'].includes(item.Code)\">\r\n                  <vxe-input\r\n                    v-model=\"row[item.Code]\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    @change=\"saveRow(row, item.Code)\"\r\n                  />\r\n                </template>\r\n                <template v-else>\r\n                  <vxe-input\r\n                    v-model=\"row[item.Code]\"\r\n                    placeholder=\"请输入\"\r\n                    @blur=\"saveRow(row, item.Code)\"\r\n                  />\r\n                </template>\r\n              </template>\r\n              <template #default=\"{ row }\">\r\n                <span> {{ row[item.Code] }}</span>\r\n              </template>\r\n            </vxe-column>\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n      <Pagination\r\n        :total=\"total\"\r\n        :page-sizes=\"tablePageSize\"\r\n        :page.sync=\"queryInfo.Page\"\r\n        :limit.sync=\"queryInfo.PageSize\"\r\n        class=\"pagination\"\r\n        @pagination=\"pageChange\"\r\n      />\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetInstallUnitPageList } from '@/api/PRO/install-unit'\r\nimport { GeAreaTrees, GetProjectPageList } from '@/api/PRO/project'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport {\r\n  BatchUpdateTrackEditableFields,\r\n  ExportstBatchTrackFromSourceAsync,\r\n  InsertBatchTrack,\r\n  SaveQIReportData\r\n} from '@/api/PRO/qualityInspect/quality-management'\r\nimport { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'\r\nimport { combineURL } from '@/utils'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport moment from 'moment'\r\n\r\nexport default {\r\n  name: 'PROBatchTracking1',\r\n  components: { DynamicTableFields, Pagination },\r\n  mixins: [getTbInfo],\r\n  data() {\r\n    return {\r\n      dateRange: '',\r\n      dialogVisible: false,\r\n      exportLoading: false,\r\n      gridCode: '',\r\n      currentComponent: '',\r\n      title: '',\r\n      currentRow: null,\r\n      form: {\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        InstallUnit_Id: '',\r\n        Workshop_Id: '',\r\n        DrawingReceiveDateStart: '',\r\n        DrawingReceiveDateEnd: ''\r\n      },\r\n\r\n      projectOption: [], // Define options as needed\r\n      treeParamsArea: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      multipleSelection: [],\r\n      areaOption: [],\r\n      installOption: [],\r\n      workshopOptions: [],\r\n      columns: [],\r\n      tableData: [],\r\n      customColumns: [],\r\n      tbLoading: false,\r\n      tablePageSize: tablePageSize,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      total: 0,\r\n      typeList: []\r\n    }\r\n  },\r\n  watch: {\r\n    dateRange: {\r\n      handler(newValue) {\r\n        if (newValue) {\r\n          const [start, end] = newValue\r\n          this.form.DrawingReceiveDateStart = start\r\n          this.form.DrawingReceiveDateEnd = end\r\n        } else {\r\n          this.form.DrawingReceiveDateStart = ''\r\n          this.form.DrawingReceiveDateEnd = ''\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  async created() {\r\n    try {\r\n      this.tbLoading = true\r\n      this.gridCode = 'PROBatchTracking'\r\n      this.initColumn()\r\n      await this.getProjectOption()\r\n      this.getWorkShop()\r\n      this.fetchData(1)\r\n    } catch (error) {\r\n      this.tbLoading = false\r\n      console.error('Error during created lifecycle:', error)\r\n    }\r\n  },\r\n  mounted() {\r\n  },\r\n  methods: {\r\n    async initColumn() {\r\n      await this.getTableConfig(this.gridCode, true)\r\n      const res = await GetPartTypeList({ Part_Grade: 0 })\r\n      this.typeList = res.Data\r\n      this.columns = this.columns.filter(v => v.Is_Display)\r\n      const expandedColumns = []\r\n      this.typeList.forEach(item => {\r\n        // 添加数量列\r\n        expandedColumns.push({\r\n          Code: `${item.Code}_Count`,\r\n          Display_Name: `${item.Name}数量`,\r\n          Is_Display: true,\r\n          Is_Edit: false,\r\n          Is_Frozen: false,\r\n          Width: 150\r\n        })\r\n        // 添加重量列\r\n        expandedColumns.push({\r\n          Code: `${item.Code}_Weight`,\r\n          Display_Name: `${item.Name}重量(kg)`,\r\n          Is_Display: true,\r\n          Is_Edit: false,\r\n          Is_Frozen: false,\r\n          Width: 150\r\n        })\r\n      })\r\n      this.columns.splice(5, 0, ...expandedColumns)\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    fetchData(page) {\r\n      this.tbLoading = true\r\n      page && (this.queryInfo.Page = page)\r\n      InsertBatchTrack({\r\n        QueryParams: this.form,\r\n        PageInfo: this.queryInfo\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tableData = res.Data.Data.map(item => {\r\n            item.Drawing_Receive_Date = item.Drawing_Receive_Date ? moment(item.Drawing_Receive_Date).format('YYYY-MM-DD') : ''\r\n            return item\r\n          })\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    getWorkShop() {\r\n      GetWorkshopPageList({ Page: 1, PageSize: -1 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res?.Data?.Data) {\r\n            this.workshopOptions = []\r\n          }\r\n          this.workshopOptions = res.Data.Data.map(item => {\r\n            return {\r\n              Id: item.Id,\r\n              Display_Name: item.Display_Name\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    reset() {\r\n      this.$refs.formRef.resetFields()\r\n      this.form.Project_Id = ''\r\n      this.dateRange = ''\r\n      this.form.DrawingReceiveDateStart = ''\r\n      this.form.DrawingReceiveDateEnd = ''\r\n      this.fetchData(1)\r\n    },\r\n    search() {\r\n      this.fetchData(1)\r\n    },\r\n    getProjectOption() {\r\n      return new Promise((resolve, reject) => {\r\n        GetProjectPageList({\r\n          Page: 1,\r\n          PageSize: -1\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.projectOption = res.Data?.Data || []\r\n            if (this.projectOption.length) {\r\n              this.form.Project_Id = ''\r\n              this.getAreaList()\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        })\r\n      })\r\n    },\r\n    projectChange(e) {\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Id = ''\r\n      this.treeParamsArea.data = []\r\n      this.installOption = []\r\n      this.form.InstallUnit_Id = ''\r\n      if (e) {\r\n        this.getAreaList()\r\n      }\r\n    },\r\n\r\n    areaClear() {\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Id = ''\r\n    },\r\n    getAreaList() {\r\n      if (!this.form.Project_Id) {\r\n        this.$refs.treeSelectArea?.treeDataUpdateFun([])\r\n        return\r\n      }\r\n      GeAreaTrees({\r\n        projectId: this.form.Project_Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsArea.data = tree\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectArea?.treeDataUpdateFun(tree)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (Children && Children.length) {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    areaChange() {\r\n      this.form.InstallUnit_Id = ''\r\n      this.getInstall()\r\n    },\r\n    getInstall() {\r\n      GetInstallUnitPageList({\r\n        Area_Id: this.form.Area_Id,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.installOption = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    exportData() {\r\n      this.exportLoading = true\r\n      ExportstBatchTrackFromSourceAsync({\r\n        QueryParams: this.form\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '导出成功',\r\n            type: 'success'\r\n          })\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.exportLoading = false\r\n      })\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n\r\n    handleSubmit(formData) {\r\n      // Call the API to save the updated data\r\n      this.tbLoading = true\r\n      SaveQIReportData(formData).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.handleClose()\r\n          this.fetchData(1)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message || '保存失败',\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).catch(error => {\r\n        this.$message({\r\n          message: '保存失败: ' + error,\r\n          type: 'error'\r\n        })\r\n      }).finally(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n\r\n    // 重新保存改行\r\n    saveRow(row, fieldCode) {\r\n      BatchUpdateTrackEditableFields({\r\n        items: [{\r\n          project_Id: row.Project_Id,\r\n          area_Id: row.Area_Id,\r\n          installUnit_Id: row.InstallUnit_Id,\r\n          workshop_id: row.Workshop_Id,\r\n          structure_Type: row.Structure_Type,\r\n          drawing_Receive_Date: row.Drawing_Receive_Date,\r\n          conclusion: row.Conclusion\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: unset;\r\n}\r\n\r\n.filter-bar {\r\n  padding-top: 16px;\r\n  box-sizing: border-box;\r\n  margin-bottom: 16px;\r\n  background-color: #fff;\r\n}\r\n\r\n.main-content {\r\n  padding: 16px 16px 0 16px;\r\n  flex: 1;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .tb-x {\r\n    flex: 1;\r\n    height: 0;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .pagination-container {\r\n    text-align: right;\r\n    padding: 16px;\r\n    margin: 0;\r\n  }\r\n}\r\n</style>\r\n"]}]}
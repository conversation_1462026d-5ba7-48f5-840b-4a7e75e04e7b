{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\draft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\draft.vue", "mtime": 1758242836212}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7IGNsb3NlVGFnVmlldywgZGVib3VuY2UgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IEJhdGNoUHJvY2Vzc0FkanVzdCBmcm9tICcuL2NvbXBvbmVudHMvQmF0Y2hQcm9jZXNzQWRqdXN0Jw0KaW1wb3J0IHsNCiAgR2V0Q2FuU2NoZHVsaW5nUGFydExpc3QsDQogIEdldENvbXBTY2hkdWxpbmdJbmZvRGV0YWlsLCBHZXREd2csDQogIEdldFBhcnRTY2hkdWxpbmdJbmZvRGV0YWlsLA0KICBHZXRTY2hkdWxpbmdXb3JraW5nVGVhbXMsDQogIFNhdmVDb21wb25lbnRTY2hlZHVsaW5nV29ya3Nob3AsDQogIFNhdmVDb21wU2NoZHVsaW5nRHJhZnQsDQogIFNhdmVQYXJ0U2NoZHVsaW5nRHJhZnROZXcsDQogIFNhdmVQYXJ0U2NoZWR1bGluZ1dvcmtzaG9wTmV3LA0KICBTYXZlU2NoZHVsaW5nVGFza0J5SWQNCn0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCB7IEdldFN0b3BMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCBBZGREcmFmdCBmcm9tICcuL2NvbXBvbmVudHMvYWRkRHJhZnQnDQppbXBvcnQgT3duZXJQcm9jZXNzIGZyb20gJy4vY29tcG9uZW50cy9Pd25lclByb2Nlc3MnDQppbXBvcnQgV29ya3Nob3AgZnJvbSAnLi9jb21wb25lbnRzL1dvcmtzaG9wLnZ1ZScNCmltcG9ydCB7IEdldEdyaWRCeUNvZGUgfSBmcm9tICdAL2FwaS9zeXMnDQppbXBvcnQgeyBnZXRVbmlxdWUsIHVuaXF1ZUNvZGUgfSBmcm9tICcuL2NvbnN0YW50Jw0KaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSAndXVpZCcNCmltcG9ydCBudW1lcmFsIGZyb20gJ251bWVyYWwnDQppbXBvcnQgeyBHZXRMaWJMaXN0VHlwZSwgR2V0UHJvY2Vzc0Zsb3dMaXN0V2l0aFRlY2hub2xvZ3ksIEdldFByb2Nlc3NMaXN0QmFzZSB9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYicNCmltcG9ydCB7IEFyZWFHZXRFbnRpdHkgfSBmcm9tICdAL2FwaS9wbG0vcHJvamVjdHMnDQppbXBvcnQgeyBtYXBBY3Rpb25zLCBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCmltcG9ydCB7IEdldFBhcnRUeXBlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wYXJ0VHlwZScNCmltcG9ydCBtb21lbnQgZnJvbSAnbW9tZW50Jw0KaW1wb3J0IEV4cGFuZGFibGVTZWN0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9FeHBhbmRhYmxlU2VjdGlvbi9pbmRleC52dWUnDQppbXBvcnQgVHJlZURldGFpbCBmcm9tICdAL2NvbXBvbmVudHMvVHJlZURldGFpbC9pbmRleC52dWUnDQppbXBvcnQgeyBHZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QsIEdldFByb2plY3RBcmVhVHJlZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCcNCg0KaW1wb3J0IHsgR2V0Q29tcFR5cGVUcmVlIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycNCmltcG9ydCB7IHBhcnNlT3NzVXJsIH0gZnJvbSAnQC91dGlscy9maWxlJw0KaW1wb3J0IER5bmFtaWNUYWJsZUZpZWxkcyBmcm9tICdAL2NvbXBvbmVudHMvRHluYW1pY1RhYmxlRmllbGRzL2luZGV4LnZ1ZScNCg0KaW1wb3J0IHsgZ2V0Q29uZmlndXJlIH0gZnJvbSAnQC9hcGkvdXNlcicNCmltcG9ydCB7IGJhc2VVcmwgfSBmcm9tICdAL3V0aWxzL2Jhc2V1cmwnDQppbXBvcnQgeyBHZXRTdGVlbENhZEFuZEJpbUlkIH0gZnJvbSAnQC9hcGkvUFJPL2NvbXBvbmVudCcNCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycNCmNvbnN0IFNQTElUX1NZTUJPTCA9ICckXyQnDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsgRHluYW1pY1RhYmxlRmllbGRzLCBUcmVlRGV0YWlsLCBFeHBhbmRhYmxlU2VjdGlvbiwgQmF0Y2hQcm9jZXNzQWRqdXN0LCBBZGREcmFmdCwgV29ya3Nob3AsIE93bmVyUHJvY2VzcyB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBkcmF3ZXI6IGZhbHNlLA0KICAgICAgZHJhd2Vyc3VsbDogZmFsc2UsDQogICAgICBpZnJhbWVLZXk6ICcnLA0KICAgICAgZnVsbHNjcmVlbmlkOiAnJywNCiAgICAgIGlmcmFtZVVybDogJycsDQogICAgICBmdWxsYmltaWQ6ICcnLA0KICAgICAgZmlsZUJpbTogJycsDQogICAgICBJc1VwbG9hZENhZDogZmFsc2UsDQogICAgICBjYWRSb3dDb2RlOiAnJywNCiAgICAgIGNhZFJvd1Byb2plY3RJZDogJycsDQogICAgICB0YktleTogMTAwLA0KICAgICAgaXNDb21wb25lbnRPcHRpb25zOiBbDQogICAgICAgIHsgbGFiZWw6ICfmmK8nLCB2YWx1ZTogZmFsc2UgfSwNCiAgICAgICAgeyBsYWJlbDogJ+WQpicsIHZhbHVlOiB0cnVlIH0NCiAgICAgIF0sDQogICAgICBzcGVjT3B0aW9uczogW3sgZGF0YTogJycgfV0sDQogICAgICBmaWx0ZXJUeXBlT3B0aW9uOiBbeyBkYXRhOiAnJyB9XSwNCiAgICAgIGZpbHRlckNvZGVPcHRpb246IFt7IGRhdGE6ICcnIH1dLA0KICAgICAgcHJvamVjdE9wdGlvbnM6IFt7IGRhdGE6ICcnIH1dLA0KICAgICAgYXJlYU9wdGlvbnM6IFt7IGRhdGE6ICcnIH1dLA0KICAgICAgaW5zdGFsbE9wdGlvbnM6IFt7IGRhdGE6ICcnIH1dLA0KICAgICAgcHJvamVjdExpc3Q6IFtdLA0KICAgICAgaW5zdGFsbExpc3Q6IFtdLA0KICAgICAgYXJlYUxpc3Q6IFtdLA0KICAgICAgcGlja2VyT3B0aW9uczogew0KICAgICAgICBkaXNhYmxlZERhdGUodGltZSkgew0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgaW5uZXJGb3JtOiB7DQogICAgICAgIHByb2plY3ROYW1lOiAnJywNCiAgICAgICAgYXJlYU5hbWU6ICcnLA0KICAgICAgICBpbnN0YWxsTmFtZTogJycsDQogICAgICAgIHNlYXJjaENvbnRlbnQ6ICcnLA0KICAgICAgICBzZWFyY2hDb21UeXBlU2VhcmNoOiAnJywNCiAgICAgICAgc2VhcmNoU3BlY1NlYXJjaDogJycsDQogICAgICAgIHNlYXJjaERpcmVjdDogJycNCiAgICAgIH0sDQogICAgICBjdXJTZWFyY2g6IDEsDQogICAgICBzZWFyY2hUeXBlOiAnJywNCiAgICAgIGZvcm1JbmxpbmU6IHsNCiAgICAgICAgU2NoZHVsaW5nX0NvZGU6ICcnLA0KICAgICAgICBDcmVhdGVfVXNlck5hbWU6ICcnLA0KICAgICAgICBGaW5pc2hfRGF0ZTogJycsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiAnJywNCiAgICAgICAgUmVtYXJrOiAnJw0KICAgICAgfSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgY3VycmVudElkczogJycsDQogICAgICBjb2x1bW5zOiBbXSwNCiAgICAgIHRiRGF0YTogW10sDQogICAgICB0YkNvbmZpZzoge30sDQogICAgICBUb3RhbENvdW50OiAwLA0KICAgICAgbXVsdGlwbGVTZWxlY3Rpb246IFtdLA0KICAgICAgc2hvd0V4cGFuZDogdHJ1ZSwNCiAgICAgIHBnTG9hZGluZzogZmFsc2UsDQogICAgICBkZWxldGVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHdvcmtTaG9wSXNPcGVuOiBmYWxzZSwNCiAgICAgIGlzT3duZXJOdWxsOiBmYWxzZSwNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgb3BlbkFkZERyYWZ0OiBmYWxzZSwNCiAgICAgIHNhdmVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBpc0NoZWNrQWxsOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLA0KICAgICAgZ3JpZENvZGU6ICcnLA0KICAgICAgZFdpZHRoOiAnMjUlJywNCiAgICAgIHRpdGxlOiAnJywNCiAgICAgIHNlYXJjaDogKCkgPT4gKHt9KSwNCiAgICAgIHBhZ2VUeXBlOiB1bmRlZmluZWQsDQogICAgICB0aXBMYWJlbDogJycsDQogICAgICB0ZWNobm9sb2d5T3B0aW9uOiBbXSwNCiAgICAgIHR5cGVPcHRpb246IFtdLA0KICAgICAgd29ya2luZ1RlYW06IFtdLA0KICAgICAgcGFnZVN0YXR1czogdW5kZWZpbmVkLA0KICAgICAgc2NoZWR1bGVJZDogJycsDQogICAgICBwYXJ0Q29tT3duZXJDb2x1bW46IG51bGwsDQoNCiAgICAgIGluc3RhbGxVbml0SWRMaXN0OiBbXSwNCiAgICAgIHByb2plY3RJZDogJycsDQogICAgICBhcmVhSWQ6ICcnLA0KICAgICAgcHJvamVjdE5hbWU6ICcnLA0KICAgICAgc3RhdHVzVHlwZTogJycsDQogICAgICBleHBhbmRlZEtleTogJycsDQogICAgICAvLyB0cmVlTG9hZGluZzogZmFsc2UsDQogICAgICB0cmVlRGF0YTogW10sDQogICAgICB0cmVlUGFyYW1zQ29tcG9uZW50VHlwZTogew0KICAgICAgICAnZGVmYXVsdC1leHBhbmQtYWxsJzogdHJ1ZSwNCiAgICAgICAgJ2NoZWNrLXN0cmljdGx5JzogdHJ1ZSwNCiAgICAgICAgZmlsdGVyYWJsZTogdHJ1ZSwNCiAgICAgICAgY2xpY2tQYXJlbnQ6IHRydWUsDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICBwcm9wczogew0KICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLA0KICAgICAgICAgIGxhYmVsOiAnTGFiZWwnLA0KICAgICAgICAgIHZhbHVlOiAnRGF0YScNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIHRyZWVTZWxlY3RQYXJhbXM6IHsNCiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLA0KICAgICAgICBjb2xsYXBzZVRhZ3M6IHRydWUsDQogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQ0KICAgICAgfSwNCiAgICAgIGRpc2FibGVkQWRkOiB0cnVlLA0KICAgICAgcHJvamVjdE9wdGlvbjogW10sDQogICAgICBjb21OYW1lOiAnJywNCiAgICAgIHBhcnROYW1lOiAnJywNCiAgICAgIGJvbUxpc3Q6IFtdDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgICd0YkRhdGEubGVuZ3RoJzogew0KICAgICAgaGFuZGxlcihuLCBvKSB7DQogICAgICAgIHRoaXMuY2hlY2tPd25lcigpDQogICAgICAgIHRoaXMuZG9GaWx0ZXIoKQ0KICAgICAgfSwNCiAgICAgIGltbWVkaWF0ZTogZmFsc2UNCiAgICB9DQogIH0sDQoNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc0NvbSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSAnY29tJw0KICAgIH0sDQogICAgaXNWaWV3KCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVN0YXR1cyA9PT0gJ3ZpZXcnDQogICAgfSwNCiAgICBpc0VkaXQoKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlU3RhdHVzID09PSAnZWRpdCcNCiAgICB9LA0KICAgIGlzQWRkKCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVN0YXR1cyA9PT0gJ2FkZCcNCiAgICB9LA0KICAgIGFkZERyYWZ0S2V5KCkgew0KICAgICAgcmV0dXJuIHRoaXMuZXhwYW5kZWRLZXkgKyB0aGlzLmZvcm1JbmxpbmUuSW5zdGFsbFVuaXRfSWQNCiAgICB9LA0KICAgIC8vIGZpbHRlclRleHQoKSB7DQogICAgLy8gICByZXR1cm4gdGhpcy5wcm9qZWN0TmFtZSArIFNQTElUX1NZTUJPTCArIHRoaXMuc3RhdHVzVHlwZQ0KICAgIC8vIH0sDQogICAgc3RhdHVzQ29kZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLmlzQ29tID8gJ0NvbXBfU2NoZHVsZV9TdGF0dXMnIDogJ1BhcnRfU2NoZHVsZV9TdGF0dXMnDQogICAgfSwNCiAgICBpbnN0YWxsTmFtZSgpIHsNCiAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmluc3RhbGxVbml0SWRMaXN0LmZpbmQodiA9PiB2LklkID09PSB0aGlzLmZvcm1JbmxpbmUuSW5zdGFsbFVuaXRfSWQpDQogICAgICBpZiAoaXRlbSkgew0KICAgICAgICByZXR1cm4gaXRlbS5OYW1lDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJycNCiAgICAgIH0NCiAgICB9LA0KICAgIGlzUGFydFByZXBhcmUoKSB7DQogICAgICByZXR1cm4gdGhpcy5nZXRJc1BhcnRQcmVwYXJlICYmICF0aGlzLmlzQ29tDQogICAgfSwNCiAgICBpc05lc3QoKSB7DQogICAgICByZXR1cm4gdGhpcy4kcm91dGUucXVlcnkudHlwZSA9PT0gJzEnDQogICAgfSwNCiAgICBoYXNDcmFmdCgpIHsNCiAgICAgIHJldHVybiAhIXRoaXMuaXNWZXJzaW9uRm91cg0KICAgIH0sDQogICAgaGFzVW5pdFBhcnQoKSB7DQogICAgICByZXR1cm4gISF0aGlzLmlzVmVyc2lvbkZvdXINCiAgICB9LA0KICAgIC4uLm1hcEdldHRlcnMoJ3RlbmFudCcsIFsnaXNWZXJzaW9uRm91ciddKSwNCiAgICAuLi5tYXBHZXR0ZXJzKCdmYWN0b3J5SW5mbycsIFsnd29ya3Nob3BFbmFibGVkJywgJ2dldElzUGFydFByZXBhcmUnXSksDQogICAgLi4ubWFwR2V0dGVycygnc2NoZWR1bGUnLCBbJ3Byb2Nlc3NMaXN0JywgJ25lc3RJZHMnXSkNCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHsNCiAgICAgIC8vIHRoaXMuYmFzZUNhZFVybCA9ICdodHRwOi8vbG9jYWxob3N0Ojk1MjknDQogICAgICAvLyB0aGlzLmJhc2VDYWRVcmwgPSAnaHR0cDovL2dsZW5kYWxlLW1vZGVsLmJpbXRrLmNvbScNCiAgICAgIHRoaXMuYmFzZUNhZFVybCA9ICdodHRwOi8vZ2xlbmRhbGUtbW9kZWwtZGV2LmJpbXRrLnRlY2gnDQogICAgfSBlbHNlIHsNCiAgICAgIGdldENvbmZpZ3VyZSh7IGNvZGU6ICdnbGVuZGFsZV91cmwnIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmJhc2VDYWRVcmwgPSByZXMuRGF0YQ0KICAgICAgfSkNCiAgICB9DQogIH0sDQogIGFzeW5jIG1vdW50ZWQoKSB7DQogICAgY29uc3QgeyBsaXN0LCBwYXJ0TmFtZSwgY29tTmFtZSB9ID0gYXdhaXQgR2V0Qk9NSW5mbygwKQ0KICAgIHRoaXMuYm9tTGlzdCA9IGxpc3QgfHwgW10NCiAgICB0aGlzLnBhcnROYW1lID0gcGFydE5hbWUNCiAgICB0aGlzLmNvbU5hbWUgPSBjb21OYW1lDQogICAgdGhpcy5pbml0UHJvY2Vzc0xpc3QoKQ0KICAgIHRoaXMudGJEYXRhTWFwID0ge30NCiAgICB0aGlzLmNyYWZ0Q29kZU1hcCA9IHt9DQogICAgdGhpcy5wYWdlVHlwZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnBnX3R5cGUNCiAgICB0aGlzLnBhZ2VTdGF0dXMgPSB0aGlzLiRyb3V0ZS5xdWVyeS5zdGF0dXMNCiAgICB0aGlzLm1vZGVsID0gdGhpcy4kcm91dGUucXVlcnkubW9kZWwNCiAgICB0aGlzLnNjaGVkdWxlSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5waWQgfHwgJycNCiAgICAvLyAvLyB0aGlzLmZvcm1JbmxpbmUuQ3JlYXRlX1VzZXJOYW1lID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lDQogICAgLy8gLy8g5qGG5p626Zeu6aKY5byV6LW3c3RvcmXmlbDmja7kuKLlpLHvvIzlt7Llj43ppojvvIznu5PmnpzvvJrmraTlpITlhYjkvb/nlKhsb2NhbFN0b3JhZ2UNCiAgICB0aGlzLmZvcm1JbmxpbmUuQ3JlYXRlX1VzZXJOYW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1VzZXJBY2NvdW50JykNCiAgICAvLyBpZiAoIXRoaXMuaXNDb20pIHsNCiAgICAvLyAgIHRoaXMuZ2V0UGFydFR5cGUoKQ0KICAgIC8vIH0gZWxzZSB7DQogICAgLy8gfQ0KDQogICAgdGhpcy51bmlxdWUgPSB1bmlxdWVDb2RlKCkNCiAgICB0aGlzLmNoZWNrV29ya3Nob3BJc09wZW4oKQ0KDQogICAgdGhpcy5zZWFyY2ggPSBkZWJvdW5jZSh0aGlzLmZldGNoRGF0YSwgODAwLCB0cnVlKQ0KICAgIGF3YWl0IHRoaXMubWVyZ2VDb25maWcoKQ0KICAgIGlmICh0aGlzLmlzVmlldyB8fCB0aGlzLmlzRWRpdCkgew0KICAgICAgY29uc3QgeyBhcmVhSWQsIGluc3RhbGwgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5DQogICAgICAvLyB0aGlzLmFyZWFJZCA9IGFyZWFJZA0KICAgICAgLy8gdGhpcy5mb3JtSW5saW5lLkluc3RhbGxVbml0X0lkID0gaW5zdGFsbA0KICAgICAgLy8gdGhpcy5nZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoKQ0KICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgIH0NCg0KICAgIGlmICh0aGlzLmlzQWRkKSB7DQogICAgICAvLyB0aGlzLmZldGNoVHJlZURhdGEoKQ0KICAgICAgdGhpcy5nZXRUeXBlKCkNCiAgICB9DQogICAgaWYgKHRoaXMuaXNFZGl0KSB7DQogICAgICB0aGlzLmdldFR5cGUoKQ0KICAgIH0NCg0KICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdtZXNzYWdlJywgdGhpcy5mcmFtZUxpc3RlbmVyKQ0KICAgIHRoaXMuJG9uY2UoJ2hvb2s6YmVmb3JlRGVzdHJveScsICgpID0+IHsNCiAgICAgIGNvbnNvbGUubG9nKCdkZWFjdGl2YXRlZCcpDQogICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignbWVzc2FnZScsIHRoaXMuZnJhbWVMaXN0ZW5lcikNCiAgICB9KQ0KICB9LA0KICBhY3RpdmF0ZWQoKSB7DQogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCB0aGlzLmZyYW1lTGlzdGVuZXIpDQogICAgdGhpcy4kb25jZSgnaG9vazpkZWFjdGl2YXRlZCcsICgpID0+IHsNCiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdtZXNzYWdlJywgdGhpcy5mcmFtZUxpc3RlbmVyKQ0KICAgIH0pDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAuLi5tYXBBY3Rpb25zKCdzY2hlZHVsZScsIFsnY2hhbmdlUHJvY2Vzc0xpc3QnLCAnaW5pdFByb2Nlc3NMaXN0JywgJ2NoYW5nZUFkZFRiS2V5cyddKSwNCiAgICBjaGVja093bmVyKCkgew0KICAgICAgaWYgKHRoaXMuaXNDb20pIHJldHVybg0KICAgICAgdGhpcy5pc093bmVyTnVsbCA9IHRoaXMudGJEYXRhLmV2ZXJ5KHYgPT4gIXYuQ29tcF9JbXBvcnRfRGV0YWlsX0lkKSAmJiAhdGhpcy5pc05lc3QNCiAgICAgIGNvbnN0IGlkeCA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgodiA9PiB2LkNvZGUgPT09ICdQYXJ0X1VzZWRfUHJvY2VzcycpDQogICAgICBpZiAodGhpcy5pc093bmVyTnVsbCkgew0KICAgICAgICBpZHggIT09IC0xICYmIHRoaXMuY29sdW1ucy5zcGxpY2UoaWR4LCAxKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWYgKGlkeCA9PT0gLTEpIHsNCiAgICAgICAgICBpZiAoIXRoaXMub3duZXJDb2x1bW4pIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiBg5YiX6KGo6YWN572u5a2X5q6157y65bCRJHt0aGlzLnBhcnROYW1lfemihueUqOW3peW6j+Wtl+autWAsDQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmNvbHVtbnMucHVzaCh0aGlzLm93bmVyQ29sdW1uKQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuY29tUGFydCA9IHRydWUNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIG1lcmdlQ29uZmlnKCkgew0KICAgICAgYXdhaXQgdGhpcy5nZXRDb25maWcoKQ0KICAgICAgYXdhaXQgdGhpcy5nZXRXb3JrVGVhbSgpDQogICAgfSwNCiAgICBkb0ZpbHRlcigpIHsNCiAgICAgIHRoaXMucHJvamVjdExpc3QgPSBbXQ0KICAgICAgdGhpcy5pbnN0YWxsTGlzdCA9IFtdDQogICAgICB0aGlzLmFyZWFMaXN0ID0gW10NCiAgICAgIHRoaXMudGJEYXRhLmZvckVhY2goY3VyID0+IHsNCiAgICAgICAgaWYgKGN1ci5Qcm9qZWN0X05hbWUgJiYgIXRoaXMucHJvamVjdExpc3QuaW5jbHVkZXMoY3VyLlByb2plY3RfTmFtZSkpIHsNCiAgICAgICAgICB0aGlzLnByb2plY3RMaXN0LnB1c2goY3VyLlByb2plY3RfTmFtZSkNCiAgICAgICAgfQ0KICAgICAgICBpZiAoY3VyLkluc3RhbGxVbml0X05hbWUgJiYgIXRoaXMuaW5zdGFsbExpc3QuaW5jbHVkZXMoY3VyLkluc3RhbGxVbml0X05hbWUpKSB7DQogICAgICAgICAgdGhpcy5pbnN0YWxsTGlzdC5wdXNoKGN1ci5JbnN0YWxsVW5pdF9OYW1lKQ0KICAgICAgICB9DQogICAgICAgIGlmIChjdXIuQXJlYV9OYW1lICYmICF0aGlzLmFyZWFMaXN0LmluY2x1ZGVzKGN1ci5BcmVhX05hbWUpKSB7DQogICAgICAgICAgdGhpcy5hcmVhTGlzdC5wdXNoKGN1ci5BcmVhX05hbWUpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRDb25maWcoKSB7DQogICAgICBsZXQgY29uZmlnQ29kZSA9ICcnDQogICAgICBpZiAodGhpcy5pc05lc3QpIHsNCiAgICAgICAgaWYgKHRoaXMuaXNWaWV3KSB7DQogICAgICAgICAgY29uZmlnQ29kZSA9ICdQUk9OZXN0aW5nU2NoZWR1bGVEZXRhaWwnDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uZmlnQ29kZSA9ICdQUk9OZXN0aW5nU2NoZWR1bGVDb25maWcnDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbmZpZ0NvZGUgPSB0aGlzLmlzQ29tDQogICAgICAgICAgPyAodGhpcy5pc1ZpZXcgPyAnUFJPQ29tVmlld1BhZ2VUYkNvbmZpZycgOiAnUFJPQ29tRHJhZnRQYWdlVGJDb25maWcnKQ0KICAgICAgICAgIDogKHRoaXMuaXNWaWV3ID8gJ1BST1BhcnRWaWV3UGFnZVRiQ29uZmlnX25ldycgOiAnUFJPUGFydERyYWZ0UGFnZVRiQ29uZmlnX25ldycpDQogICAgICB9DQogICAgICB0aGlzLmdyaWRDb2RlID0gY29uZmlnQ29kZQ0KICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyhjb25maWdDb2RlKQ0KICAgICAgaWYgKCF0aGlzLndvcmtzaG9wRW5hYmxlZCkgew0KICAgICAgICB0aGlzLmNvbHVtbnMgPSB0aGlzLmNvbHVtbnMuZmlsdGVyKHYgPT4gdi5Db2RlICE9PSAnV29ya3Nob3BfTmFtZScpDQogICAgICB9DQogICAgICBpZiAoIXRoaXMuaGFzQ3JhZnQpIHsNCiAgICAgICAgdGhpcy5jb2x1bW5zID0gdGhpcy5jb2x1bW5zLmZpbHRlcih2ID0+IHYuQ29kZSAhPT0gJ1RlY2hub2xvZ3lfQ29kZScpDQogICAgICB9DQogICAgICB0aGlzLmNoZWNrT3duZXIoKQ0KICAgIH0sDQogICAgYXN5bmMgY2hhbmdlQ29sdW1uKCkgew0KICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyh0aGlzLmdyaWRDb2RlKQ0KICAgICAgdGhpcy50YktleSsrDQogICAgfSwNCiAgICAvKiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgew0KICAgICAgY29uc29sZS5sb2coJ2RhdGEnLCBkYXRhKQ0KICAgICAgaWYgKHRoaXMuYXJlYUlkID09PSBkYXRhLklkKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy4NCiAgICAgICA9IHRydWUNCiAgICAgIGlmICghZGF0YS5QYXJlbnROb2RlcyB8fCBkYXRhLkNoaWxkcmVuPy5sZW5ndGggPiAwKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgaWYgKGRhdGE/LkRhdGFbdGhpcy5zdGF0dXNDb2RlXSA9PT0gJ+acquWvvOWFpScpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+a4heWNleacquWvvOWFpe+8jOivt+iBlOezu+a3seWMluS6uuWRmOWvvOWFpea4heWNlScsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBjb25zdCBpbml0RGF0YSA9ICh7IERhdGEgfSkgPT4gew0KICAgICAgICB0aGlzLmFyZWFJZCA9IERhdGEuSWQNCiAgICAgICAgdGhpcy5wcm9qZWN0SWQgPSBEYXRhLlByb2plY3RfSWQNCiAgICAgICAgdGhpcy5leHBhbmRlZEtleSA9IHRoaXMuYXJlYUlkDQogICAgICAgIHRoaXMuZm9ybUlubGluZS5GaW5pc2hfRGF0ZSA9ICcnDQogICAgICAgIHRoaXMuZm9ybUlubGluZS5JbnN0YWxsVW5pdF9JZCA9ICcnDQogICAgICAgIHRoaXMuZm9ybUlubGluZS5SZW1hcmsgPSAnJw0KICAgICAgICB0aGlzLnRiRGF0YSA9IFtdDQogICAgICAgIHRoaXMuZ2V0QXJlYUluZm8oKQ0KICAgICAgICB0aGlzLmdldEluc3RhbGxVbml0SWROYW1lTGlzdCgpDQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLnRiRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kY29uZmlybSgn5YiH5o2i5Yy65Z+f5Y+z5L6n5pWw5o2u5riF56m677yM5piv5ZCm56Gu6K6kPycsICfmj5DnpLonLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICBpbml0RGF0YShkYXRhKQ0KICAgICAgICAgIHRoaXMuZGlzYWJsZWRBZGQgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMudGJEYXRhTWFwID0ge30NCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5kaXNhYmxlZEFkZCA9IGZhbHNlDQogICAgICAgIGluaXREYXRhKGRhdGEpDQogICAgICB9DQogICAgfSwqLw0KDQogICAgLyogY3VzdG9tRmlsdGVyRnVuKHZhbHVlLCBkYXRhLCBub2RlKSB7DQogICAgICBjb25zdCBhcnIgPSB2YWx1ZS5zcGxpdChTUExJVF9TWU1CT0wpDQogICAgICBjb25zdCBsYWJlbFZhbCA9IGFyclswXQ0KICAgICAgY29uc3Qgc3RhdHVzVmFsID0gYXJyWzFdDQogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZQ0KICAgICAgbGV0IHBhcmVudE5vZGUgPSBub2RlLnBhcmVudA0KICAgICAgbGV0IGxhYmVscyA9IFtub2RlLmxhYmVsXQ0KICAgICAgbGV0IHN0YXR1cyA9IFtkYXRhLkRhdGFbdGhpcy5zdGF0dXNDb2RlXV0NCiAgICAgIGxldCBsZXZlbCA9IDENCiAgICAgIHdoaWxlIChsZXZlbCA8IG5vZGUubGV2ZWwpIHsNCiAgICAgICAgbGFiZWxzID0gWy4uLmxhYmVscywgcGFyZW50Tm9kZS5sYWJlbF0NCiAgICAgICAgc3RhdHVzID0gWy4uLnN0YXR1cywgZGF0YS5EYXRhW3RoaXMuc3RhdHVzQ29kZV1dDQogICAgICAgIHBhcmVudE5vZGUgPSBwYXJlbnROb2RlLnBhcmVudA0KICAgICAgICBsZXZlbCsrDQogICAgICB9DQogICAgICBsYWJlbHMgPSBsYWJlbHMuZmlsdGVyKHYgPT4gISF2KQ0KICAgICAgc3RhdHVzID0gc3RhdHVzLmZpbHRlcih2ID0+ICEhdikNCiAgICAgIGxldCByZXN1bHRMYWJlbCA9IHRydWUNCiAgICAgIGxldCByZXN1bHRTdGF0dXMgPSB0cnVlDQogICAgICBpZiAodGhpcy5zdGF0dXNUeXBlKSB7DQogICAgICAgIHJlc3VsdFN0YXR1cyA9IHN0YXR1cy5zb21lKHMgPT4gcy5pbmRleE9mKHN0YXR1c1ZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMucHJvamVjdE5hbWUpIHsNCiAgICAgICAgcmVzdWx0TGFiZWwgPSBsYWJlbHMuc29tZShzID0+IHMuaW5kZXhPZihsYWJlbFZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHJlc3VsdExhYmVsICYmIHJlc3VsdFN0YXR1cw0KICAgIH0sKi8NCiAgICBhc3luYyBmZXRjaERhdGEoKSB7DQogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgIGxldCByZXNEYXRhID0gW10NCiAgICAgIGlmICh0aGlzLmlzTmVzdCkgew0KICAgICAgICBpZiAodGhpcy5pc1ZpZXcpIHsNCiAgICAgICAgICByZXNEYXRhID0gYXdhaXQgdGhpcy5nZXRQYXJ0UGFnZUxpc3QoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJlc0RhdGEgPSBhd2FpdCB0aGlzLmdldE5lc3RQYWdlTGlzdCgpDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJlc0RhdGEgPSBhd2FpdCB0aGlzLmdldFBhcnRQYWdlTGlzdCgpDQogICAgICB9DQoNCiAgICAgIHRoaXMuaW5pdFRiRGF0YShyZXNEYXRhKQ0KICAgICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgZmV0Y2hUcmVlRGF0YUxvY2FsKCkgew0KICAgICAgLy8gdGhpcy5maWx0ZXJUZXh0ID0gdGhpcy5wcm9qZWN0TmFtZQ0KICAgIH0sDQogICAgZmV0Y2hUcmVlU3RhdHVzKCkgew0KICAgICAgLy8gdGhpcy5maWx0ZXJUZXh0ID0gdGhpcy5zdGF0dXNUeXBlDQogICAgfSwNCiAgICAvKiAgICBmZXRjaFRyZWVEYXRhKCkgew0KICAgICAgdGhpcy50cmVlTG9hZGluZyA9IHRydWUNCiAgICAgIEdldFByb2plY3RBcmVhVHJlZUxpc3QoeyBwcm9qZWN0TmFtZTogdGhpcy5wcm9qZWN0TmFtZSwgdHlwZTogdGhpcy5pc0NvbSA/IDEgOiAyIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLkRhdGEubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgdGhpcy50cmVlTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgY29uc3QgcmVzRGF0YSA9IHJlcy5EYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLklzX0RpcmVjdG9yeSA9IHRydWUNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLnRyZWVEYXRhID0gcmVzRGF0YQ0KICAgICAgICB0aGlzLnNldEtleSgpDQogICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LCovDQogICAgLy8gc2V0S2V5KCkgew0KICAgIC8vICAgY29uc3QgZGVlcEZpbHRlciA9ICh0cmVlKSA9PiB7DQogICAgLy8gICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdHJlZS5sZW5ndGg7IGkrKykgew0KICAgIC8vICAgICAgIGNvbnN0IGl0ZW0gPSB0cmVlW2ldDQogICAgLy8gICAgICAgY29uc3QgeyBEYXRhLCBDaGlsZHJlbiB9ID0gaXRlbQ0KICAgIC8vICAgICAgIGNvbnNvbGUubG9nKERhdGEpDQogICAgLy8gICAgICAgaWYgKERhdGEuUGFyZW50SWQgJiYgIUNoaWxkcmVuPy5sZW5ndGgpIHsNCiAgICAvLyAgICAgICAgIHRoaXMuaGFuZGxlTm9kZUNsaWNrKGl0ZW0pDQogICAgLy8gICAgICAgICByZXR1cm4NCiAgICAvLyAgICAgICB9IGVsc2Ugew0KICAgIC8vICAgICAgICAgaWYgKENoaWxkcmVuICYmIENoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAvLyAgICAgICAgICAgcmV0dXJuIGRlZXBGaWx0ZXIoQ2hpbGRyZW4pDQogICAgLy8gICAgICAgICB9DQogICAgLy8gICAgICAgfQ0KICAgIC8vICAgICB9DQogICAgLy8gICB9DQogICAgLy8gICByZXR1cm4gZGVlcEZpbHRlcih0aGlzLnRyZWVEYXRhKQ0KICAgIC8vIH0sDQogICAgY2xvc2VWaWV3KCkgew0KICAgICAgY2xvc2VUYWdWaWV3KHRoaXMuJHN0b3JlLCB0aGlzLiRyb3V0ZSkNCiAgICB9LA0KICAgIGNoZWNrV29ya3Nob3BJc09wZW4oKSB7DQogICAgICB0aGlzLndvcmtTaG9wSXNPcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgdGJTZWxlY3RDaGFuZ2UoYXJyYXkpIHsNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBhcnJheS5yZWNvcmRzDQogICAgfSwNCiAgICBnZXRBcmVhSW5mbygpIHsNCiAgICAgIHRoaXMuZm9ybUlubGluZS5GaW5pc2hfRGF0ZSA9ICcnDQogICAgICBBcmVhR2V0RW50aXR5KHsNCiAgICAgICAgaWQ6IHRoaXMuYXJlYUlkDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgaWYgKCFyZXMuRGF0YSkgew0KICAgICAgICAgICAgcmV0dXJuIFtdDQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29uc3Qgc3RhcnQgPSBtb21lbnQocmVzLkRhdGE/LkRlbWFuZF9CZWdpbl9EYXRlKQ0KICAgICAgICAgIGNvbnN0IGVuZCA9IG1vbWVudChyZXMuRGF0YT8uRGVtYW5kX0VuZF9EYXRlKQ0KICAgICAgICAgIHRoaXMucGlja2VyT3B0aW9ucy5kaXNhYmxlZERhdGUgPSAodGltZSkgPT4gew0KICAgICAgICAgICAgcmV0dXJuIHRpbWUuZ2V0VGltZSgpIDwgc3RhcnQgfHwgdGltZS5nZXRUaW1lKCkgPiBlbmQNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgdGhpcy5vcGVuQWRkRHJhZnQgPSBmYWxzZQ0KICAgIH0sDQogICAgZ2V0TmVzdFBhZ2VMaXN0KCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsNCiAgICAgICAgR2V0Q2FuU2NoZHVsaW5nUGFydExpc3Qoew0KICAgICAgICAgIElkczogdGhpcy5uZXN0SWRzDQogICAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgY29uc3QgX2xpc3QgPSByZXM/LkRhdGEgfHwgW10NCiAgICAgICAgICAgIGNvbnN0IGxpc3QgPSBfbGlzdC5tYXAodiA9PiB7DQogICAgICAgICAgICAgIHYuUGFydF9Vc2VkX1Byb2Nlc3MgPSB2LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MgfHwgdi5QYXJ0X1R5cGVfVXNlZF9Qcm9jZXNzDQogICAgICAgICAgICAgIC8vIHYub3JpZ2luYWxQYXRoID0gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoID8gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoIDogJycNCiAgICAgICAgICAgICAgdi5Xb3Jrc2hvcF9JZCA9IHYuU2NoZWR1bGVkX1dvcmtzaG9wX0lkDQogICAgICAgICAgICAgIHYuV29ya3Nob3BfTmFtZSA9IHYuU2NoZWR1bGVkX1dvcmtzaG9wX05hbWUNCiAgICAgICAgICAgICAgdi5UZWNobm9sb2d5X1BhdGggPSB2LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggfHwgdi5UZWNobm9sb2d5X1BhdGgNCiAgICAgICAgICAgICAgdi5jaG9vc2VDb3VudCA9IHYuQ2FuX1NjaGR1bGluZ19Db3VudA0KDQogICAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICByZXNvbHZlKGxpc3QpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldENvbVBhZ2VMaXN0KCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsNCiAgICAgICAgY29uc3Qgew0KICAgICAgICAgIHBpZA0KICAgICAgICB9ID0gdGhpcy4kcm91dGUucXVlcnkNCiAgICAgICAgR2V0Q29tcFNjaGR1bGluZ0luZm9EZXRhaWwoew0KICAgICAgICAgIFNjaGR1bGluZ19QbGFuX0lkOiBwaWQNCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IHsgU2NoZHVsaW5nX1BsYW4sIFNjaGR1bGluZ19Db21wcywgUHJvY2Vzc19MaXN0IH0gPSByZXMuRGF0YQ0KICAgICAgICAgICAgdGhpcy5mb3JtSW5saW5lID0gT2JqZWN0LmFzc2lnbih0aGlzLmZvcm1JbmxpbmUsIFNjaGR1bGluZ19QbGFuKQ0KICAgICAgICAgICAgUHJvY2Vzc19MaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IHBsaXN0ID0gew0KICAgICAgICAgICAgICAgIGtleTogaXRlbS5Qcm9jZXNzX0NvZGUsDQogICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB0aGlzLmNoYW5nZVByb2Nlc3NMaXN0KHBsaXN0KQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIGNvbnN0IGxpc3QgPSBTY2hkdWxpbmdfQ29tcHMubWFwKHYgPT4gew0KICAgICAgICAgICAgICB2LmNob29zZUNvdW50ID0gdi5DYW5fU2NoZHVsaW5nX0NvdW50DQogICAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmVzb2x2ZShsaXN0IHx8IFtdKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZWplY3QoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRQYXJ0UGFnZUxpc3QoKSB7DQogICAgICBjb25zdCB7DQogICAgICAgIHBpZA0KICAgICAgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5DQogICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBHZXRQYXJ0U2NoZHVsaW5nSW5mb0RldGFpbCh7DQogICAgICAgIFNjaGR1bGluZ19QbGFuX0lkOiBwaWQNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnN0IFNhcmVQYXJ0c01vZGVsID0gcmVzLkRhdGE/LlNhcmVQYXJ0c01vZGVsLm1hcCh2ID0+IHsNCiAgICAgICAgICAgIGlmICh2LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgICAgICAgLy8g5bey5a2Y5Zyo5pON5L2c6L+H5pWw5o2uDQogICAgICAgICAgICAgIHYuUGFydF9Vc2VkX1Byb2Nlc3MgPSB2LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHYuY2hvb3NlQ291bnQgPSB2LkNhbl9TY2hkdWxpbmdfQ291bnQNCiAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmZvcm1JbmxpbmUgPSBPYmplY3QuYXNzaWduKHRoaXMuZm9ybUlubGluZSwgcmVzLkRhdGE/LlNjaGR1bGluZ19QbGFuKQ0KICAgICAgICAgIHRoaXMuZ2V0U3RvcExpc3QoU2FyZVBhcnRzTW9kZWwpDQogICAgICAgICAgcmV0dXJuIFNhcmVQYXJ0c01vZGVsIHx8IFtdDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgcmV0dXJuIHJlc3VsdCB8fCBbXQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0U3RvcExpc3QobGlzdCkgew0KICAgICAgY29uc3Qgc3VibWl0T2JqID0gbGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgSWQ6IGl0ZW0uUGFydF9BZ2dyZWdhdGVfSWQsDQogICAgICAgICAgVHlwZTogMQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgYXdhaXQgR2V0U3RvcExpc3Qoc3VibWl0T2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3Qgc3RvcE1hcCA9IHt9DQogICAgICAgICAgcmVzLkRhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgIHN0b3BNYXBbaXRlbS5JZF0gPSAhIWl0ZW0uSXNfU3RvcA0KICAgICAgICAgIH0pDQogICAgICAgICAgbGlzdC5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBpZiAoc3RvcE1hcFtyb3cuUGFydF9BZ2dyZWdhdGVfSWRdKSB7DQogICAgICAgICAgICAgIHRoaXMuJHNldChyb3csICdzdG9wRmxhZycsIHN0b3BNYXBbcm93LlBhcnRfQWdncmVnYXRlX0lkXSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaW5pdFRiRGF0YShsaXN0LCB0ZWFtS2V5ID0gJ0FsbG9jYXRpb25fVGVhbXMnKSB7DQogICAgICBjb25zb2xlLmxvZyg1LCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGxpc3QpKSkNCiAgICAgIHRoaXMudGJEYXRhID0gbGlzdC5tYXAocm93ID0+IHsNCiAgICAgICAgY29uc3QgcHJvY2Vzc0xpc3QgPSByb3cuVGVjaG5vbG9neV9QYXRoPy5zcGxpdCgnLycpIHx8IFtdDQogICAgICAgIHJvdy51dWlkID0gdXVpZHY0KCkNCiAgICAgICAgdGhpcy5hZGRFbGVtZW50VG9UYkRhdGEocm93KQ0KICAgICAgICBpZiAocm93W3RlYW1LZXldKSB7DQogICAgICAgICAgY29uc3QgbmV3RGF0YSA9IHJvd1t0ZWFtS2V5XS5maWx0ZXIoKHIpID0+IHByb2Nlc3NMaXN0LmZpbmRJbmRleCgocCkgPT4gci5Qcm9jZXNzX0NvZGUgPT09IHApICE9PSAtMSkNCiAgICAgICAgICBuZXdEYXRhLmZvckVhY2goKGVsZSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShyb3cudXVpZCwgZWxlLlByb2Nlc3NfQ29kZSwgZWxlLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgICAgIGNvbnN0IG1heCA9IHRoaXMuZ2V0Um93VW5pcXVlTWF4KHJvdy51dWlkLCBlbGUuUHJvY2Vzc19Db2RlLCBlbGUuV29ya2luZ19UZWFtX0lkKQ0KICAgICAgICAgICAgcm93W2NvZGVdID0gZWxlLkNvdW50DQogICAgICAgICAgICByb3dbbWF4XSA9IDANCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuc2V0SW5wdXRNYXgocm93KQ0KICAgICAgICByZXR1cm4gcm93DQogICAgICB9KQ0KICAgICAgbGV0IGlkcyA9ICcnDQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBpZHMgPSB0aGlzLnRiRGF0YS5tYXAodiA9PiB2LkNvbXBfSW1wb3J0X0RldGFpbF9JZCkudG9TdHJpbmcoKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWRzID0gdGhpcy50YkRhdGEubWFwKHYgPT4gdi5QYXJ0X0FnZ3JlZ2F0ZV9JZCkudG9TdHJpbmcoKQ0KICAgICAgfQ0KICAgICAgdGhpcy5jdXJyZW50SWRzID0gaWRzDQogICAgfSwNCiAgICBhc3luYyBtZXJnZUNyYWZ0UHJvY2VzcyhsaXN0KSB7DQogICAgICBsZXQgY29kZXMgPSBbLi4ubmV3IFNldChsaXN0Lm1hcCh2ID0+IHYuVGVjaG5vbG9neV9Db2RlKSldDQogICAgICBmb3IgKGNvbnN0IGtleSBpbiB0aGlzLmNyYWZ0Q29kZU1hcCkgew0KICAgICAgICBpZiAodGhpcy5jcmFmdENvZGVNYXAuaGFzT3duUHJvcGVydHkoa2V5KSkgew0KICAgICAgICAgIGNvZGVzID0gY29kZXMuZmlsdGVyKGNvZGUgPT4gY29kZSAhPT0ga2V5KQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBjb25zdCBfY3JhZnRDb2RlTWFwID0gYXdhaXQgdGhpcy5nZXRDcmFmdFByb2Nlc3MoY29kZXMpDQogICAgICBPYmplY3QuYXNzaWduKHRoaXMuY3JhZnRDb2RlTWFwLCBfY3JhZnRDb2RlTWFwKQ0KICAgIH0sDQogICAgZ2V0Q3JhZnRQcm9jZXNzKGd5R3JvdXAgPSBbXSkgew0KICAgICAgZ3lHcm91cCA9IGd5R3JvdXAuZmlsdGVyKHYgPT4gISF2KQ0KICAgICAgaWYgKCFneUdyb3VwLmxlbmd0aCkgcmV0dXJuIFByb21pc2UucmVzb2x2ZSh7fSkNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7DQogICAgICAgIEdldFByb2Nlc3NGbG93TGlzdFdpdGhUZWNobm9sb2d5KHsNCiAgICAgICAgICBUZWNobm9sb2d5Q29kZXM6IGd5R3JvdXAsDQogICAgICAgICAgVHlwZTogMg0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IGd5TGlzdCA9IHJlcy5EYXRhIHx8IFtdDQogICAgICAgICAgICBjb25zdCBneU1hcCA9IGd5TGlzdC5yZWR1Y2UoKGFjYywgaXRlbSkgPT4gew0KICAgICAgICAgICAgICBhY2NbaXRlbS5Db2RlXSA9IGl0ZW0uVGVjaG5vbG9neV9QYXRoDQogICAgICAgICAgICAgIHJldHVybiBhY2MNCiAgICAgICAgICAgIH0sIHt9KQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ2d5TWFwJywgZ3lNYXApDQogICAgICAgICAgICByZXNvbHZlKGd5TWFwKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZWplY3QoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBtZXJnZVNlbGVjdExpc3QobmV3TGlzdCkgew0KICAgICAgaWYgKHRoaXMuaGFzQ3JhZnQpIHsNCiAgICAgICAgYXdhaXQgdGhpcy5tZXJnZUNyYWZ0UHJvY2VzcyhuZXdMaXN0KQ0KICAgICAgfQ0KICAgICAgY29uc29sZS50aW1lKCdtZXJnZVNlbGVjdExpc3RUaW1lJykNCiAgICAgIGxldCBoYXNVc2VkUGFydEZsYWcgPSB0cnVlDQoNCiAgICAgIG5ld0xpc3QuZm9yRWFjaCgoZWxlbWVudCwgaW5kZXgpID0+IHsNCiAgICAgICAgY29uc3QgY3VyID0gdGhpcy5nZXRNZXJnZVVuaXF1ZVJvdyhlbGVtZW50KQ0KDQogICAgICAgIGlmICghY3VyKSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ2VsZW1lbnQnLCBlbGVtZW50KQ0KICAgICAgICAgIGVsZW1lbnQucHV1aWQgPSBlbGVtZW50LnV1aWQNCiAgICAgICAgICBlbGVtZW50LlNjaGR1bGVkX0NvdW50ID0gZWxlbWVudC5jaG9vc2VDb3VudA0KICAgICAgICAgIGVsZW1lbnQuU2NoZHVsZWRfV2VpZ2h0ID0gbnVtZXJhbChlbGVtZW50LmNob29zZUNvdW50ICogZWxlbWVudC5XZWlnaHQpLmZvcm1hdCgnMC5bMDBdJykNCg0KICAgICAgICAgIGlmICh0aGlzLmhhc0NyYWZ0ICYmICFlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICAgICAgaWYgKHRoaXMuY3JhZnRDb2RlTWFwW2VsZW1lbnQuVGVjaG5vbG9neV9Db2RlXSAmJiB0aGlzLmNyYWZ0Q29kZU1hcFtlbGVtZW50LlRlY2hub2xvZ3lfQ29kZV0gaW5zdGFuY2VvZiBBcnJheSkgew0KICAgICAgICAgICAgICBjb25zdCBjdXJQYXRoQXJyID0gdGhpcy5jcmFmdENvZGVNYXBbZWxlbWVudC5UZWNobm9sb2d5X0NvZGVdDQogICAgICAgICAgICAgIGlmIChlbGVtZW50LlBhcnRfVXNlZF9Qcm9jZXNzICYmICFjdXJQYXRoQXJyLmluY2x1ZGVzKGVsZW1lbnQuUGFydF9Vc2VkX1Byb2Nlc3MpKSB7DQogICAgICAgICAgICAgICAgaGFzVXNlZFBhcnRGbGFnID0gZmFsc2UNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCA9IGN1clBhdGhBcnIuam9pbignLycpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICB0aGlzLnRiRGF0YS5wdXNoKGVsZW1lbnQpDQogICAgICAgICAgdGhpcy5hZGRFbGVtZW50VG9UYkRhdGEoZWxlbWVudCkNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIGN1ci5wdXVpZCA9IGVsZW1lbnQudXVpZA0KDQogICAgICAgIGN1ci5TY2hkdWxlZF9Db3VudCArPSBlbGVtZW50LmNob29zZUNvdW50DQogICAgICAgIGN1ci5TY2hkdWxlZF9XZWlnaHQgPSBudW1lcmFsKGN1ci5TY2hkdWxlZF9XZWlnaHQpLmFkZChlbGVtZW50LmNob29zZUNvdW50ICogZWxlbWVudC5XZWlnaHQpLmZvcm1hdCgnMC5bMDBdJykNCiAgICAgICAgaWYgKCFjdXIuVGVjaG5vbG9neV9QYXRoKSB7DQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgdGhpcy5zZXRJbnB1dE1heChjdXIpDQogICAgICB9KQ0KDQogICAgICAvLyBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgLy8gICB0aGlzLnRiRGF0YS5zb3J0KChhLCBiKSA9PiBhLmluaXRSb3dJbmRleCAtIGIuaW5pdFJvd0luZGV4KQ0KICAgICAgLy8gfSBlbHNlIHsNCiAgICAgIC8vICAgdGhpcy50YkRhdGEuc29ydCgoYSwgYikgPT4gYS5QYXJ0X0NvZGUubG9jYWxlQ29tcGFyZShiLlBhcnRfQ29kZSkpDQogICAgICAvLyB9DQogICAgICB0aGlzLnRiRGF0YS5zb3J0KChhLCBiKSA9PiBhLmluaXRSb3dJbmRleCAtIGIuaW5pdFJvd0luZGV4KQ0KICAgICAgY29uc29sZS50aW1lRW5kKCdmZmYnKQ0KICAgICAgY29uc29sZS5sb2coJ3RoaXMudGJEYXRhTWFwJywgdGhpcy50YkRhdGFNYXAsIHRoaXMudGJEYXRhKQ0KICAgIH0sDQogICAgYWRkRWxlbWVudFRvVGJEYXRhKGVsZW1lbnQpIHsNCiAgICAgIGNvbnN0IGtleSA9IHRoaXMuZ2V0VW5pS2V5KGVsZW1lbnQpDQogICAgICB0aGlzLnRiRGF0YU1hcFtrZXldID0gZWxlbWVudA0KICAgIH0sDQogICAgZ2V0TWVyZ2VVbmlxdWVSb3coZWxlbWVudCkgew0KICAgICAgY29uc3Qga2V5ID0gdGhpcy5nZXRVbmlLZXkoZWxlbWVudCkNCiAgICAgIHJldHVybiB0aGlzLnRiRGF0YU1hcFtrZXldDQogICAgfSwNCiAgICBnZXRVbmlLZXkoZWxlbWVudCkgew0KICAgICAgcmV0dXJuIGdldFVuaXF1ZSh0aGlzLmlzQ29tLCBlbGVtZW50KQ0KICAgIH0sDQogICAgY2hlY2tGb3JtKCkgew0KICAgICAgbGV0IGlzVmFsaWRhdGUgPSB0cnVlDQogICAgICB0aGlzLiRyZWZzWydmb3JtSW5saW5lJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICghdmFsaWQpIGlzVmFsaWRhdGUgPSBmYWxzZQ0KICAgICAgfSkNCiAgICAgIHJldHVybiBpc1ZhbGlkYXRlDQogICAgfSwNCiAgICBhc3luYyBzYXZlRHJhZnQoaXNPcmRlciA9IGZhbHNlKSB7DQogICAgICBjb25zdCBjaGVja1N1Y2Nlc3MgPSB0aGlzLmNoZWNrRm9ybSgpDQogICAgICBpZiAoIWNoZWNrU3VjY2VzcykgcmV0dXJuIGZhbHNlDQogICAgICBjb25zdCB7IHRhYmxlRGF0YSwgc3RhdHVzIH0gPSB0aGlzLmdldFN1Ym1pdFRiSW5mbygpDQogICAgICBpZiAoIXN0YXR1cykgcmV0dXJuIGZhbHNlDQogICAgICBpZiAoIWlzT3JkZXIpIHsNCiAgICAgICAgdGhpcy5zYXZlTG9hZGluZyA9IHRydWUNCiAgICAgIH0NCg0KICAgICAgY29uc3QgaXNTdWNjZXNzID0gYXdhaXQgdGhpcy5oYW5kbGVTYXZlRHJhZnQodGFibGVEYXRhLCBpc09yZGVyKQ0KICAgICAgY29uc29sZS5sb2coJ2lzU3VjY2VzcycsIGlzU3VjY2VzcykNCiAgICAgIGlmICghaXNTdWNjZXNzKSByZXR1cm4gZmFsc2UNCiAgICAgIGlmIChpc09yZGVyKSByZXR1cm4gaXNTdWNjZXNzDQogICAgICB0aGlzLiRyZWZzWydkcmFmdCddPy5mZXRjaERhdGEoKQ0KICAgICAgdGhpcy5zYXZlTG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICBhc3luYyBzYXZlV29ya1Nob3AoKSB7DQogICAgICBjb25zdCBjaGVja1N1Y2Nlc3MgPSB0aGlzLmNoZWNrRm9ybSgpDQogICAgICBpZiAoIWNoZWNrU3VjY2VzcykgcmV0dXJuIGZhbHNlDQogICAgICBjb25zdCBvYmogPSB7fQ0KICAgICAgaWYgKCF0aGlzLnRiRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+aVsOaNruS4jeiDveS4uuepuicsDQogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgICAgb2JqLlNjaGR1bGluZ19Db21wcyA9IHRoaXMudGJEYXRhDQogICAgICB9IGVsc2Ugew0KICAgICAgICBvYmouU2FyZVBhcnRzTW9kZWwgPSB0aGlzLnRiRGF0YQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuaXNFZGl0KSB7DQogICAgICAgIG9iai5TY2hkdWxpbmdfUGxhbiA9IHRoaXMuZm9ybUlubGluZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgb2JqLlNjaGR1bGluZ19QbGFuID0gew0KICAgICAgICAgIC4uLnRoaXMuZm9ybUlubGluZSwNCiAgICAgICAgICBQcm9qZWN0X0lkOiB0aGlzLnByb2plY3RJZCwNCiAgICAgICAgICBBcmVhX0lkOiB0aGlzLmFyZWFJZCwNCiAgICAgICAgICBTY2hkdWxpbmdfTW9kZWw6IHRoaXMubW9kZWwgLy8gMeaehOS7tuWNleeLrOaOkuS6p++8jDLpm7bku7bljZXni6zmjpLkuqfvvIwz5p6EL+mbtuS7tuS4gOi1t+aOkuS6pw0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLnBnTG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IF9mdW4gPSB0aGlzLmlzQ29tID8gU2F2ZUNvbXBvbmVudFNjaGVkdWxpbmdXb3Jrc2hvcCA6IFNhdmVQYXJ0U2NoZWR1bGluZ1dvcmtzaG9wTmV3DQogICAgICBfZnVuKG9iaikudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkv53lrZjmiJDlip8nLA0KICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmNsb3NlVmlldygpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFN1Ym1pdFRiSW5mbygpIHsNCiAgICAgIC8vIOWkhOeQhuS4iuS8oOeahOaVsOaNrg0KICAgICAgbGV0IHRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy50YkRhdGEpKQ0KICAgICAgdGFibGVEYXRhID0gdGFibGVEYXRhLmZpbHRlcihpdGVtID0+IGl0ZW0uU2NoZHVsZWRfQ291bnQgPiAwKQ0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0YWJsZURhdGEubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgZWxlbWVudCA9IHRhYmxlRGF0YVtpXQ0KICAgICAgICBsZXQgbGlzdCA9IFtdDQogICAgICAgIGlmICghZWxlbWVudC5UZWNobm9sb2d5X1BhdGgpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICflt6Xluo/kuI3og73kuLrnqbonLA0KICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgfSkNCiAgICAgICAgICByZXR1cm4geyBzdGF0dXM6IGZhbHNlIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5pc1BhcnRQcmVwYXJlICYmICFlbGVtZW50LlBhcnRfVXNlZF9Qcm9jZXNzICYmIGVsZW1lbnQuVHlwZSAhPT0gJ0RpcmVjdCcgJiYgdGhpcy5jb21QYXJ0KSB7DQogICAgICAgICAgY29uc3QgbXNnID0gJ+mihueUqOW3peW6j+S4jeiDveS4uuepuicNCiAgICAgICAgICBpZiAodGhpcy5pc05lc3QpIHsNCiAgICAgICAgICAgIGlmIChlbGVtZW50LkNvbXBfSW1wb3J0X0RldGFpbF9JZCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiBtc2csDQogICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIHJldHVybiB7IHN0YXR1czogZmFsc2UgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogbXNnLA0KICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZXR1cm4geyBzdGF0dXM6IGZhbHNlIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLy8gaWYgKCF0aGlzLmlzQ29tICYmIGVsZW1lbnQuQ29tcF9JbXBvcnRfRGV0YWlsX0lkICYmICFlbGVtZW50LlBhcnRfVXNlZF9Qcm9jZXNzKSB7DQogICAgICAgIC8vICAgLy8g6Zu25p6E5Lu2IOmbtuS7tuWNleeLrOaOkuS6pw0KICAgICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAvLyAgICAgbWVzc2FnZTogJ+mbtuS7tumihueUqOW3peW6j+S4jeiDveS4uuepuicsDQogICAgICAgIC8vICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgLy8gICB9KQ0KICAgICAgICAvLyAgIHJldHVybiB7IHN0YXR1czogZmFsc2UgfQ0KICAgICAgICAvLyB9DQogICAgICAgIGlmIChlbGVtZW50LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggJiYgZWxlbWVudC5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoICE9PSBlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogYOivt+WSjOivpeWMuuWfn+aJueasoeS4i+W3suaOkuS6p+WQjCR7dGhpcy5wYXJ0TmFtZX3kv53mjIHlt6Xluo/kuIDoh7RgLA0KICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgfSkNCiAgICAgICAgICByZXR1cm4geyBzdGF0dXM6IGZhbHNlIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAoZWxlbWVudC5TY2hlZHVsZWRfVXNlZF9Qcm9jZXNzICYmIGVsZW1lbnQuU2NoZWR1bGVkX1VzZWRfUHJvY2VzcyAhPT0gZWxlbWVudC5QYXJ0X1VzZWRfUHJvY2Vzcykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogYOivt+WSjOivpeWMuuWfn+aJueasoeS4i+W3suaOkuS6p+WQjCR7dGhpcy5wYXJ0TmFtZX3poobnlKjlt6Xluo/kv53mjIHkuIDoh7RgLA0KICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgfSkNCiAgICAgICAgICByZXR1cm4geyBzdGF0dXM6IGZhbHNlIH0NCiAgICAgICAgfQ0KICAgICAgICBjb25zdCBwcm9jZXNzTGlzdCA9IEFycmF5LmZyb20obmV3IFNldChlbGVtZW50LlRlY2hub2xvZ3lfUGF0aC5zcGxpdCgnLycpKSkNCiAgICAgICAgLy8gcHJvY2Vzc0xpc3QuZm9yRWFjaChjb2RlID0+IHsNCiAgICAgICAgLy8gY29uc3QgZ3JvdXBzID0gdGhpcy53b3JraW5nVGVhbS5maWx0ZXIodiA9PiB2LlByb2Nlc3NfQ29kZSA9PT0gY29kZSkNCiAgICAgICAgLy8gY29uc3QgZ3JvdXBzTGlzdCA9IGdyb3Vwcy5tYXAoZ3JvdXAgPT4gew0KICAgICAgICAvLyAgIGNvbnN0IHVDb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUoZWxlbWVudC51dWlkLCBjb2RlLCBncm91cC5Xb3JraW5nX1RlYW1fSWQpDQogICAgICAgIC8vICAgY29uc3QgdU1heCA9IHRoaXMuZ2V0Um93VW5pcXVlTWF4KGVsZW1lbnQudXVpZCwgY29kZSwgZ3JvdXAuV29ya2luZ19UZWFtX0lkKQ0KICAgICAgICAvLyAgIGNvbnN0IG9iaiA9IHsNCiAgICAgICAgLy8gICAgIFRlYW1fVGFza19JZDogZWxlbWVudC5UZWFtX1Rhc2tfSWQsDQogICAgICAgIC8vICAgICBDb21wX0NvZGU6IGVsZW1lbnQuQ29tcF9Db2RlLA0KICAgICAgICAvLyAgICAgQWdhaW5fQ291bnQ6ICtlbGVtZW50W3VDb2RlXSB8fCAwLCAvLyDkuI3loavvvIzlkI7lj7DorqnkvKAwDQogICAgICAgIC8vICAgICBQYXJ0X0NvZGU6IHRoaXMuaXNDb20gPyBudWxsIDogJycsDQogICAgICAgIC8vICAgICBQcm9jZXNzX0NvZGU6IGNvZGUsDQogICAgICAgIC8vICAgICBUZWNobm9sb2d5X1BhdGg6IGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoLA0KICAgICAgICAvLyAgICAgV29ya2luZ19UZWFtX0lkOiBncm91cC5Xb3JraW5nX1RlYW1fSWQsDQogICAgICAgIC8vICAgICBXb3JraW5nX1RlYW1fTmFtZTogZ3JvdXAuV29ya2luZ19UZWFtX05hbWUNCiAgICAgICAgLy8gICB9DQogICAgICAgIC8vICAgZGVsZXRlIGVsZW1lbnRbdUNvZGVdDQogICAgICAgIC8vICAgZGVsZXRlIGVsZW1lbnRbdU1heF0NCiAgICAgICAgLy8gICByZXR1cm4gb2JqDQogICAgICAgIC8vIH0pDQogICAgICAgIC8vIGxpc3QucHVzaCguLi5ncm91cHNMaXN0KQ0KICAgICAgICAvLyB9KQ0KICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IHByb2Nlc3NMaXN0Lmxlbmd0aDsgaisrKSB7DQogICAgICAgICAgY29uc3QgY29kZSA9IHByb2Nlc3NMaXN0W2pdDQogICAgICAgICAgY29uc3Qgc2NoZHVsZWRDb3VudCA9IGVsZW1lbnQuU2NoZHVsZWRfQ291bnQgfHwgMA0KICAgICAgICAgIGxldCBncm91cHMgPSBbXQ0KICAgICAgICAgIGlmIChlbGVtZW50LkFsbG9jYXRpb25fVGVhbXMpIHsNCiAgICAgICAgICAgIGdyb3VwcyA9IGVsZW1lbnQuQWxsb2NhdGlvbl9UZWFtcy5maWx0ZXIodiA9PiB2LlByb2Nlc3NfQ29kZSA9PT0gY29kZSkNCiAgICAgICAgICB9DQogICAgICAgICAgY29uc3QgYWdhaW5Db3VudCA9IGdyb3Vwcy5yZWR1Y2UoKGFjYywgY3VyKSA9PiB7DQogICAgICAgICAgICByZXR1cm4gYWNjICsgKGN1ci5BZ2Fpbl9Db3VudCB8fCAwKQ0KICAgICAgICAgIH0sIDApDQogICAgICAgICAgaWYgKGFnYWluQ291bnQgPiBzY2hkdWxlZENvdW50KSB7DQogICAgICAgICAgICBsaXN0ID0gW10NCiAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGxpc3QucHVzaCguLi5ncm91cHMpDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGNvbnN0IGhhc0lucHV0ID0gT2JqZWN0LmtleXMoZWxlbWVudCkuZmlsdGVyKF8gPT4gXy5zdGFydHNXaXRoKGVsZW1lbnRbJ3V1aWQnXSkpDQogICAgICAgIGhhc0lucHV0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBkZWxldGUgZWxlbWVudFtpdGVtXQ0KICAgICAgICB9KQ0KICAgICAgICBkZWxldGUgZWxlbWVudFsndXVpZCddDQogICAgICAgIGRlbGV0ZSBlbGVtZW50WydfWF9ST1dfS0VZJ10NCiAgICAgICAgZGVsZXRlIGVsZW1lbnRbJ3B1dWlkJ10NCiAgICAgICAgZWxlbWVudC5BbGxvY2F0aW9uX1RlYW1zID0gbGlzdA0KICAgICAgfQ0KICAgICAgcmV0dXJuIHsgdGFibGVEYXRhLCBzdGF0dXM6IHRydWUgfQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlU2F2ZURyYWZ0KHRhYmxlRGF0YSwgaXNPcmRlcikgew0KICAgICAgY29uc29sZS5sb2coJ+S/neWtmOiNieeovycpDQogICAgICBjb25zdCBfZnVuID0gdGhpcy5pc0NvbSA/IFNhdmVDb21wU2NoZHVsaW5nRHJhZnQgOiBTYXZlUGFydFNjaGR1bGluZ0RyYWZ0TmV3DQogICAgICBjb25zdCBvYmogPSB7fQ0KICAgICAgLy8gaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgIC8vIG9iai5TY2hkdWxpbmdfQ29tcHMgPSB0YWJsZURhdGENCiAgICAgIGNvbnN0IHAgPSBbXQ0KICAgICAgZm9yIChjb25zdCBvYmpLZXkgaW4gdGhpcy5wcm9jZXNzTGlzdCkgew0KICAgICAgICBpZiAodGhpcy5wcm9jZXNzTGlzdC5oYXNPd25Qcm9wZXJ0eShvYmpLZXkpKSB7DQogICAgICAgICAgcC5wdXNoKHRoaXMucHJvY2Vzc0xpc3Rbb2JqS2V5XSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgb2JqLlByb2Nlc3NfTGlzdCA9IHANCiAgICAgIC8vIH0gZWxzZSB7DQogICAgICBvYmouU2FyZVBhcnRzTW9kZWwgPSB0YWJsZURhdGENCiAgICAgIC8vIH0NCiAgICAgIGlmICh0aGlzLmlzRWRpdCkgew0KICAgICAgICBvYmouU2NoZHVsaW5nX1BsYW4gPSB0aGlzLmZvcm1JbmxpbmUNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIG9iai5TY2hkdWxpbmdfUGxhbiA9IHsNCiAgICAgICAgICAuLi50aGlzLmZvcm1JbmxpbmUsDQogICAgICAgICAgUHJvamVjdF9JZDogdGhpcy5wcm9qZWN0SWQsDQogICAgICAgICAgQXJlYV9JZDogdGhpcy5hcmVhSWQsDQogICAgICAgICAgU2NoZHVsaW5nX01vZGVsOiB0aGlzLm1vZGVsIC8vIDHmnoTku7bljZXni6zmjpLkuqfvvIwy6Zu25Lu25Y2V54us5o6S5Lqn77yMM+aehC/pm7bku7bkuIDotbfmjpLkuqcNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgbGV0IG9yZGVyU3VjY2VzcyA9IGZhbHNlDQogICAgICBjb25zb2xlLmxvZygnb2JqJywgb2JqKQ0KDQogICAgICBhd2FpdCBfZnVuKG9iaikudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGlmICghaXNPcmRlcikgew0KICAgICAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6ICfkv53lrZjmiJDlip8nLA0KICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLmNsb3NlVmlldygpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMudGVtcGxhdGVTY2hlZHVsZUNvZGUgPSByZXMuRGF0YQ0KICAgICAgICAgICAgb3JkZXJTdWNjZXNzID0gdHJ1ZQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ+S/neWtmOiNieeov+aIkOWKnyAnKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLnNhdmVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnBnTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgY29uc29sZS5sb2coJ+e7k+adnyAnKQ0KICAgICAgcmV0dXJuIG9yZGVyU3VjY2Vzcw0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKCkgew0KICAgICAgdGhpcy5kZWxldGVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIGNvbnN0IHNlbGVjdGVkVXVpZHMgPSBuZXcgU2V0KHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKHYgPT4gdi51dWlkKSkNCiAgICAgICAgdGhpcy50YkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgICAgY29uc3QgaXNTZWxlY3RlZCA9IHNlbGVjdGVkVXVpZHMuaGFzKGl0ZW0udXVpZCkNCiAgICAgICAgICBpZiAoaXNTZWxlY3RlZCkgew0KICAgICAgICAgICAgY29uc3Qga2V5ID0gdGhpcy5nZXRVbmlLZXkoaXRlbSkNCiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLnRiRGF0YU1hcFtrZXldDQogICAgICAgICAgfQ0KICAgICAgICAgIHJldHVybiAhaXNTZWxlY3RlZA0KICAgICAgICB9KQ0KICAgICAgICAvLyB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgLy8gICBjb25zdCBfbGlzdCA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZmlsdGVyKHYgPT4gdi5wdXVpZCkNCiAgICAgICAgLy8gICB0aGlzLiRyZWZzWydkcmFmdCddPy5tZXJnZURhdGEoX2xpc3QpDQogICAgICAgIC8vICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IFtdDQogICAgICAgIC8vIH0pDQogICAgICAgIHRoaXMuZGVsZXRlTG9hZGluZyA9IGZhbHNlDQogICAgICB9LCAwKQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0V29ya1RlYW0oKSB7DQogICAgICBhd2FpdCBHZXRTY2hkdWxpbmdXb3JraW5nVGVhbXMoew0KICAgICAgICB0eXBlOiB0aGlzLmlzQ29tID8gMSA6IDINCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLndvcmtpbmdUZWFtID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVTdWJtaXQoKSB7DQogICAgICB0aGlzLiRyZWZzWydmb3JtSW5saW5lJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICghdmFsaWQpIHJldHVybg0KICAgICAgICBjb25zdCB7IHRhYmxlRGF0YSwgc3RhdHVzIH0gPSB0aGlzLmdldFN1Ym1pdFRiSW5mbygpDQogICAgICAgIGlmICghc3RhdHVzKSByZXR1cm4NCiAgICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5o+Q5Lqk5b2T5YmN5pWw5o2uPycsICfmj5DnpLonLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnNhdmVEcmFmdERvU3VibWl0KHRhYmxlRGF0YSkNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIHNhdmVEcmFmdERvU3VibWl0KCkgew0KICAgICAgdGhpcy5wZ0xvYWRpbmcgPSB0cnVlDQogICAgICBpZiAodGhpcy5mb3JtSW5saW5lPy5TY2hkdWxpbmdfQ29kZSkgew0KICAgICAgICBjb25zdCBpc1N1Y2Nlc3MgPSBhd2FpdCB0aGlzLnNhdmVEcmFmdCh0cnVlKQ0KICAgICAgICBjb25zb2xlLmxvZygnc2F2ZURyYWZ0RG9TdWJtaXQnLCBpc1N1Y2Nlc3MpDQogICAgICAgIGlzU3VjY2VzcyAmJiB0aGlzLmRvU3VibWl0KHRoaXMuZm9ybUlubGluZS5JZCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnN0IGlzU3VjY2VzcyA9IGF3YWl0IHRoaXMuc2F2ZURyYWZ0KHRydWUpDQogICAgICAgIGlzU3VjY2VzcyAmJiB0aGlzLmRvU3VibWl0KHRoaXMudGVtcGxhdGVTY2hlZHVsZUNvZGUpDQogICAgICB9DQogICAgfSwNCiAgICBkb1N1Ym1pdChzY2hlZHVsZUNvZGUpIHsNCiAgICAgIFNhdmVTY2hkdWxpbmdUYXNrQnlJZCh7DQogICAgICAgIHNjaGR1bGluZ1BsYW5JZDogc2NoZWR1bGVDb2RlDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAn5LiL6L6+5oiQ5YqfJywNCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5jbG9zZVZpZXcoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkuZmluYWxseShfID0+IHsNCiAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkuY2F0Y2goXyA9PiB7DQogICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRXb3JrU2hvcCh2YWx1ZSkgew0KICAgICAgY29uc29sZS5sb2coJ3ZhbHVlJywgdmFsdWUpDQogICAgICBjb25zdCB7DQogICAgICAgIG9yaWdpbiwNCiAgICAgICAgcm93LA0KICAgICAgICB3b3JrU2hvcDogew0KICAgICAgICAgIElkLA0KICAgICAgICAgIERpc3BsYXlfTmFtZQ0KICAgICAgICB9DQogICAgICB9ID0gdmFsdWUNCiAgICAgIGlmIChvcmlnaW4gPT09IDIpIHsNCiAgICAgICAgaWYgKHZhbHVlLndvcmtTaG9wPy5JZCkgew0KICAgICAgICAgIHJvdy5Xb3Jrc2hvcF9OYW1lID0gRGlzcGxheV9OYW1lDQogICAgICAgICAgcm93LldvcmtzaG9wX0lkID0gSWQNCiAgICAgICAgICB0aGlzLnNldFBhdGgocm93LCBJZCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByb3cuV29ya3Nob3BfTmFtZSA9ICcnDQogICAgICAgICAgcm93LldvcmtzaG9wX0lkID0gJycNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmICh2YWx1ZS53b3JrU2hvcD8uSWQpIHsNCiAgICAgICAgICAgIGl0ZW0uV29ya3Nob3BfTmFtZSA9IERpc3BsYXlfTmFtZQ0KICAgICAgICAgICAgaXRlbS5Xb3Jrc2hvcF9JZCA9IElkDQogICAgICAgICAgICB0aGlzLnNldFBhdGgoaXRlbSwgSWQpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGl0ZW0uV29ya3Nob3BfTmFtZSA9ICcnDQogICAgICAgICAgICBpdGVtLldvcmtzaG9wX0lkID0gJycNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBzZXRQYXRoKHJvdywgSWQpIHsNCiAgICAgIGlmIChyb3c/LlNjaGVkdWxlZF9Xb3Jrc2hvcF9JZCkgew0KICAgICAgICBpZiAocm93LlNjaGVkdWxlZF9Xb3Jrc2hvcF9JZCAhPT0gSWQpIHsNCiAgICAgICAgICByb3cuVGVjaG5vbG9neV9QYXRoID0gJycNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcm93LlRlY2hub2xvZ3lfUGF0aCA9ICcnDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVCYXRjaFdvcmtzaG9wKG9yaWdpbiwgcm93KSB7DQogICAgICB0aGlzLnRpdGxlID0gb3JpZ2luID09PSAxID8gJ+aJuemHj+WIhumFjei9pumXtCcgOiAn5YiG6YWN6L2m6Ze0Jw0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1dvcmtzaG9wJw0KICAgICAgdGhpcy5kV2lkdGggPSAnMzAlJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5mZXRjaERhdGEob3JpZ2luLCByb3cpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICBnZXRQcm9jZXNzT3B0aW9uKHdvcmtzaG9wSWQpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7DQogICAgICAgIEdldFByb2Nlc3NMaXN0QmFzZSh7DQogICAgICAgICAgd29ya3Nob3BJZDogd29ya3Nob3BJZCwNCiAgICAgICAgICB0eXBlOiAyIC8vIDA65YWo6YOo77yM5bel6Im657G75Z6LMe+8muaehOS7tuW3peiJuu+8jDLvvJrpm7bku7blt6XoiboNCiAgICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICBjb25zdCBwcm9jZXNzID0gcmVzLkRhdGEubWFwKHYgPT4gdi5Db2RlKQ0KICAgICAgICAgICAgcmVzb2x2ZShwcm9jZXNzKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldExpYlR5cGUoY29kZSwgd29ya3Nob3BJZCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiB7DQogICAgICAgIGNvbnN0IG9iaiA9IHsNCiAgICAgICAgICBDb21wb25lbnRfdHlwZTogY29kZSwNCiAgICAgICAgICB0eXBlOiAxDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMud29ya3Nob3BFbmFibGVkKSB7DQogICAgICAgICAgb2JqLndvcmtzaG9wSWQgPSB3b3Jrc2hvcElkDQogICAgICAgIH0NCiAgICAgICAgR2V0TGliTGlzdFR5cGUob2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGlmIChyZXMuRGF0YS5EYXRhICYmIHJlcy5EYXRhLkRhdGEubGVuZ3RoKSB7DQogICAgICAgICAgICAgIGNvbnN0IGluZm8gPSByZXMuRGF0YS5EYXRhWzBdDQogICAgICAgICAgICAgIGNvbnN0IHdvcmtDb2RlID0gaW5mby5Xb3JrQ29kZSAmJiBpbmZvLldvcmtDb2RlLnJlcGxhY2UoL1xcL2csICcvJykNCiAgICAgICAgICAgICAgcmVzb2x2ZSh3b3JrQ29kZSkNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHJlc29sdmUodW5kZWZpbmVkKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGlucHV0Q2hhbmdlKHJvdykgew0KICAgICAgdGhpcy5zZXRJbnB1dE1heChyb3cpDQogICAgfSwNCiAgICBzZXRJbnB1dE1heChyb3cpIHsNCiAgICAgIGNvbnN0IGlucHV0VmFsdWVzS2V5cyA9IE9iamVjdC5rZXlzKHJvdykNCiAgICAgICAgLmZpbHRlcih2ID0+ICF2LmVuZHNXaXRoKCdtYXgnKSAmJiB2LnN0YXJ0c1dpdGgocm93LnV1aWQpICYmIHYubGVuZ3RoID4gcm93LnV1aWQubGVuZ3RoKQ0KICAgICAgaW5wdXRWYWx1ZXNLZXlzLmZvckVhY2goKHZhbCkgPT4gew0KICAgICAgICBjb25zdCBjdXJDb2RlID0gdmFsLnNwbGl0KFNQTElUX1NZTUJPTClbMV0NCiAgICAgICAgY29uc3Qgb3RoZXJUb3RhbCA9IGlucHV0VmFsdWVzS2V5cy5maWx0ZXIoeCA9PiB7DQogICAgICAgICAgY29uc3QgY29kZSA9IHguc3BsaXQoU1BMSVRfU1lNQk9MKVsxXQ0KICAgICAgICAgIHJldHVybiB4ICE9PSB2YWwgJiYgY29kZSA9PT0gY3VyQ29kZQ0KICAgICAgICB9KS5yZWR1Y2UoKGFjYywgaXRlbSkgPT4gew0KICAgICAgICAgIHJldHVybiBhY2MgKyBudW1lcmFsKHJvd1tpdGVtXSkudmFsdWUoKQ0KICAgICAgICB9LCAwKQ0KICAgICAgICByb3dbdmFsICsgU1BMSVRfU1lNQk9MICsgJ21heCddID0gcm93LlNjaGR1bGVkX0NvdW50IC0gb3RoZXJUb3RhbA0KICAgICAgfSkNCiAgICB9LA0KICAgIHNlbmRQcm9jZXNzKHsgYXJyLCBzdHIgfSkgew0KICAgICAgbGV0IGlzU3VjY2VzcyA9IHRydWUNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYXJyLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGl0ZW0gPSBhcnJbaV0NCiAgICAgICAgaWYgKGl0ZW0ub3JpZ2luYWxQYXRoICYmIGl0ZW0ub3JpZ2luYWxQYXRoICE9PSBzdHIpIHsNCiAgICAgICAgICBpc1N1Y2Nlc3MgPSBmYWxzZQ0KICAgICAgICAgIGJyZWFrDQogICAgICAgIH0NCiAgICAgICAgaXRlbS5UZWNobm9sb2d5X1BhdGggPSBzdHINCiAgICAgIH0NCiAgICAgIGlmICghaXNTdWNjZXNzKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfor7flkozor6XljLrln5/mibnmrKHkuIvlt7LmjpLkuqflkIzmnoTku7bkv53mjIHlt6Xluo/kuIDoh7QnLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgcmVzZXRXb3JrVGVhbU1heChyb3csIHN0cikgew0KICAgICAgaWYgKHN0cikgew0KICAgICAgICByb3cuVGVjaG5vbG9neV9QYXRoID0gc3RyDQogICAgICB9IGVsc2Ugew0KICAgICAgICBzdHIgPSByb3cuVGVjaG5vbG9neV9QYXRoDQogICAgICB9DQogICAgICBjb25zdCBsaXN0ID0gc3RyPy5zcGxpdCgnLycpIHx8IFtdDQogICAgICB0aGlzLndvcmtpbmdUZWFtLmZvckVhY2goKGVsZW1lbnQsIGlkeCkgPT4gew0KICAgICAgICBjb25zdCBjdXIgPSBsaXN0LnNvbWUoayA9PiBrID09PSBlbGVtZW50LlByb2Nlc3NfQ29kZSkNCiAgICAgICAgY29uc3QgY29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKHJvdy51dWlkLCBlbGVtZW50LlByb2Nlc3NfQ29kZSwgZWxlbWVudC5Xb3JraW5nX1RlYW1fSWQpDQogICAgICAgIGNvbnN0IG1heCA9IHRoaXMuZ2V0Um93VW5pcXVlTWF4KHJvdy51dWlkLCBlbGVtZW50LlByb2Nlc3NfQ29kZSwgZWxlbWVudC5Xb3JraW5nX1RlYW1fSWQpDQogICAgICAgIGlmIChjdXIpIHsNCiAgICAgICAgICBpZiAoIXJvd1tjb2RlXSkgew0KICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgY29kZSwgMCkNCiAgICAgICAgICAgIHRoaXMuJHNldChyb3csIG1heCwgcm93LlNjaGR1bGVkX0NvdW50KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRkZWxldGUocm93LCBjb2RlKQ0KICAgICAgICAgIHRoaXMuJGRlbGV0ZShyb3csIG1heCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNoZWNrUGVybWlzc2lvblRlYW0ocHJvY2Vzc1N0ciwgcHJvY2Vzc0NvZGUpIHsNCiAgICAgIGlmICghcHJvY2Vzc1N0cikgcmV0dXJuIGZhbHNlDQogICAgICBjb25zdCBsaXN0ID0gcHJvY2Vzc1N0cj8uc3BsaXQoJy8nKSB8fCBbXQ0KICAgICAgcmV0dXJuICEhbGlzdC5zb21lKHYgPT4gdiA9PT0gcHJvY2Vzc0NvZGUpDQogICAgfSwNCg0KICAgIGFzeW5jIGdldFRhYmxlQ29uZmlnKGNvZGUpIHsNCiAgICAgIGF3YWl0IEdldEdyaWRCeUNvZGUoew0KICAgICAgICBjb2RlDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgY29uc3QgeyBJc1N1Y2NlZWQsIERhdGEsIE1lc3NhZ2UgfSA9IHJlcw0KICAgICAgICBpZiAoSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy50YkNvbmZpZyA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMudGJDb25maWcsIERhdGEuR3JpZCkNCiAgICAgICAgICBjb25zdCBsaXN0ID0gRGF0YS5Db2x1bW5MaXN0IHx8IFtdDQogICAgICAgICAgdGhpcy5vd25lckNvbHVtbiA9IGxpc3QuZmluZChpdGVtID0+IGl0ZW0uQ29kZSA9PT0gJ1BhcnRfVXNlZF9Qcm9jZXNzJykNCiAgICAgICAgICB0aGlzLm93bmVyQ29sdW1uMiA9IGxpc3QuZmluZChpdGVtID0+IGl0ZW0uQ29kZSA9PT0gJ0lzX01haW5fUGFydCcpDQogICAgICAgICAgdGhpcy5jb2x1bW5zID0gdGhpcy5zZXRDb2x1bW5EaXNwbGF5KGxpc3QpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiBNZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzZXRDb2x1bW5EaXNwbGF5KGxpc3QpIHsNCiAgICAgIHJldHVybiBsaXN0LmZpbHRlcih2ID0+IHYuSXNfRGlzcGxheSkNCiAgICAgIC8vIC5tYXAoaXRlbSA9PiB7DQogICAgICAvLyAgIGlmIChGSVhfQ09MVU1OLmluY2x1ZGVzKGl0ZW0uQ29kZSkpIHsNCiAgICAgIC8vICAgICBpdGVtLmZpeGVkID0gJ2xlZnQnDQogICAgICAvLyAgIH0NCiAgICAgIC8vICAgcmV0dXJuIGl0ZW0NCiAgICAgIC8vIH0pDQogICAgfSwNCiAgICBhY3RpdmVDZWxsTWV0aG9kKHsgcm93LCBjb2x1bW4sIGNvbHVtbkluZGV4IH0pIHsNCiAgICAgIGlmICh0aGlzLmlzVmlldykgcmV0dXJuIGZhbHNlDQogICAgICBjb25zdCBwcm9jZXNzQ29kZSA9IGNvbHVtbi5maWVsZD8uc3BsaXQoJyRfJCcpWzFdDQogICAgICByZXR1cm4gdGhpcy5jaGVja1Blcm1pc3Npb25UZWFtKHJvdy5UZWNobm9sb2d5X1BhdGgsIHByb2Nlc3NDb2RlKQ0KICAgIH0sDQogICAgb3BlbkJQQURpYWxvZyh0eXBlLCByb3cpIHsNCiAgICAgIGlmICh0aGlzLndvcmtzaG9wRW5hYmxlZCkgew0KICAgICAgICBpZiAodHlwZSA9PT0gMSkgew0KICAgICAgICAgIGNvbnN0IElzVW5pcXVlID0gdGhpcy5jaGVja0lzVW5pcXVlV29ya3Nob3AoKQ0KICAgICAgICAgIGlmICghSXNVbmlxdWUpIHJldHVybg0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLnRpdGxlID0gdHlwZSA9PT0gMiA/ICflt6Xluo/osIPmlbQnIDogJ+aJuemHj+W3peW6j+iwg+aVtCcNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdCYXRjaFByb2Nlc3NBZGp1c3QnDQogICAgICB0aGlzLmRXaWR0aCA9ICc1MCUnDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLnNldERhdGEodHlwZSA9PT0gMiA/IFtyb3ddIDogdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiwgdHlwZSA9PT0gMiA/IHJvdy5UZWNobm9sb2d5X1BhdGggOiAnJykNCiAgICAgIH0pDQogICAgfSwNCiAgICBjaGVja0lzVW5pcXVlV29ya3Nob3AoKSB7DQogICAgICBsZXQgaXNVbmlxdWUgPSB0cnVlDQogICAgICBjb25zdCBmaXJzdFYgPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uWzBdLldvcmtzaG9wX05hbWUNCiAgICAgIGZvciAobGV0IGkgPSAxOyBpIDwgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGg7IGkrKykgew0KICAgICAgICBjb25zdCBpdGVtID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbltpXQ0KICAgICAgICBpZiAoaXRlbS5Xb3Jrc2hvcF9OYW1lICE9PSBmaXJzdFYpIHsNCiAgICAgICAgICBpc1VuaXF1ZSA9IGZhbHNlDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKCFpc1VuaXF1ZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5om56YeP5YiG6YWN5bel5bqP5pe25Y+q5pyJ55u45ZCM6L2m6Ze05LiL55qE5omN5Y+v5LiA6LW35om56YeP5YiG6YWNJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHJldHVybiBpc1VuaXF1ZQ0KICAgIH0sDQogICAgY2hlY2tIYXNXb3JrU2hvcCh0eXBlLCBhcnIpIHsNCiAgICAgIGxldCBoYXNXb3JrU2hvcCA9IHRydWUNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYXJyLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGl0ZW0gPSBhcnJbaV0NCiAgICAgICAgaWYgKCFpdGVtLldvcmtzaG9wX05hbWUpIHsNCiAgICAgICAgICBoYXNXb3JrU2hvcCA9IGZhbHNlDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKCFoYXNXb3JrU2hvcCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn6K+35YWI6YCJ5oup6L2m6Ze05ZCO5YaN6L+b6KGM5bel5bqP5YiG6YWNJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHJldHVybiBoYXNXb3JrU2hvcA0KICAgIH0sDQogICAgaGFuZGxlQWRkRGlhbG9nKHR5cGUgPSAnYWRkJykgew0KICAgICAgdGhpcy50aXRsZSA9IGDmt7vliqAke3RoaXMucGFydE5hbWV9YA0KDQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnQWRkRHJhZnQnDQogICAgICB0aGlzLmRXaWR0aCA9ICc5NiUnDQogICAgICB0aGlzLm9wZW5BZGREcmFmdCA9IHRydWUNCg0KICAgICAgdGhpcy5zZXRBZGRUYktleSgpDQoNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydkcmFmdCddLmluaXREYXRhKCkNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBhZGRUb1RiTGlzdChuZXdMaXN0KSB7DQogICAgICBhd2FpdCB0aGlzLm1lcmdlU2VsZWN0TGlzdChuZXdMaXN0KQ0KICAgICAgdGhpcy5zZXRBZGRUYktleSgpDQogICAgfSwNCiAgICBzZXRBZGRUYktleSgpIHsNCiAgICAgIGNvbnN0IHNlbGVjdEtleXMgPSB0aGlzLnRiRGF0YS5maWx0ZXIoY3VyID0+IGN1ci5wdXVpZCkubWFwKHYgPT4gdi5wdXVpZCkNCiAgICAgIHRoaXMuY2hhbmdlQWRkVGJLZXlzKHNlbGVjdEtleXMpDQogICAgfSwNCiAgICBnZXRSb3dVbmlxdWUodXVpZCwgcHJvY2Vzc0NvZGUsIHdvcmtpbmdJZCkgew0KICAgICAgcmV0dXJuIGAke3V1aWR9JHtTUExJVF9TWU1CT0x9JHtwcm9jZXNzQ29kZX0ke1NQTElUX1NZTUJPTH0ke3dvcmtpbmdJZH1gDQogICAgfSwNCiAgICBnZXRSb3dVbmlxdWVNYXgodXVpZCwgcHJvY2Vzc0NvZGUsIHdvcmtpbmdJZCkgew0KICAgICAgcmV0dXJuIHRoaXMuZ2V0Um93VW5pcXVlKHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpICsgYCR7U1BMSVRfU1lNQk9MfW1heGANCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdE1lbnUodikgew0KICAgICAgaWYgKHYgPT09ICdwcm9jZXNzJykgew0KICAgICAgICB0aGlzLm9wZW5CUEFEaWFsb2coMSkNCiAgICAgIH0gZWxzZSBpZiAodiA9PT0gJ2NyYWZ0Jykgew0KICAgICAgICB0aGlzLmhhbmRsZVNldENyYWZ0UHJvY2VzcygpDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVTZXRDcmFmdFByb2Nlc3MoKSB7DQogICAgICBjb25zdCBzaG93U3VjY2VzcyA9ICgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+W3suWIhumFjeaIkOWKnycsDQogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgIH0pDQogICAgICB9DQogICAgICBjb25zdCByb3dMaXN0ID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5tYXAodiA9PiB2LlRlY2hub2xvZ3lfQ29kZSkuZmlsdGVyKHYgPT4gISF2KQ0KICAgICAgaWYgKCFyb3dMaXN0Lmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5bel6Im65Luj56CB5LiN5a2Y5ZyoJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBhd2FpdCB0aGlzLm1lcmdlQ3JhZnRQcm9jZXNzKHRoaXMubXVsdGlwbGVTZWxlY3Rpb24pDQogICAgICBjb25zdCB3b3Jrc2hvcElkcyA9IEFycmF5LmZyb20obmV3IFNldCh0aGlzLm11bHRpcGxlU2VsZWN0aW9uLm1hcCh2ID0+IHYuV29ya3Nob3BfSWQpLmZpbHRlcih2ID0+ICEhdikpKQ0KICAgICAgY29uc3Qgd19wcm9jZXNzID0gW10NCiAgICAgIGlmICh3b3Jrc2hvcElkcy5sZW5ndGgpIHsNCiAgICAgICAgd29ya3Nob3BJZHMuZm9yRWFjaCh3b3Jrc2hvcElkID0+IHsNCiAgICAgICAgICB3X3Byb2Nlc3MucHVzaCh0aGlzLmdldFByb2Nlc3NPcHRpb24od29ya3Nob3BJZCkudGhlbihyZXN1bHQgPT4gKHsNCiAgICAgICAgICAgIFt3b3Jrc2hvcElkXTogcmVzdWx0DQogICAgICAgICAgfSkpKQ0KICAgICAgICB9KQ0KICAgICAgICBjb25zdCB3b3Jrc2hvcFByb21pc2UgPSBQcm9taXNlLmFsbCh3X3Byb2Nlc3MpLnRoZW4oKHZhbHVlcykgPT4gew0KICAgICAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCAuLi52YWx1ZXMpDQogICAgICAgIH0pDQogICAgICAgIHdvcmtzaG9wUHJvbWlzZS50aGVuKHdvcmtzaG9wID0+IHsNCiAgICAgICAgICBsZXQgZmxhZyA9IHRydWUNCiAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICAgIGNvbnN0IGN1clJvdyA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25baV0NCiAgICAgICAgICAgIGNvbnN0IHdvcmtzaG9wUHJvY2VzcyA9IHdvcmtzaG9wW2N1clJvdy5Xb3Jrc2hvcF9JZF0NCiAgICAgICAgICAgIGNvbnN0IGNyYWZ0QXJyYXkgPSB0aGlzLmNyYWZ0Q29kZU1hcFtjdXJSb3cuVGVjaG5vbG9neV9Db2RlXQ0KICAgICAgICAgICAgaWYgKGNyYWZ0QXJyYXkpIHsNCiAgICAgICAgICAgICAgY29uc3QgaXNJbmNsdWRlZCA9IGNyYWZ0QXJyYXkuZXZlcnkocHJvY2VzcyA9PiB3b3Jrc2hvcFByb2Nlc3MuaW5jbHVkZXMocHJvY2VzcykpDQogICAgICAgICAgICAgIGlmICghaXNJbmNsdWRlZCkgew0KICAgICAgICAgICAgICAgIGZsYWcgPSBmYWxzZQ0KICAgICAgICAgICAgICAgIGNvbnRpbnVlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgY3VyUm93LlRlY2hub2xvZ3lfUGF0aCA9IGNyYWZ0QXJyYXkuam9pbignLycpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmICghZmxhZykgew0KICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJGFsZXJ0KCfmiYDpgInovabpl7TkuIvnj63nu4TliqDlt6Xlt6Xluo/kuI3ljIXlkKvlt6Xoibrku6PnoIHlt6Xluo/or7fmiYvliqjmjpLkuqcnLCAn5o+Q56S6Jywgew0KICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfSwgMjAwKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGZsYWcgJiYgc2hvd1N1Y2Nlc3MoKQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKChjdXJSb3cpID0+IHsNCiAgICAgICAgICBjb25zdCBjcmFmdEFycmF5ID0gdGhpcy5jcmFmdENvZGVNYXBbY3VyUm93LlRlY2hub2xvZ3lfQ29kZV0NCiAgICAgICAgICBpZiAoY3JhZnRBcnJheSkgew0KICAgICAgICAgICAgY3VyUm93LlRlY2hub2xvZ3lfUGF0aCA9IGNyYWZ0QXJyYXkuam9pbignLycpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICBzaG93U3VjY2VzcygpDQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZUJhdGNoT3duZXIodHlwZSwgcm93KSB7DQogICAgICB0aGlzLnRpdGxlID0gYCR7dHlwZSA9PT0gMSA/ICfmibnph48nIDogJyd95YiG6YWN6aKG55So5bel5bqPYA0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ093bmVyUHJvY2VzcycNCiAgICAgIHRoaXMuZFdpZHRoID0gJzMwJScNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uc2V0T3B0aW9uKHR5cGUgPT09IDIsIHR5cGUgPT09IDIgPyBbcm93XSA6IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24pDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlUmV2ZXJzZSgpIHsNCiAgICAgIGNvbnN0IGN1ciA9IFtdDQogICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgICAgZWxlbWVudC5jaGVja2VkID0gIWVsZW1lbnQuY2hlY2tlZA0KICAgICAgICBpZiAoZWxlbWVudC5jaGVja2VkKSB7DQogICAgICAgICAgY3VyLnB1c2goZWxlbWVudCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBjdXINCiAgICAgIGlmICh0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aCA9PT0gdGhpcy50YkRhdGEubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuJHJlZnNbJ3hUYWJsZSddLnNldEFsbENoZWNrYm94Um93KHRydWUpDQogICAgICB9DQogICAgICBpZiAodGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kcmVmc1sneFRhYmxlJ10uc2V0QWxsQ2hlY2tib3hSb3coZmFsc2UpDQogICAgICB9DQogICAgfSwNCiAgICAvLyB0YkZpbHRlckNoYW5nZSgpIHsNCiAgICAvLyAgIGNvbnN0IHhUYWJsZSA9IHRoaXMuJHJlZnMueFRhYmxlDQogICAgLy8gICBjb25zdCBjb2x1bW4gPSB4VGFibGUuZ2V0Q29sdW1uQnlGaWVsZCgnVHlwZV9OYW1lJykNCiAgICAvLyAgIGlmICghY29sdW1uPy5maWx0ZXJzPy5sZW5ndGgpIHJldHVybg0KICAgIC8vICAgY29sdW1uLmZpbHRlcnMuZm9yRWFjaChkID0+IHsNCiAgICAvLyAgICAgZC5jaGVja2VkID0gZC52YWx1ZSA9PT0gdGhpcy5zZWFyY2hUeXBlDQogICAgLy8gICB9KQ0KICAgIC8vICAgeFRhYmxlLnVwZGF0ZURhdGEoKQ0KICAgIC8vIH0sDQogICAgZ2V0VHlwZSgpIHsNCiAgICAgIGNvbnN0IGdldENvbXBUcmVlID0gKCkgPT4gew0KICAgICAgICBjb25zdCBmdW4gPSB0aGlzLmlzQ29tID8gR2V0Q29tcFR5cGVUcmVlIDogR2V0UGFydFR5cGVMaXN0DQogICAgICAgIGZ1bih7fSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICBsZXQgcmVzdWx0ID0gcmVzLkRhdGENCiAgICAgICAgICAgIGlmICghdGhpcy5pc0NvbSkgew0KICAgICAgICAgICAgICByZXN1bHQgPSByZXN1bHQNCiAgICAgICAgICAgICAgICAubWFwKCh2LCBpZHgpID0+IHsNCiAgICAgICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgICAgIERhdGE6IHYuTmFtZSwNCiAgICAgICAgICAgICAgICAgICAgTGFiZWw6IHYuTmFtZQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLnRyZWVQYXJhbXNDb21wb25lbnRUeXBlLmRhdGEgPSByZXN1bHQNCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVNlbGVjdENvbXBvbmVudFR5cGU/LnRyZWVEYXRhVXBkYXRlRnVuKHJlc3VsdCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQoNCiAgICAgIGdldENvbXBUcmVlKCkNCiAgICB9LA0KICAgIC8vIOafpeeci+Wbvue6uA0KICAgIGhhbmRsZUR3Zyhyb3cpIHsNCiAgICAgIGNvbnN0IG9iaiA9IHt9DQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBvYmouQ29tcF9JZCA9IHJvdy5Db21wX0ltcG9ydF9EZXRhaWxfSWQNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIG9iai5QYXJ0X0lkID0gcm93LlBhcnRfQWdncmVnYXRlX0lkDQogICAgICB9DQoNCiAgICAgIEdldFN0ZWVsQ2FkQW5kQmltSWQoeyBpbXBvcnREZXRhaWxJZDogb2JqLlBhcnRfSWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5leHRlbnNpb25OYW1lID0gcmVzLkRhdGFbMF0uRXh0ZW5zaW9uTmFtZQ0KICAgICAgICAgIHRoaXMuZmlsZUJpbSA9IHJlcy5EYXRhWzBdLmZpbGVCaW0NCiAgICAgICAgICB0aGlzLklzVXBsb2FkQ2FkID0gcmVzLkRhdGFbMF0uSXNVcGxvYWQNCiAgICAgICAgICB0aGlzLmNhZFJvd0NvZGUgPSByb3cuUGFydF9Db2RlDQogICAgICAgICAgdGhpcy5jYWRSb3dQcm9qZWN0SWQgPSByb3cuU3lzX1Byb2plY3RfSWQNCiAgICAgICAgICB0aGlzLmZpbGVWaWV3KCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIC8vIEdldER3ZyhvYmopLnRoZW4ocmVzID0+IHsNCiAgICAgIC8vICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgIC8vICAgICBjb25zdCBmaWxldXJsID0gcmVzPy5EYXRhPy5sZW5ndGggJiYgcmVzLkRhdGFbMF0uRmlsZV9VcmwNCiAgICAgIC8vICAgICB3aW5kb3cub3BlbignaHR0cDovL2R3Z3YxLmJpbXRrLmNvbTo1NDMyLz9DYWRVcmw9JyArIHBhcnNlT3NzVXJsKGZpbGV1cmwpLCAnX2JsYW5rJykNCiAgICAgIC8vICAgfSBlbHNlIHsNCiAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgIC8vICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgLy8gICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgLy8gICAgIH0pDQogICAgICAvLyAgIH0NCiAgICAgIC8vIH0pDQogICAgfSwNCiAgICBzZXRQcm9jZXNzTGlzdChpbmZvKSB7DQogICAgICB0aGlzLmNoYW5nZVByb2Nlc3NMaXN0KGluZm8pDQogICAgfSwNCiAgICByZXNldElubmVyRm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ3NlYXJjaEZvcm0nXS5yZXNldEZpZWxkcygpDQogICAgICB0aGlzLiRyZWZzLnhUYWJsZS5jbGVhckZpbHRlcigpDQogICAgfSwNCiAgICBpbm5lckZpbHRlcigpIHsNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBbXQ0KICAgICAgY29uc3QgYXJyID0gW10NCiAgICAgIGlmICh0aGlzLmlzQ29tKSB7DQogICAgICAgIGFyci5wdXNoKCdUeXBlJywgJ0NvbXBfQ29kZScsICdTcGVjJywgJ0lzX0NvbXBvbmVudCcpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBhcnIucHVzaCgnUGFydF9Db2RlJywgJ1NwZWMnLCAnVHlwZV9OYW1lJywgJ1Byb2plY3RfTmFtZScsICdBcmVhX05hbWUnLCAnSW5zdGFsbFVuaXRfTmFtZScpDQogICAgICB9DQoNCiAgICAgIGNvbnN0IHhUYWJsZSA9IHRoaXMuJHJlZnMueFRhYmxlDQogICAgICB4VGFibGUuY2xlYXJDaGVja2JveFJvdygpDQogICAgICBhcnIuZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgIGNvbnN0IGNvbHVtbiA9IHhUYWJsZS5nZXRDb2x1bW5CeUZpZWxkKGVsZW1lbnQpDQogICAgICAgIGlmIChlbGVtZW50ID09PSAnSXNfQ29tcG9uZW50Jykgew0KICAgICAgICAgIGNvbHVtbi5maWx0ZXJzLmZvckVhY2goKG9wdGlvbiwgaWR4KSA9PiB7DQogICAgICAgICAgICBvcHRpb24uY2hlY2tlZCA9IGlkeCA9PT0gKHRoaXMuaW5uZXJGb3JtLnNlYXJjaERpcmVjdCA/IDAgOiAxKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgICAgaWYgKGVsZW1lbnQgPT09ICdTcGVjJykgew0KICAgICAgICAgIGNvbnN0IG9wdGlvbiA9IGNvbHVtbi5maWx0ZXJzWzBdDQogICAgICAgICAgb3B0aW9uLmRhdGEgPSB0aGlzLmlubmVyRm9ybS5zZWFyY2hTcGVjU2VhcmNoDQogICAgICAgICAgb3B0aW9uLmNoZWNrZWQgPSB0cnVlDQogICAgICAgIH0NCiAgICAgICAgaWYgKGVsZW1lbnQgPT09ICdUeXBlJyB8fCBlbGVtZW50ID09PSAnVHlwZV9OYW1lJykgew0KICAgICAgICAgIGNvbnN0IG9wdGlvbiA9IGNvbHVtbi5maWx0ZXJzWzBdDQogICAgICAgICAgb3B0aW9uLmRhdGEgPSB0aGlzLmlubmVyRm9ybS5zZWFyY2hDb21UeXBlU2VhcmNoDQogICAgICAgICAgb3B0aW9uLmNoZWNrZWQgPSB0cnVlDQogICAgICAgIH0NCiAgICAgICAgaWYgKGVsZW1lbnQgPT09ICdDb21wX0NvZGUnIHx8IGVsZW1lbnQgPT09ICdQYXJ0X0NvZGUnKSB7DQogICAgICAgICAgY29uc3Qgb3B0aW9uID0gY29sdW1uLmZpbHRlcnNbMF0NCiAgICAgICAgICBvcHRpb24uZGF0YSA9IHRoaXMuaW5uZXJGb3JtLnNlYXJjaENvbnRlbnQNCiAgICAgICAgICBvcHRpb24uY2hlY2tlZCA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZWxlbWVudCA9PT0gJ1Byb2plY3RfTmFtZScpIHsNCiAgICAgICAgICBjb25zdCBvcHRpb24gPSBjb2x1bW4uZmlsdGVyc1swXQ0KICAgICAgICAgIG9wdGlvbi5kYXRhID0gdGhpcy5pbm5lckZvcm0ucHJvamVjdE5hbWUNCiAgICAgICAgICBvcHRpb24uY2hlY2tlZCA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZWxlbWVudCA9PT0gJ0FyZWFfTmFtZScpIHsNCiAgICAgICAgICBjb25zdCBvcHRpb24gPSBjb2x1bW4uZmlsdGVyc1swXQ0KICAgICAgICAgIG9wdGlvbi5kYXRhID0gdGhpcy5pbm5lckZvcm0uYXJlYU5hbWUNCiAgICAgICAgICBvcHRpb24uY2hlY2tlZCA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZWxlbWVudCA9PT0gJ0luc3RhbGxVbml0X05hbWUnKSB7DQogICAgICAgICAgY29uc3Qgb3B0aW9uID0gY29sdW1uLmZpbHRlcnNbMF0NCiAgICAgICAgICBvcHRpb24uZGF0YSA9IHRoaXMuaW5uZXJGb3JtLmluc3RhbGxOYW1lDQogICAgICAgICAgb3B0aW9uLmNoZWNrZWQgPSB0cnVlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICB4VGFibGUudXBkYXRlRGF0YSgpDQogICAgfSwNCiAgICBmaWx0ZXJDb21wb25lbnRNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAodGhpcy5pbm5lckZvcm0uc2VhcmNoRGlyZWN0ID09PSAnJykgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHJvdy5Jc19Db21wb25lbnQgPT09ICF0aGlzLmlubmVyRm9ybS5zZWFyY2hEaXJlY3QNCiAgICB9LA0KICAgIGZpbHRlclNwZWNNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAodGhpcy5pbm5lckZvcm0uc2VhcmNoU3BlY1NlYXJjaC50cmltKCkgPT09ICcnKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQogICAgICBjb25zdCBzcGxpdEFuZENsZWFuID0gKGlucHV0KSA9PiBpbnB1dC50cmltKCkucmVwbGFjZSgvXHMrL2csICcgJykuc3BsaXQoJyAnKQ0KICAgICAgY29uc3Qgc3BlY0FycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmlubmVyRm9ybS5zZWFyY2hTcGVjU2VhcmNoKQ0KICAgICAgcmV0dXJuIHNwZWNBcnJheS5zb21lKGNvZGUgPT4gKHJvdy5TcGVjIHx8ICcnKS5pbmNsdWRlcyhjb2RlKSkNCiAgICB9LA0KDQogICAgZmlsdGVyVHlwZU1ldGhvZCh7IG9wdGlvbiwgcm93IH0pIHsNCiAgICAgIGlmICh0aGlzLmlubmVyRm9ybS5zZWFyY2hDb21UeXBlU2VhcmNoID09PSAnJykgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KICAgICAgY29uc3QgY3VyID0gdGhpcy5pc0NvbSA/ICdUeXBlJyA6ICdUeXBlX05hbWUnDQogICAgICByZXR1cm4gcm93W2N1cl0gPT09IHRoaXMuaW5uZXJGb3JtLnNlYXJjaENvbVR5cGVTZWFyY2gNCiAgICB9LA0KICAgIGZpbHRlckNvZGVNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAodGhpcy5pbm5lckZvcm0uc2VhcmNoQ29udGVudC50cmltKCkgPT09ICcnKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQoNCiAgICAgIGNvbnN0IHNwbGl0QW5kQ2xlYW4gPSAoaW5wdXQpID0+IGlucHV0LnRyaW0oKS5yZXBsYWNlKC9ccysvZywgJyAnKS5zcGxpdCgnICcpDQoNCiAgICAgIGNvbnN0IGN1ciA9IHRoaXMuaXNDb20gPyAnQ29tcF9Db2RlJyA6ICdQYXJ0X0NvZGUnDQoNCiAgICAgIGNvbnN0IGFyciA9IHNwbGl0QW5kQ2xlYW4odGhpcy5pbm5lckZvcm0uc2VhcmNoQ29udGVudCkNCg0KICAgICAgaWYgKHRoaXMuY3VyU2VhcmNoID09PSAxKSB7DQogICAgICAgIHJldHVybiBhcnIuc29tZShjb2RlID0+IHJvd1tjdXJdID09PSBjb2RlKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICBjb25zdCBpdGVtID0gYXJyW2ldDQogICAgICAgICAgaWYgKHJvd1tjdXJdLmluY2x1ZGVzKGl0ZW0pKSB7DQogICAgICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KICAgIGZpbHRlclByb2plY3RNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAob3B0aW9uLmRhdGEgPT09ICcnKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQogICAgICByZXR1cm4gcm93LlByb2plY3RfTmFtZSA9PT0gb3B0aW9uLmRhdGENCiAgICB9LA0KICAgIGZpbHRlckFyZWFNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAob3B0aW9uLmRhdGEgPT09ICcnKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQogICAgICByZXR1cm4gcm93LkFyZWFfTmFtZSA9PT0gb3B0aW9uLmRhdGENCiAgICB9LA0KICAgIGZpbHRlckluc3RhbGxNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAob3B0aW9uLmRhdGEgPT09ICcnKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQogICAgICByZXR1cm4gcm93Lkluc3RhbGxVbml0X05hbWUgPT09IG9wdGlvbi5kYXRhDQogICAgfSwNCiAgICBjb21wb25lbnRUeXBlRmlsdGVyKGUpIHsNCiAgICAgIHRoaXMuJHJlZnM/LnRyZWVTZWxlY3RDb21wb25lbnRUeXBlLmZpbHRlckZ1bihlKQ0KICAgIH0sDQogICAgZ2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0KGlkKSB7DQogICAgICBpZiAoIXRoaXMuYXJlYUlkKSB7DQogICAgICAgIHRoaXMuaW5zdGFsbFVuaXRJZExpc3QgPSBbXQ0KICAgICAgICB0aGlzLmRpc2FibGVkQWRkID0gZmFsc2UNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZGlzYWJsZWRBZGQgPSB0cnVlDQogICAgICAgIEdldEluc3RhbGxVbml0SWROYW1lTGlzdCh7IEFyZWFfSWQ6IHRoaXMuYXJlYUlkIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICB0aGlzLmluc3RhbGxVbml0SWRMaXN0ID0gcmVzLkRhdGENCiAgICAgICAgICBpZiAodGhpcy5pbnN0YWxsVW5pdElkTGlzdC5sZW5ndGgpIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybUlubGluZS5JbnN0YWxsVW5pdF9JZCA9IHRoaXMuaW5zdGFsbFVuaXRJZExpc3RbMF0uSWQNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5kaXNhYmxlZEFkZCA9IGZhbHNlDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBpbnN0YWxsQ2hhbmdlKCkgew0KICAgICAgaWYgKCF0aGlzLnRiRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kcmVmc1snc2VhcmNoRm9ybSddLnJlc2V0RmllbGRzKCkNCiAgICAgICAgdGhpcy4kcmVmcy54VGFibGUuY2xlYXJGaWx0ZXIoKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+WIh+aNouWMuuWfn+WPs+S+p+aVsOaNrua4heepuiwg5piv5ZCm56Gu6K6kPycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgICAgdGhpcy5yZXNldElubmVyRm9ybSgpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNob3dQYXJ0VXNlZFByb2Nlc3Mocm93KSB7DQogICAgICBpZiAodGhpcy5pc05lc3QpIHsNCiAgICAgICAgcmV0dXJuICEhcm93LkNvbXBfSW1wb3J0X0RldGFpbF9JZA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICF0aGlzLmlzVmlldyAmJiByb3cuVHlwZSAhPT0gJ0RpcmVjdCcNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIGlmICghdGhpcy50YkRhdGEubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfmmoLml6DmlbDmja4nLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuJHJlZnMueFRhYmxlLmV4cG9ydERhdGEoew0KICAgICAgICBmaWxlbmFtZTogYCR7dGhpcy5wYXJ0TmFtZX3mjpLkuqctJHt0aGlzLmZvcm1JbmxpbmUuU2NoZHVsaW5nX0NvZGV9KCR7dGhpcy5wYXJ0TmFtZX0pYCwNCiAgICAgICAgdHlwZTogJ3hsc3gnLA0KICAgICAgICBkYXRhOiB0aGlzLnRiRGF0YQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlRHJhd2VyKCkgew0KICAgICAgdGhpcy5kcmF3ZXIgPSBmYWxzZQ0KICAgIH0sDQogICAgZmlsZVZpZXcoKSB7DQogICAgICB0aGlzLmlmcmFtZUtleSA9IHV1aWR2NCgpDQogICAgICB0aGlzLmlmcmFtZVVybCA9IGAkew0KICAgICAgICB0aGlzLmJhc2VDYWRVcmwNCiAgICAgIH0/cm91dGVyPTEmaWZyYW1lSWQ9MTEmYmFzZVVybD0ke2Jhc2VVcmwoKX0mdG9rZW49JHtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgNCiAgICAgICAgJ1Rva2VuJw0KICAgICAgKX0mYXV0aF9pZD0ke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJyl9YA0KICAgICAgdGhpcy5kcmF3ZXIgPSB0cnVlDQogICAgfSwNCiAgICByZW5kZXJJZnJhbWUoKSB7DQogICAgICBjb25zdCBFeHRlbnNpb25OYW1lID0gdGhpcy5leHRlbnNpb25OYW1lDQogICAgICBjb25zdCBmaWxlQmltID0gdGhpcy5maWxlQmltDQogICAgICB0aGlzLmlmcmFtZVVybCA9IGAkew0KICAgICAgICB0aGlzLmJhc2VDYWRVcmwNCiAgICAgIH0/cm91dGVyPTEmaWZyYW1lSWQ9MTEmYmFzZVVybD0ke2Jhc2VVcmwoKX0mdG9rZW49JHtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgNCiAgICAgICAgJ1Rva2VuJw0KICAgICAgKX0mYXV0aF9pZD0ke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJyl9YA0KICAgICAgdGhpcy5mdWxsc2NyZWVuaWQgPSBFeHRlbnNpb25OYW1lDQogICAgICB0aGlzLmZ1bGxiaW1pZCA9IGZpbGVCaW0NCiAgICB9LA0KICAgIGZ1bGxzY3JlZW4odikgew0KICAgICAgdGhpcy50ZW1wbGF0ZVVybCA9IGAkew0KICAgICAgICB0aGlzLmJhc2VDYWRVcmwNCiAgICAgIH0/cm91dGVyPTEmaWZyYW1lSWQ9MTMmYmFzZVVybD0ke2Jhc2VVcmwoKX0mdG9rZW49JHtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgNCiAgICAgICAgJ1Rva2VuJw0KICAgICAgKX0mYXV0aF9pZD0ke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJyl9YA0KICAgICAgdGhpcy5kcmF3ZXJzdWxsID0gdHJ1ZQ0KICAgIH0sDQogICAgZnJhbWVMaXN0ZW5lcih7IGRhdGEgfSkgew0KICAgICAgaWYgKGRhdGEudHlwZSA9PT0gJ2xvYWRlZCcpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ2RhdGEnLCBkYXRhKQ0KICAgICAgICBjb25zb2xlLmVycm9yKA0KICAgICAgICAgICdkYXRhLmRhdGEuaWZyYW1lSWQnLA0KICAgICAgICAgIGRhdGEuZGF0YS5pZnJhbWVJZCwNCiAgICAgICAgICB0eXBlb2YgZGF0YS5kYXRhLmlmcmFtZUlkDQogICAgICAgICkNCiAgICAgICAgaWYgKGRhdGEuZGF0YS5pZnJhbWVJZCA9PT0gJzExJykgew0KICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdmcmFtZScpLmNvbnRlbnRXaW5kb3cucG9zdE1lc3NhZ2UoDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIHR5cGU6ICdyb3V0ZXInLA0KICAgICAgICAgICAgICBwYXRoOiAnL21vZGVsQ2FkJywNCiAgICAgICAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICAgICAgICAvLyBiYXNlVXJsOiBiYXNlVXJsKCksDQogICAgICAgICAgICAgICAgY2FkSWQ6IHRoaXMuZmlsZUJpbSwNCiAgICAgICAgICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuY2FkUm93UHJvamVjdElkLA0KICAgICAgICAgICAgICAgIHN0ZWVsTmFtZTogdGhpcy5jYWRSb3dDb2RlLA0KICAgICAgICAgICAgICAgIHNob3dDYWQ6IHRoaXMuSXNVcGxvYWRDYWQsDQogICAgICAgICAgICAgICAgaXNTdWJBc3NlbWJseTogZmFsc2UsDQogICAgICAgICAgICAgICAgaXNQYXJ0OiB0cnVlDQogICAgICAgICAgICAgICAgLy8gY2FkSWQ6IHRoaXMuZmlsZUJpbQ0KICAgICAgICAgICAgICAgIC8vIHRva2VuOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnVG9rZW4nKSwNCiAgICAgICAgICAgICAgICAvLyBhdXRoX2lkOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnTGFzdF9Xb3JraW5nX09iamVjdF9JZCcpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAnKicNCiAgICAgICAgICApDQogICAgICAgIH0gZWxzZSBpZiAoZGF0YS5kYXRhLmlmcmFtZUlkID09PSAnMTMnKSB7DQogICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2Z1bGxGcmFtZScpLmNvbnRlbnRXaW5kb3cucG9zdE1lc3NhZ2UoDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIHR5cGU6ICdyb3V0ZXInLA0KICAgICAgICAgICAgICBwYXRoOiAnL21vZGVsQ2FkJywNCiAgICAgICAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICAgICAgICAvLyBiYXNlVXJsOiBiYXNlVXJsKCksDQogICAgICAgICAgICAgICAgY2FkSWQ6IHRoaXMuZmlsZUJpbSwNCiAgICAgICAgICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuY2FkUm93UHJvamVjdElkLA0KICAgICAgICAgICAgICAgIHN0ZWVsTmFtZTogdGhpcy5jYWRSb3dDb2RlLA0KICAgICAgICAgICAgICAgIHNob3dDYWQ6IHRoaXMuSXNVcGxvYWRDYWQsDQogICAgICAgICAgICAgICAgaXNTdWJBc3NlbWJseTogZmFsc2UsDQogICAgICAgICAgICAgICAgaXNQYXJ0OiB0cnVlDQogICAgICAgICAgICAgICAgLy8gdG9rZW46IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdUb2tlbicpLA0KICAgICAgICAgICAgICAgIC8vIGF1dGhfaWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJykNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICcqJw0KICAgICAgICAgICkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["draft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6rBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "draft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-part", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 flex-row\">\r\n    <div class=\"cs-right\">\r\n      <el-card v-loading=\"pgLoading\" class=\"box-card h100\" element-loading-text=\"正在处理...\">\r\n        <h4 class=\"topTitle\"><span />基本信息</h4>\r\n        <el-form\r\n          ref=\"formInline\"\r\n          :inline=\"true\"\r\n          :model=\"formInline\"\r\n          class=\"demo-form-inline\"\r\n        >\r\n          <el-form-item v-if=\"!isAdd&&!isNest\" label=\"排产单号\" prop=\"Schduling_Code\">\r\n            <span v-if=\"isView\">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>\r\n            <el-input v-else v-model=\"formInline.Schduling_Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"计划员\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ formInline.Create_UserName }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Create_UserName\"\r\n              disabled\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"要求完成时间\"\r\n            prop=\"Finish_Date\"\r\n            :rules=\"{ required: true, message: '请选择', trigger: 'change' }\"\r\n          >\r\n            <span v-if=\"isView\">{{ formInline.Finish_Date | timeFormat }}</span>\r\n            <el-date-picker\r\n              v-else\r\n              v-model=\"formInline.Finish_Date\"\r\n              :picker-options=\"pickerOptions\"\r\n              :disabled=\"isView\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"date\"\r\n              placeholder=\"选择日期\"\r\n            />\r\n          </el-form-item>\r\n          <!--          <el-form-item v-if=\"!isNest\" label=\"批次\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ installName }}</span>\r\n            <el-select\r\n              v-else\r\n              v-model=\"formInline.InstallUnit_Id\"\r\n              :disabled=\"!isAdd\"\r\n              filterable\r\n              placeholder=\"请选择\"\r\n              @change=\"installChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in installUnitIdList\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>-->\r\n          <el-form-item label=\"备注\" prop=\"Remark\">\r\n            <span v-if=\"isView\">{{ formInline.Remark }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Remark\"\r\n              :disabled=\"isView\"\r\n              style=\"width: 320px\"\r\n              placeholder=\"请输入\"\r\n            />\r\n          </el-form-item>\r\n\r\n        </el-form>\r\n        <el-divider class=\"elDivder\" />\r\n        <div v-if=\"!isView\">\r\n          <div ref=\"searchDom\" class=\"search-container\">\r\n\r\n            <el-form ref=\"searchForm\" :model=\"innerForm\">\r\n              <el-row>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"项目名称\" prop=\"projectName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.projectName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in projectList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"区域\" prop=\"areaName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.areaName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in areaList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"批次\" prop=\"installName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.installName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in installList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"90px\" prop=\"searchContent\" :label=\"`${partName}名称` \">\r\n                    <el-input\r\n                      v-model=\"innerForm.searchContent\"\r\n                      clearable\r\n                      class=\"input-with-select w100\"\r\n                      placeholder=\"请输入(空格区分/多个搜索)\"\r\n                      size=\"small\"\r\n                    >\r\n                      <el-select\r\n                        slot=\"prepend\"\r\n                        v-model=\"curSearch\"\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100px\"\r\n                      >\r\n                        <el-option label=\"精准查询\" :value=\"1\" />\r\n                        <el-option label=\"模糊查询\" :value=\"0\" />\r\n                      </el-select>\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" :label=\"`${partName}类型`\" prop=\"searchComTypeSearch\">\r\n                    <el-tree-select\r\n                      v-if=\"$route.query.status!=='view'\"\r\n                      ref=\"treeSelectComponentType\"\r\n                      v-model=\"innerForm.searchComTypeSearch\"\r\n                      placeholder=\"请选择\"\r\n                      :select-params=\"treeSelectParams\"\r\n                      class=\"cs-tree-x\"\r\n                      :tree-params=\"treeParamsComponentType\"\r\n                      @searchFun=\"componentTypeFilter\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"规格\" prop=\"searchSpecSearch\">\r\n                    <el-input v-model=\"innerForm.searchSpecSearch\" class=\"w100\" placeholder=\"请输入\" clearable=\"\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item v-if=\"isCom\" label-width=\"80px\" label=\"是否直发件\" prop=\"searchDirect\">\r\n                    <el-select v-model=\"innerForm.searchDirect\" class=\"w100\" placeholder=\"请选择\" clearable>\r\n                      <el-option label=\"是\" :value=\"true\" />\r\n                      <el-option label=\"否\" :value=\"false\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"20px\">\r\n                    <el-button type=\"primary\" @click=\"innerFilter\">搜索</el-button>\r\n                    <el-button @click=\"resetInnerForm\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        <vxe-toolbar\r\n          ref=\"xToolbar1\"\r\n        >\r\n          <template #buttons>\r\n            <div v-if=\"!isView\" class=\"btn-x\">\r\n              <el-button v-if=\"!isNest\" type=\"primary\" @click=\"handleAddDialog()\">添加</el-button>\r\n\r\n              <el-button\r\n                v-if=\"workshopEnabled\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchWorkshop(1)\"\r\n              >分配车间\r\n              </el-button>\r\n\r\n              <el-dropdown v-if=\"hasCraft\" style=\"margin:0 10px\" @command=\"handleSelectMenu\">\r\n                <el-button :disabled=\"!multipleSelection.length\" type=\"primary\" plain>\r\n                  分配工序<i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    command=\"process\"\r\n                  >批量分配工序\r\n                  </el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    command=\"craft\"\r\n                  >工艺代码分配\r\n                  </el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <el-button\r\n                v-else\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleSelectMenu('process')\"\r\n              >分配工序\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"!isCom && !isOwnerNull\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchOwner(1)\"\r\n              >批量分配领用工序\r\n              </el-button>\r\n              <el-button\r\n                plain\r\n                :disabled=\"!tbData.length\"\r\n                :loading=\"false\"\r\n                @click=\"handleReverse\"\r\n              >反选\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                plain\r\n                :loading=\"deleteLoading\"\r\n                :disabled=\"!multipleSelection.length || multipleSelection.some(item=>item.stopFlag)\"\r\n                @click=\"handleDelete\"\r\n              >删除\r\n              </el-button>\r\n            </div>\r\n            <div v-else>\r\n              <el-button style=\"margin-bottom: 8px;\" :disabled=\"!tbData.length\" @click=\"handleExport\">导出</el-button>\r\n            </div>\r\n          </template>\r\n          <template #tools>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </template>\r\n        </vxe-toolbar>\r\n        <div class=\"tb-x\">\r\n          <!--          activeMethod: activeCellMethod,-->\r\n          <vxe-table\r\n            ref=\"xTable\"\r\n            :key=\"tbKey\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :checkbox-config=\"{checkField: 'checked'}\"\r\n            class=\"cs-vxe-table\"\r\n            :row-config=\"{isCurrent: true, isHover: true}\"\r\n            align=\"left\"\r\n            height=\"100%\"\r\n            :filter-config=\"{showIcon:false}\"\r\n            show-overflow\r\n            :loading=\"tbLoading\"\r\n            stripe\r\n            :scroll-y=\"{enabled: true, gt: 20}\"\r\n            size=\"medium\"\r\n            :edit-config=\"{\r\n              trigger: 'click',\r\n              mode: 'cell',\r\n              showIcon: !isView,\r\n\r\n            }\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"isComponentOptions\"\r\n                :filter-method=\"filterComponentMethod\"\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag\r\n                    :type=\"row.Is_Component ? 'danger' : 'success'\"\r\n                  >{{ row.Is_Component ? '否' : '是' }}\r\n                  </el-tag>\r\n                </template>\r\n              </vxe-column>\r\n\r\n              <vxe-column\r\n                v-else-if=\"['Type','Type_Name'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filter-method=\"filterTypeMethod\"\r\n                :field=\"item.Code\"\r\n                :filters=\"filterTypeOption\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row[item.Code] | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Comp_Code','Part_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :filter-method=\"filterCodeMethod\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"filterCodeOption\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin: 8px;\" type=\"danger\">变</el-tag>\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <el-link v-if=\"row.DwgCount>0\" type=\"primary\" @click.stop=\"handleDwg(row)\"> {{ row[item.Code] | displayValue }}\r\n                  </el-link>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Spec'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"specOptions\"\r\n                :filter-method=\"filterSpecMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Spec | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Project_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"projectOptions\"\r\n                :filter-method=\"filterProjectMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in projectList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Project_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Area_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"areaOptions\"\r\n                :filter-method=\"filterAreaMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in areaList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Area_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'InstallUnit_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"installOptions\"\r\n                :filter-method=\"filterInstallMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in installList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.InstallUnit_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Weight'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  {{ (row.Schduled_Count * row.Weight).toFixed(2) / 1 }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Technology_Path'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :show-overflow=\"false\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Technology_Path\" placement=\"top\">\r\n                        <span>{{ row.Technology_Path | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"openBPADialog(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Part_Used_Process'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :show-overflow=\"false\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Part_Used_Process\" placement=\"top\">\r\n                        <span>{{ row.Part_Used_Process | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"showPartUsedProcess(row)\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchOwner(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Workshop_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :show-overflow=\"false\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Workshop_Name\" placement=\"top\">\r\n                        <span>{{ row.Workshop_Name | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchWorkshop(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Count'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :edit-render=\"{enabled:!isView}\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #edit=\"{ row }\">\r\n                  <vxe-input\r\n                    v-model.number=\"row.Schduled_Count\"\r\n                    type=\"integer\"\r\n                    min=\"0\"\r\n                    :max=\"row.chooseCount\"\r\n                  />\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Schduled_Count | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Id\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n\r\n          </vxe-table>\r\n        </div>\r\n        <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n        <footer v-if=\"!isView\">\r\n          <div class=\"data-info\">\r\n            <el-tag\r\n              size=\"medium\"\r\n              class=\"info-x\"\r\n            >已选 {{ multipleSelection.length }} 条数据\r\n            </el-tag>\r\n            <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{\r\n              tipLabel\r\n            }}\r\n            </el-tag>\r\n          </div>\r\n          <div>\r\n            <el-button v-if=\"workshopEnabled&&!isNest\" type=\"primary\" :disabled=\"tbData.some(item=>item.stopFlag)\" @click=\"saveWorkShop\">保存车间分配</el-button>\r\n            <el-button\r\n              v-if=\"!isNest\"\r\n              type=\"primary\"\r\n              :disabled=\"tbData.some(item=>item.stopFlag)\"\r\n              :loading=\"saveLoading\"\r\n              @click=\"saveDraft(false)\"\r\n            >保存草稿\r\n            </el-button>\r\n            <el-button :disabled=\"deleteLoading || tbData.some(item=>item.stopFlag)\" @click=\"handleSubmit\">下发任务</el-button>\r\n          </div>\r\n        </footer>\r\n      </el-card>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :bom-list=\"bomList\"\r\n        :is-nest=\"isNest\"\r\n        :part-name=\"partName\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :process-list=\"processList\"\r\n        :page-type=\"pageType\"\r\n        :has-unit-part=\"hasUnitPart\"\r\n        :part-type-option=\"typeOption\"\r\n        @close=\"handleClose\"\r\n        @sendProcess=\"sendProcess\"\r\n        @workShop=\"getWorkShop\"\r\n        @refresh=\"fetchData\"\r\n        @setProcessList=\"setProcessList\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      :key=\"addDraftKey\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"openAddDraft\"\r\n      :width=\"dWidth\"\r\n      top=\"7vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <add-draft\r\n        v-if=\"openAddDraft\"\r\n        ref=\"draft\"\r\n        :com-name=\"comName\"\r\n        :part-name=\"partName\"\r\n        :current-ids=\"currentIds\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :install-id=\"formInline.InstallUnit_Id\"\r\n        :schedule-id=\"scheduleId\"\r\n        :show-dialog=\"openAddDraft\"\r\n        :page-type=\"pageType\"\r\n        @addToTbList=\"addToTbList\"\r\n        @sendSelectList=\"mergeSelectList\"\r\n        @setAddTbKey=\"setAddTbKey\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-drawer\r\n      :visible.sync=\"drawer\"\r\n      direction=\"btt\"\r\n      size=\"60%\"\r\n      destroy-on-close\r\n      :before-close=\"handleCloseDrawer\"\r\n      @opened=\"renderIframe\"\r\n    >\r\n      <div style=\"width: 100%; display: flex\">\r\n        <div style=\"margin-left: 20px\">\r\n          <span style=\"display: inline-block; width: 100px\">{{ partName }}图纸</span>\r\n        </div>\r\n        <el-button\r\n          v-if=\"fileBim\"\r\n          style=\"margin-left: 42%\"\r\n          @click=\"fullscreen(1)\"\r\n        >全屏</el-button>\r\n      </div>\r\n      <iframe\r\n        id=\"frame\"\r\n        :key=\"iframeKey\"\r\n        :src=\"iframeUrl\"\r\n        style=\"width: 100%; border: 0px; margin: 0; height: 60vh\"\r\n      />\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { closeTagView, debounce } from '@/utils'\r\nimport BatchProcessAdjust from './components/BatchProcessAdjust'\r\nimport {\r\n  GetCanSchdulingPartList,\r\n  GetCompSchdulingInfoDetail, GetDwg,\r\n  GetPartSchdulingInfoDetail,\r\n  GetSchdulingWorkingTeams,\r\n  SaveComponentSchedulingWorkshop,\r\n  SaveCompSchdulingDraft,\r\n  SavePartSchdulingDraftNew,\r\n  SavePartSchedulingWorkshopNew,\r\n  SaveSchdulingTaskById\r\n} from '@/api/PRO/production-task'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport AddDraft from './components/addDraft'\r\nimport OwnerProcess from './components/OwnerProcess'\r\nimport Workshop from './components/Workshop.vue'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { getUnique, uniqueCode } from './constant'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { GetLibListType, GetProcessFlowListWithTechnology, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { AreaGetEntity } from '@/api/plm/projects'\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport moment from 'moment'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\n\r\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\n\r\nimport { getConfigure } from '@/api/user'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: { DynamicTableFields, TreeDetail, ExpandableSection, BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },\r\n  data() {\r\n    return {\r\n      drawer: false,\r\n      drawersull: false,\r\n      iframeKey: '',\r\n      fullscreenid: '',\r\n      iframeUrl: '',\r\n      fullbimid: '',\r\n      fileBim: '',\r\n      IsUploadCad: false,\r\n      cadRowCode: '',\r\n      cadRowProjectId: '',\r\n      tbKey: 100,\r\n      isComponentOptions: [\r\n        { label: '是', value: false },\r\n        { label: '否', value: true }\r\n      ],\r\n      specOptions: [{ data: '' }],\r\n      filterTypeOption: [{ data: '' }],\r\n      filterCodeOption: [{ data: '' }],\r\n      projectOptions: [{ data: '' }],\r\n      areaOptions: [{ data: '' }],\r\n      installOptions: [{ data: '' }],\r\n      projectList: [],\r\n      installList: [],\r\n      areaList: [],\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n        }\r\n      },\r\n      innerForm: {\r\n        projectName: '',\r\n        areaName: '',\r\n        installName: '',\r\n        searchContent: '',\r\n        searchComTypeSearch: '',\r\n        searchSpecSearch: '',\r\n        searchDirect: ''\r\n      },\r\n      curSearch: 1,\r\n      searchType: '',\r\n      formInline: {\r\n        Schduling_Code: '',\r\n        Create_UserName: '',\r\n        Finish_Date: '',\r\n        InstallUnit_Id: '',\r\n        Remark: ''\r\n      },\r\n      total: 0,\r\n      currentIds: '',\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      multipleSelection: [],\r\n      showExpand: true,\r\n      pgLoading: false,\r\n      deleteLoading: false,\r\n      workShopIsOpen: false,\r\n      isOwnerNull: false,\r\n      dialogVisible: false,\r\n      openAddDraft: false,\r\n      saveLoading: false,\r\n      tbLoading: false,\r\n      isCheckAll: false,\r\n      currentComponent: '',\r\n      gridCode: '',\r\n      dWidth: '25%',\r\n      title: '',\r\n      search: () => ({}),\r\n      pageType: undefined,\r\n      tipLabel: '',\r\n      technologyOption: [],\r\n      typeOption: [],\r\n      workingTeam: [],\r\n      pageStatus: undefined,\r\n      scheduleId: '',\r\n      partComOwnerColumn: null,\r\n\r\n      installUnitIdList: [],\r\n      projectId: '',\r\n      areaId: '',\r\n      projectName: '',\r\n      statusType: '',\r\n      expandedKey: '',\r\n      // treeLoading: false,\r\n      treeData: [],\r\n      treeParamsComponentType: {\r\n        'default-expand-all': true,\r\n        'check-strictly': true,\r\n        filterable: true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        collapseTags: true,\r\n        clearable: true\r\n      },\r\n      disabledAdd: true,\r\n      projectOption: [],\r\n      comName: '',\r\n      partName: '',\r\n      bomList: []\r\n    }\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler(n, o) {\r\n        this.checkOwner()\r\n        this.doFilter()\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    isView() {\r\n      return this.pageStatus === 'view'\r\n    },\r\n    isEdit() {\r\n      return this.pageStatus === 'edit'\r\n    },\r\n    isAdd() {\r\n      return this.pageStatus === 'add'\r\n    },\r\n    addDraftKey() {\r\n      return this.expandedKey + this.formInline.InstallUnit_Id\r\n    },\r\n    // filterText() {\r\n    //   return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    // },\r\n    statusCode() {\r\n      return this.isCom ? 'Comp_Schdule_Status' : 'Part_Schdule_Status'\r\n    },\r\n    installName() {\r\n      const item = this.installUnitIdList.find(v => v.Id === this.formInline.InstallUnit_Id)\r\n      if (item) {\r\n        return item.Name\r\n      } else {\r\n        return ''\r\n      }\r\n    },\r\n    isPartPrepare() {\r\n      return this.getIsPartPrepare && !this.isCom\r\n    },\r\n    isNest() {\r\n      return this.$route.query.type === '1'\r\n    },\r\n    hasCraft() {\r\n      return !!this.isVersionFour\r\n    },\r\n    hasUnitPart() {\r\n      return !!this.isVersionFour\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    ...mapGetters('factoryInfo', ['workshopEnabled', 'getIsPartPrepare']),\r\n    ...mapGetters('schedule', ['processList', 'nestIds'])\r\n  },\r\n  async created() {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      // this.baseCadUrl = 'http://localhost:9529'\r\n      // this.baseCadUrl = 'http://glendale-model.bimtk.com'\r\n      this.baseCadUrl = 'http://glendale-model-dev.bimtk.tech'\r\n    } else {\r\n      getConfigure({ code: 'glendale_url' }).then((res) => {\r\n        this.baseCadUrl = res.Data\r\n      })\r\n    }\r\n  },\r\n  async mounted() {\r\n    const { list, partName, comName } = await GetBOMInfo(0)\r\n    this.bomList = list || []\r\n    this.partName = partName\r\n    this.comName = comName\r\n    this.initProcessList()\r\n    this.tbDataMap = {}\r\n    this.craftCodeMap = {}\r\n    this.pageType = this.$route.query.pg_type\r\n    this.pageStatus = this.$route.query.status\r\n    this.model = this.$route.query.model\r\n    this.scheduleId = this.$route.query.pid || ''\r\n    // // this.formInline.Create_UserName = this.$store.getters.name\r\n    // // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage\r\n    this.formInline.Create_UserName = localStorage.getItem('UserAccount')\r\n    // if (!this.isCom) {\r\n    //   this.getPartType()\r\n    // } else {\r\n    // }\r\n\r\n    this.unique = uniqueCode()\r\n    this.checkWorkshopIsOpen()\r\n\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.mergeConfig()\r\n    if (this.isView || this.isEdit) {\r\n      const { areaId, install } = this.$route.query\r\n      // this.areaId = areaId\r\n      // this.formInline.InstallUnit_Id = install\r\n      // this.getInstallUnitIdNameList()\r\n      this.fetchData()\r\n    }\r\n\r\n    if (this.isAdd) {\r\n      // this.fetchTreeData()\r\n      this.getType()\r\n    }\r\n    if (this.isEdit) {\r\n      this.getType()\r\n    }\r\n\r\n    window.addEventListener('message', this.frameListener)\r\n    this.$once('hook:beforeDestroy', () => {\r\n      console.log('deactivated')\r\n      window.removeEventListener('message', this.frameListener)\r\n    })\r\n  },\r\n  activated() {\r\n    window.addEventListener('message', this.frameListener)\r\n    this.$once('hook:deactivated', () => {\r\n      window.removeEventListener('message', this.frameListener)\r\n    })\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['changeProcessList', 'initProcessList', 'changeAddTbKeys']),\r\n    checkOwner() {\r\n      if (this.isCom) return\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id) && !this.isNest\r\n      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')\r\n      if (this.isOwnerNull) {\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      } else {\r\n        if (idx === -1) {\r\n          if (!this.ownerColumn) {\r\n            this.$message({\r\n              message: `列表配置字段缺少${this.partName}领用工序字段`,\r\n              type: 'success'\r\n            })\r\n            return\r\n          }\r\n          this.columns.push(this.ownerColumn)\r\n        }\r\n        this.comPart = true\r\n      }\r\n    },\r\n    async mergeConfig() {\r\n      await this.getConfig()\r\n      await this.getWorkTeam()\r\n    },\r\n    doFilter() {\r\n      this.projectList = []\r\n      this.installList = []\r\n      this.areaList = []\r\n      this.tbData.forEach(cur => {\r\n        if (cur.Project_Name && !this.projectList.includes(cur.Project_Name)) {\r\n          this.projectList.push(cur.Project_Name)\r\n        }\r\n        if (cur.InstallUnit_Name && !this.installList.includes(cur.InstallUnit_Name)) {\r\n          this.installList.push(cur.InstallUnit_Name)\r\n        }\r\n        if (cur.Area_Name && !this.areaList.includes(cur.Area_Name)) {\r\n          this.areaList.push(cur.Area_Name)\r\n        }\r\n      })\r\n    },\r\n    async getConfig() {\r\n      let configCode = ''\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          configCode = 'PRONestingScheduleDetail'\r\n        } else {\r\n          configCode = 'PRONestingScheduleConfig'\r\n        }\r\n      } else {\r\n        configCode = this.isCom\r\n          ? (this.isView ? 'PROComViewPageTbConfig' : 'PROComDraftPageTbConfig')\r\n          : (this.isView ? 'PROPartViewPageTbConfig_new' : 'PROPartDraftPageTbConfig_new')\r\n      }\r\n      this.gridCode = configCode\r\n      await this.getTableConfig(configCode)\r\n      if (!this.workshopEnabled) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')\r\n      }\r\n      if (!this.hasCraft) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Technology_Code')\r\n      }\r\n      this.checkOwner()\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n      this.tbKey++\r\n    },\r\n    /*    handleNodeClick(data) {\r\n      console.log('data', data)\r\n      if (this.areaId === data.Id) {\r\n        return\r\n      }\r\n      this.\r\n       = true\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      const initData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n        this.formInline.Finish_Date = ''\r\n        this.formInline.InstallUnit_Id = ''\r\n        this.formInline.Remark = ''\r\n        this.tbData = []\r\n        this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      if (this.tbData.length) {\r\n        this.$confirm('切换区域右侧数据清空，是否确认?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          initData(data)\r\n          this.disabledAdd = false\r\n          this.tbDataMap = {}\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      } else {\r\n        this.disabledAdd = false\r\n        initData(data)\r\n      }\r\n    },*/\r\n\r\n    /* customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },*/\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      let resData = []\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          resData = await this.getPartPageList()\r\n        } else {\r\n          resData = await this.getNestPageList()\r\n        }\r\n      } else {\r\n        resData = await this.getPartPageList()\r\n      }\r\n\r\n      this.initTbData(resData)\r\n      this.tbLoading = false\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    fetchTreeStatus() {\r\n      // this.filterText = this.statusType\r\n    },\r\n    /*    fetchTreeData() {\r\n      this.treeLoading = true\r\n      GetProjectAreaTreeList({ projectName: this.projectName, type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data.map(item => {\r\n          item.Is_Directory = true\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        this.setKey()\r\n        this.treeLoading = false\r\n      })\r\n    },*/\r\n    // setKey() {\r\n    //   const deepFilter = (tree) => {\r\n    //     for (let i = 0; i < tree.length; i++) {\r\n    //       const item = tree[i]\r\n    //       const { Data, Children } = item\r\n    //       console.log(Data)\r\n    //       if (Data.ParentId && !Children?.length) {\r\n    //         this.handleNodeClick(item)\r\n    //         return\r\n    //       } else {\r\n    //         if (Children && Children.length > 0) {\r\n    //           return deepFilter(Children)\r\n    //         }\r\n    //       }\r\n    //     }\r\n    //   }\r\n    //   return deepFilter(this.treeData)\r\n    // },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    checkWorkshopIsOpen() {\r\n      this.workShopIsOpen = true\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    getAreaInfo() {\r\n      this.formInline.Finish_Date = ''\r\n      AreaGetEntity({\r\n        id: this.areaId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            return []\r\n          }\r\n\r\n          const start = moment(res.Data?.Demand_Begin_Date)\r\n          const end = moment(res.Data?.Demand_End_Date)\r\n          this.pickerOptions.disabledDate = (time) => {\r\n            return time.getTime() < start || time.getTime() > end\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.openAddDraft = false\r\n    },\r\n    getNestPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        GetCanSchdulingPartList({\r\n          Ids: this.nestIds\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const _list = res?.Data || []\r\n            const list = _list.map(v => {\r\n              v.Part_Used_Process = v.Scheduled_Used_Process || v.Part_Type_Used_Process\r\n              // v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n              v.Workshop_Id = v.Scheduled_Workshop_Id\r\n              v.Workshop_Name = v.Scheduled_Workshop_Name\r\n              v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n              v.chooseCount = v.Can_Schduling_Count\r\n\r\n              return v\r\n            })\r\n\r\n            resolve(list)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getComPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        const {\r\n          pid\r\n        } = this.$route.query\r\n        GetCompSchdulingInfoDetail({\r\n          Schduling_Plan_Id: pid\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const { Schduling_Plan, Schduling_Comps, Process_List } = res.Data\r\n            this.formInline = Object.assign(this.formInline, Schduling_Plan)\r\n            Process_List.forEach(item => {\r\n              const plist = {\r\n                key: item.Process_Code,\r\n                value: item\r\n              }\r\n              this.changeProcessList(plist)\r\n            })\r\n            const list = Schduling_Comps.map(v => {\r\n              v.chooseCount = v.Can_Schduling_Count\r\n              return v\r\n            })\r\n            resolve(list || [])\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async getPartPageList() {\r\n      const {\r\n        pid\r\n      } = this.$route.query\r\n      const result = await GetPartSchdulingInfoDetail({\r\n        Schduling_Plan_Id: pid\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const SarePartsModel = res.Data?.SarePartsModel.map(v => {\r\n            if (v.Scheduled_Used_Process) {\r\n              // 已存在操作过数据\r\n              v.Part_Used_Process = v.Scheduled_Used_Process\r\n            }\r\n            v.chooseCount = v.Can_Schduling_Count\r\n            return v\r\n          })\r\n          this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)\r\n          this.getStopList(SarePartsModel)\r\n          return SarePartsModel || []\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      return result || []\r\n    },\r\n    async getStopList(list) {\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 1\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      console.log(5, JSON.parse(JSON.stringify(list)))\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        row.uuid = uuidv4()\r\n        this.addElementToTbData(row)\r\n        if (row[teamKey]) {\r\n          const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n          newData.forEach((ele, index) => {\r\n            const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            row[code] = ele.Count\r\n            row[max] = 0\r\n          })\r\n        }\r\n        this.setInputMax(row)\r\n        return row\r\n      })\r\n      let ids = ''\r\n      if (this.isCom) {\r\n        ids = this.tbData.map(v => v.Comp_Import_Detail_Id).toString()\r\n      } else {\r\n        ids = this.tbData.map(v => v.Part_Aggregate_Id).toString()\r\n      }\r\n      this.currentIds = ids\r\n    },\r\n    async mergeCraftProcess(list) {\r\n      let codes = [...new Set(list.map(v => v.Technology_Code))]\r\n      for (const key in this.craftCodeMap) {\r\n        if (this.craftCodeMap.hasOwnProperty(key)) {\r\n          codes = codes.filter(code => code !== key)\r\n        }\r\n      }\r\n      const _craftCodeMap = await this.getCraftProcess(codes)\r\n      Object.assign(this.craftCodeMap, _craftCodeMap)\r\n    },\r\n    getCraftProcess(gyGroup = []) {\r\n      gyGroup = gyGroup.filter(v => !!v)\r\n      if (!gyGroup.length) return Promise.resolve({})\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessFlowListWithTechnology({\r\n          TechnologyCodes: gyGroup,\r\n          Type: 2\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const gyList = res.Data || []\r\n            const gyMap = gyList.reduce((acc, item) => {\r\n              acc[item.Code] = item.Technology_Path\r\n              return acc\r\n            }, {})\r\n            console.log('gyMap', gyMap)\r\n            resolve(gyMap)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async mergeSelectList(newList) {\r\n      if (this.hasCraft) {\r\n        await this.mergeCraftProcess(newList)\r\n      }\r\n      console.time('mergeSelectListTime')\r\n      let hasUsedPartFlag = true\r\n\r\n      newList.forEach((element, index) => {\r\n        const cur = this.getMergeUniqueRow(element)\r\n\r\n        if (!cur) {\r\n          console.log('element', element)\r\n          element.puuid = element.uuid\r\n          element.Schduled_Count = element.chooseCount\r\n          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')\r\n\r\n          if (this.hasCraft && !element.Technology_Path) {\r\n            if (this.craftCodeMap[element.Technology_Code] && this.craftCodeMap[element.Technology_Code] instanceof Array) {\r\n              const curPathArr = this.craftCodeMap[element.Technology_Code]\r\n              if (element.Part_Used_Process && !curPathArr.includes(element.Part_Used_Process)) {\r\n                hasUsedPartFlag = false\r\n              } else {\r\n                element.Technology_Path = curPathArr.join('/')\r\n              }\r\n            }\r\n          }\r\n\r\n          this.tbData.push(element)\r\n          this.addElementToTbData(element)\r\n          return\r\n        }\r\n\r\n        cur.puuid = element.uuid\r\n\r\n        cur.Schduled_Count += element.chooseCount\r\n        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')\r\n        if (!cur.Technology_Path) {\r\n          return\r\n        }\r\n        this.setInputMax(cur)\r\n      })\r\n\r\n      // if (this.isCom) {\r\n      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      // } else {\r\n      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))\r\n      // }\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.timeEnd('fff')\r\n      console.log('this.tbDataMap', this.tbDataMap, this.tbData)\r\n    },\r\n    addElementToTbData(element) {\r\n      const key = this.getUniKey(element)\r\n      this.tbDataMap[key] = element\r\n    },\r\n    getMergeUniqueRow(element) {\r\n      const key = this.getUniKey(element)\r\n      return this.tbDataMap[key]\r\n    },\r\n    getUniKey(element) {\r\n      return getUnique(this.isCom, element)\r\n    },\r\n    checkForm() {\r\n      let isValidate = true\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) isValidate = false\r\n      })\r\n      return isValidate\r\n    },\r\n    async saveDraft(isOrder = false) {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return false\r\n      if (!isOrder) {\r\n        this.saveLoading = true\r\n      }\r\n\r\n      const isSuccess = await this.handleSaveDraft(tableData, isOrder)\r\n      console.log('isSuccess', isSuccess)\r\n      if (!isSuccess) return false\r\n      if (isOrder) return isSuccess\r\n      this.$refs['draft']?.fetchData()\r\n      this.saveLoading = false\r\n    },\r\n    async saveWorkShop() {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const obj = {}\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'success'\r\n        })\r\n        return\r\n      }\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = this.tbData\r\n      } else {\r\n        obj.SarePartsModel = this.tbData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      this.pgLoading = true\r\n      const _fun = this.isCom ? SaveComponentSchedulingWorkshop : SavePartSchedulingWorkshopNew\r\n      _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.pgLoading = false\r\n        }\r\n      })\r\n    },\r\n    getSubmitTbInfo() {\r\n      // 处理上传的数据\r\n      let tableData = JSON.parse(JSON.stringify(this.tbData))\r\n      tableData = tableData.filter(item => item.Schduled_Count > 0)\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        let list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (this.isPartPrepare && !element.Part_Used_Process && element.Type !== 'Direct' && this.comPart) {\r\n          const msg = '领用工序不能为空'\r\n          if (this.isNest) {\r\n            if (element.Comp_Import_Detail_Id) {\r\n              this.$message({\r\n                message: msg,\r\n                type: 'warning'\r\n              })\r\n              return { status: false }\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: msg,\r\n              type: 'warning'\r\n            })\r\n            return { status: false }\r\n          }\r\n        }\r\n        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {\r\n        //   // 零构件 零件单独排产\r\n        //   this.$message({\r\n        //     message: '零件领用工序不能为空',\r\n        //     type: 'warning'\r\n        //   })\r\n        //   return { status: false }\r\n        // }\r\n        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.partName}保持工序一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.partName}领用工序保持一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        // processList.forEach(code => {\r\n        // const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n        // const groupsList = groups.map(group => {\r\n        //   const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n        //   const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n        //   const obj = {\r\n        //     Team_Task_Id: element.Team_Task_Id,\r\n        //     Comp_Code: element.Comp_Code,\r\n        //     Again_Count: +element[uCode] || 0, // 不填，后台让传0\r\n        //     Part_Code: this.isCom ? null : '',\r\n        //     Process_Code: code,\r\n        //     Technology_Path: element.Technology_Path,\r\n        //     Working_Team_Id: group.Working_Team_Id,\r\n        //     Working_Team_Name: group.Working_Team_Name\r\n        //   }\r\n        //   delete element[uCode]\r\n        //   delete element[uMax]\r\n        //   return obj\r\n        // })\r\n        // list.push(...groupsList)\r\n        // })\r\n        for (let j = 0; j < processList.length; j++) {\r\n          const code = processList[j]\r\n          const schduledCount = element.Schduled_Count || 0\r\n          let groups = []\r\n          if (element.Allocation_Teams) {\r\n            groups = element.Allocation_Teams.filter(v => v.Process_Code === code)\r\n          }\r\n          const againCount = groups.reduce((acc, cur) => {\r\n            return acc + (cur.Again_Count || 0)\r\n          }, 0)\r\n          if (againCount > schduledCount) {\r\n            list = []\r\n            break\r\n          } else {\r\n            list.push(...groups)\r\n          }\r\n        }\r\n        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))\r\n        hasInput.forEach((item) => {\r\n          delete element[item]\r\n        })\r\n        delete element['uuid']\r\n        delete element['_X_ROW_KEY']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    async handleSaveDraft(tableData, isOrder) {\r\n      console.log('保存草稿')\r\n      const _fun = this.isCom ? SaveCompSchdulingDraft : SavePartSchdulingDraftNew\r\n      const obj = {}\r\n      // if (this.isCom) {\r\n      // obj.Schduling_Comps = tableData\r\n      const p = []\r\n      for (const objKey in this.processList) {\r\n        if (this.processList.hasOwnProperty(objKey)) {\r\n          p.push(this.processList[objKey])\r\n        }\r\n      }\r\n      obj.Process_List = p\r\n      // } else {\r\n      obj.SarePartsModel = tableData\r\n      // }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      let orderSuccess = false\r\n      console.log('obj', obj)\r\n\r\n      await _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!isOrder) {\r\n            this.pgLoading = false\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.templateScheduleCode = res.Data\r\n            orderSuccess = true\r\n            console.log('保存草稿成功 ')\r\n          }\r\n        } else {\r\n          this.saveLoading = false\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log('结束 ')\r\n      return orderSuccess\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))\r\n        this.tbData = this.tbData.filter(item => {\r\n          const isSelected = selectedUuids.has(item.uuid)\r\n          if (isSelected) {\r\n            const key = this.getUniKey(item)\r\n            delete this.tbDataMap[key]\r\n          }\r\n          return !isSelected\r\n        })\r\n        // this.$nextTick(_ => {\r\n        //   const _list = this.multipleSelection.filter(v => v.puuid)\r\n        //   this.$refs['draft']?.mergeData(_list)\r\n        //   this.multipleSelection = []\r\n        // })\r\n        this.deleteLoading = false\r\n      }, 0)\r\n    },\r\n    async getWorkTeam() {\r\n      await GetSchdulingWorkingTeams({\r\n        type: this.isCom ? 1 : 2\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.workingTeam = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) return\r\n        const { tableData, status } = this.getSubmitTbInfo()\r\n        if (!status) return\r\n        this.$confirm('是否提交当前数据?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.saveDraftDoSubmit(tableData)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      })\r\n    },\r\n    async saveDraftDoSubmit() {\r\n      this.pgLoading = true\r\n      if (this.formInline?.Schduling_Code) {\r\n        const isSuccess = await this.saveDraft(true)\r\n        console.log('saveDraftDoSubmit', isSuccess)\r\n        isSuccess && this.doSubmit(this.formInline.Id)\r\n      } else {\r\n        const isSuccess = await this.saveDraft(true)\r\n        isSuccess && this.doSubmit(this.templateScheduleCode)\r\n      }\r\n    },\r\n    doSubmit(scheduleCode) {\r\n      SaveSchdulingTaskById({\r\n        schdulingPlanId: scheduleCode\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '下达成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.pgLoading = false\r\n      }).catch(_ => {\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    getWorkShop(value) {\r\n      console.log('value', value)\r\n      const {\r\n        origin,\r\n        row,\r\n        workShop: {\r\n          Id,\r\n          Display_Name\r\n        }\r\n      } = value\r\n      if (origin === 2) {\r\n        if (value.workShop?.Id) {\r\n          row.Workshop_Name = Display_Name\r\n          row.Workshop_Id = Id\r\n          this.setPath(row, Id)\r\n        } else {\r\n          row.Workshop_Name = ''\r\n          row.Workshop_Id = ''\r\n        }\r\n      } else {\r\n        this.multipleSelection.forEach(item => {\r\n          if (value.workShop?.Id) {\r\n            item.Workshop_Name = Display_Name\r\n            item.Workshop_Id = Id\r\n            this.setPath(item, Id)\r\n          } else {\r\n            item.Workshop_Name = ''\r\n            item.Workshop_Id = ''\r\n          }\r\n        })\r\n      }\r\n    },\r\n    setPath(row, Id) {\r\n      if (row?.Scheduled_Workshop_Id) {\r\n        if (row.Scheduled_Workshop_Id !== Id) {\r\n          row.Technology_Path = ''\r\n        }\r\n      } else {\r\n        row.Technology_Path = ''\r\n      }\r\n    },\r\n    handleBatchWorkshop(origin, row) {\r\n      this.title = origin === 1 ? '批量分配车间' : '分配车间'\r\n      this.currentComponent = 'Workshop'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].fetchData(origin, row)\r\n      })\r\n    },\r\n\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const process = res.Data.map(v => v.Code)\r\n            resolve(process)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    setLibType(code, workshopId) {\r\n      return new Promise((resolve) => {\r\n        const obj = {\r\n          Component_type: code,\r\n          type: 1\r\n        }\r\n        if (this.workshopEnabled) {\r\n          obj.workshopId = workshopId\r\n        }\r\n        GetLibListType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            if (res.Data.Data && res.Data.Data.length) {\r\n              const info = res.Data.Data[0]\r\n              const workCode = info.WorkCode && info.WorkCode.replace(/\\\\/g, '/')\r\n              resolve(workCode)\r\n            } else {\r\n              resolve(undefined)\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n      })\r\n    },\r\n    sendProcess({ arr, str }) {\r\n      let isSuccess = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (item.originalPath && item.originalPath !== str) {\r\n          isSuccess = false\r\n          break\r\n        }\r\n        item.Technology_Path = str\r\n      }\r\n      if (!isSuccess) {\r\n        this.$message({\r\n          message: '请和该区域批次下已排产同构件保持工序一致',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    resetWorkTeamMax(row, str) {\r\n      if (str) {\r\n        row.Technology_Path = str\r\n      } else {\r\n        str = row.Technology_Path\r\n      }\r\n      const list = str?.split('/') || []\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const cur = list.some(k => k === element.Process_Code)\r\n        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        if (cur) {\r\n          if (!row[code]) {\r\n            this.$set(row, code, 0)\r\n            this.$set(row, max, row.Schduled_Count)\r\n          }\r\n        } else {\r\n          this.$delete(row, code)\r\n          this.$delete(row, max)\r\n        }\r\n      })\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')\r\n          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')\r\n          this.columns = this.setColumnDisplay(list)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setColumnDisplay(list) {\r\n      return list.filter(v => v.Is_Display)\r\n      // .map(item => {\r\n      //   if (FIX_COLUMN.includes(item.Code)) {\r\n      //     item.fixed = 'left'\r\n      //   }\r\n      //   return item\r\n      // })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    openBPADialog(type, row) {\r\n      if (this.workshopEnabled) {\r\n        if (type === 1) {\r\n          const IsUnique = this.checkIsUniqueWorkshop()\r\n          if (!IsUnique) return\r\n        }\r\n      }\r\n      this.title = type === 2 ? '工序调整' : '批量工序调整'\r\n      this.currentComponent = 'BatchProcessAdjust'\r\n      this.dWidth = '50%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')\r\n      })\r\n    },\r\n    checkIsUniqueWorkshop() {\r\n      let isUnique = true\r\n      const firstV = this.multipleSelection[0].Workshop_Name\r\n      for (let i = 1; i < this.multipleSelection.length; i++) {\r\n        const item = this.multipleSelection[i]\r\n        if (item.Workshop_Name !== firstV) {\r\n          isUnique = false\r\n          break\r\n        }\r\n      }\r\n      if (!isUnique) {\r\n        this.$message({\r\n          message: '批量分配工序时只有相同车间下的才可一起批量分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return isUnique\r\n    },\r\n    checkHasWorkShop(type, arr) {\r\n      let hasWorkShop = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (!item.Workshop_Name) {\r\n          hasWorkShop = false\r\n          break\r\n        }\r\n      }\r\n      if (!hasWorkShop) {\r\n        this.$message({\r\n          message: '请先选择车间后再进行工序分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return hasWorkShop\r\n    },\r\n    handleAddDialog(type = 'add') {\r\n      this.title = `添加${this.partName}`\r\n\r\n      this.currentComponent = 'AddDraft'\r\n      this.dWidth = '96%'\r\n      this.openAddDraft = true\r\n\r\n      this.setAddTbKey()\r\n\r\n      this.$nextTick(_ => {\r\n        this.$refs['draft'].initData()\r\n      })\r\n    },\r\n    async addToTbList(newList) {\r\n      await this.mergeSelectList(newList)\r\n      this.setAddTbKey()\r\n    },\r\n    setAddTbKey() {\r\n      const selectKeys = this.tbData.filter(cur => cur.puuid).map(v => v.puuid)\r\n      this.changeAddTbKeys(selectKeys)\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    handleSelectMenu(v) {\r\n      if (v === 'process') {\r\n        this.openBPADialog(1)\r\n      } else if (v === 'craft') {\r\n        this.handleSetCraftProcess()\r\n      }\r\n    },\r\n    async handleSetCraftProcess() {\r\n      const showSuccess = () => {\r\n        this.$message({\r\n          message: '已分配成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n      const rowList = this.multipleSelection.map(v => v.Technology_Code).filter(v => !!v)\r\n      if (!rowList.length) {\r\n        this.$message({\r\n          message: '工艺代码不存在',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      await this.mergeCraftProcess(this.multipleSelection)\r\n      const workshopIds = Array.from(new Set(this.multipleSelection.map(v => v.Workshop_Id).filter(v => !!v)))\r\n      const w_process = []\r\n      if (workshopIds.length) {\r\n        workshopIds.forEach(workshopId => {\r\n          w_process.push(this.getProcessOption(workshopId).then(result => ({\r\n            [workshopId]: result\r\n          })))\r\n        })\r\n        const workshopPromise = Promise.all(w_process).then((values) => {\r\n          return Object.assign({}, ...values)\r\n        })\r\n        workshopPromise.then(workshop => {\r\n          let flag = true\r\n          for (let i = 0; i < this.multipleSelection.length; i++) {\r\n            const curRow = this.multipleSelection[i]\r\n            const workshopProcess = workshop[curRow.Workshop_Id]\r\n            const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n            if (craftArray) {\r\n              const isIncluded = craftArray.every(process => workshopProcess.includes(process))\r\n              if (!isIncluded) {\r\n                flag = false\r\n                continue\r\n              }\r\n              curRow.Technology_Path = craftArray.join('/')\r\n            }\r\n          }\r\n          if (!flag) {\r\n            setTimeout(() => {\r\n              this.$alert('所选车间下班组加工工序不包含工艺代码工序请手动排产', '提示', {\r\n                confirmButtonText: '确定'\r\n              })\r\n            }, 200)\r\n          }\r\n\r\n          flag && showSuccess()\r\n        })\r\n      } else {\r\n        this.multipleSelection.forEach((curRow) => {\r\n          const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n          if (craftArray) {\r\n            curRow.Technology_Path = craftArray.join('/')\r\n          }\r\n        })\r\n        showSuccess()\r\n      }\r\n    },\r\n\r\n    handleBatchOwner(type, row) {\r\n      this.title = `${type === 1 ? '批量' : ''}分配领用工序`\r\n      this.currentComponent = 'OwnerProcess'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)\r\n      })\r\n    },\r\n    handleReverse() {\r\n      const cur = []\r\n      this.tbData.forEach((element, idx) => {\r\n        element.checked = !element.checked\r\n        if (element.checked) {\r\n          cur.push(element)\r\n        }\r\n      })\r\n      this.multipleSelection = cur\r\n      if (this.multipleSelection.length === this.tbData.length) {\r\n        this.$refs['xTable'].setAllCheckboxRow(true)\r\n      }\r\n      if (this.multipleSelection.length === 0) {\r\n        this.$refs['xTable'].setAllCheckboxRow(false)\r\n      }\r\n    },\r\n    // tbFilterChange() {\r\n    //   const xTable = this.$refs.xTable\r\n    //   const column = xTable.getColumnByField('Type_Name')\r\n    //   if (!column?.filters?.length) return\r\n    //   column.filters.forEach(d => {\r\n    //     d.checked = d.value === this.searchType\r\n    //   })\r\n    //   xTable.updateData()\r\n    // },\r\n    getType() {\r\n      const getCompTree = () => {\r\n        const fun = this.isCom ? GetCompTypeTree : GetPartTypeList\r\n        fun({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            let result = res.Data\r\n            if (!this.isCom) {\r\n              result = result\r\n                .map((v, idx) => {\r\n                  return {\r\n                    Data: v.Name,\r\n                    Label: v.Name\r\n                  }\r\n                })\r\n            }\r\n            this.treeParamsComponentType.data = result\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectComponentType?.treeDataUpdateFun(result)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      getCompTree()\r\n    },\r\n    // 查看图纸\r\n    handleDwg(row) {\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Comp_Id = row.Comp_Import_Detail_Id\r\n      } else {\r\n        obj.Part_Id = row.Part_Aggregate_Id\r\n      }\r\n\r\n      GetSteelCadAndBimId({ importDetailId: obj.Part_Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.extensionName = res.Data[0].ExtensionName\r\n          this.fileBim = res.Data[0].fileBim\r\n          this.IsUploadCad = res.Data[0].IsUpload\r\n          this.cadRowCode = row.Part_Code\r\n          this.cadRowProjectId = row.Sys_Project_Id\r\n          this.fileView()\r\n        }\r\n      })\r\n      // GetDwg(obj).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     const fileurl = res?.Data?.length && res.Data[0].File_Url\r\n      //     window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl), '_blank')\r\n      //   } else {\r\n      //     this.$message({\r\n      //       message: res.Message,\r\n      //       type: 'error'\r\n      //     })\r\n      //   }\r\n      // })\r\n    },\r\n    setProcessList(info) {\r\n      this.changeProcessList(info)\r\n    },\r\n    resetInnerForm() {\r\n      this.$refs['searchForm'].resetFields()\r\n      this.$refs.xTable.clearFilter()\r\n    },\r\n    innerFilter() {\r\n      this.multipleSelection = []\r\n      const arr = []\r\n      if (this.isCom) {\r\n        arr.push('Type', 'Comp_Code', 'Spec', 'Is_Component')\r\n      } else {\r\n        arr.push('Part_Code', 'Spec', 'Type_Name', 'Project_Name', 'Area_Name', 'InstallUnit_Name')\r\n      }\r\n\r\n      const xTable = this.$refs.xTable\r\n      xTable.clearCheckboxRow()\r\n      arr.forEach((element, idx) => {\r\n        const column = xTable.getColumnByField(element)\r\n        if (element === 'Is_Component') {\r\n          column.filters.forEach((option, idx) => {\r\n            option.checked = idx === (this.innerForm.searchDirect ? 0 : 1)\r\n          })\r\n        }\r\n        if (element === 'Spec') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchSpecSearch\r\n          option.checked = true\r\n        }\r\n        if (element === 'Type' || element === 'Type_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchComTypeSearch\r\n          option.checked = true\r\n        }\r\n        if (element === 'Comp_Code' || element === 'Part_Code') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchContent\r\n          option.checked = true\r\n        }\r\n        if (element === 'Project_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.projectName\r\n          option.checked = true\r\n        }\r\n        if (element === 'Area_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.areaName\r\n          option.checked = true\r\n        }\r\n        if (element === 'InstallUnit_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.installName\r\n          option.checked = true\r\n        }\r\n      })\r\n      xTable.updateData()\r\n    },\r\n    filterComponentMethod({ option, row }) {\r\n      if (this.innerForm.searchDirect === '') {\r\n        return true\r\n      }\r\n      return row.Is_Component === !this.innerForm.searchDirect\r\n    },\r\n    filterSpecMethod({ option, row }) {\r\n      if (this.innerForm.searchSpecSearch.trim() === '') {\r\n        return true\r\n      }\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n      const specArray = splitAndClean(this.innerForm.searchSpecSearch)\r\n      return specArray.some(code => (row.Spec || '').includes(code))\r\n    },\r\n\r\n    filterTypeMethod({ option, row }) {\r\n      if (this.innerForm.searchComTypeSearch === '') {\r\n        return true\r\n      }\r\n      const cur = this.isCom ? 'Type' : 'Type_Name'\r\n      return row[cur] === this.innerForm.searchComTypeSearch\r\n    },\r\n    filterCodeMethod({ option, row }) {\r\n      if (this.innerForm.searchContent.trim() === '') {\r\n        return true\r\n      }\r\n\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      const cur = this.isCom ? 'Comp_Code' : 'Part_Code'\r\n\r\n      const arr = splitAndClean(this.innerForm.searchContent)\r\n\r\n      if (this.curSearch === 1) {\r\n        return arr.some(code => row[cur] === code)\r\n      } else {\r\n        for (let i = 0; i < arr.length; i++) {\r\n          const item = arr[i]\r\n          if (row[cur].includes(item)) {\r\n            return true\r\n          }\r\n        }\r\n        return false\r\n      }\r\n    },\r\n    filterProjectMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.Project_Name === option.data\r\n    },\r\n    filterAreaMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.Area_Name === option.data\r\n    },\r\n    filterInstallMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.InstallUnit_Name === option.data\r\n    },\r\n    componentTypeFilter(e) {\r\n      this.$refs?.treeSelectComponentType.filterFun(e)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n        this.disabledAdd = false\r\n      } else {\r\n        this.disabledAdd = true\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data\r\n          if (this.installUnitIdList.length) {\r\n            this.formInline.InstallUnit_Id = this.installUnitIdList[0].Id\r\n          }\r\n          this.disabledAdd = false\r\n        })\r\n      }\r\n    },\r\n    installChange() {\r\n      if (!this.tbData.length) {\r\n        this.$refs['searchForm'].resetFields()\r\n        this.$refs.xTable.clearFilter()\r\n        return\r\n      }\r\n      this.$confirm('切换区域右侧数据清空, 是否确认?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbData = []\r\n        this.resetInnerForm()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    showPartUsedProcess(row) {\r\n      if (this.isNest) {\r\n        return !!row.Comp_Import_Detail_Id\r\n      } else {\r\n        return !this.isView && row.Type !== 'Direct'\r\n      }\r\n    },\r\n    handleExport() {\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '暂无数据',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$refs.xTable.exportData({\r\n        filename: `${this.partName}排产-${this.formInline.Schduling_Code}(${this.partName})`,\r\n        type: 'xlsx',\r\n        data: this.tbData\r\n      })\r\n    },\r\n    handleCloseDrawer() {\r\n      this.drawer = false\r\n    },\r\n    fileView() {\r\n      this.iframeKey = uuidv4()\r\n      this.iframeUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=11&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.drawer = true\r\n    },\r\n    renderIframe() {\r\n      const ExtensionName = this.extensionName\r\n      const fileBim = this.fileBim\r\n      this.iframeUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=11&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.fullscreenid = ExtensionName\r\n      this.fullbimid = fileBim\r\n    },\r\n    fullscreen(v) {\r\n      this.templateUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=13&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.drawersull = true\r\n    },\r\n    frameListener({ data }) {\r\n      if (data.type === 'loaded') {\r\n        console.log('data', data)\r\n        console.error(\r\n          'data.data.iframeId',\r\n          data.data.iframeId,\r\n          typeof data.data.iframeId\r\n        )\r\n        if (data.data.iframeId === '11') {\r\n          document.getElementById('frame').contentWindow.postMessage(\r\n            {\r\n              type: 'router',\r\n              path: '/modelCad',\r\n              query: {\r\n                // baseUrl: baseUrl(),\r\n                cadId: this.fileBim,\r\n                projectId: this.cadRowProjectId,\r\n                steelName: this.cadRowCode,\r\n                showCad: this.IsUploadCad,\r\n                isSubAssembly: false,\r\n                isPart: true\r\n                // cadId: this.fileBim\r\n                // token: localStorage.getItem('Token'),\r\n                // auth_id: localStorage.getItem('Last_Working_Object_Id')\r\n              }\r\n            },\r\n            '*'\r\n          )\r\n        } else if (data.data.iframeId === '13') {\r\n          document.getElementById('fullFrame').contentWindow.postMessage(\r\n            {\r\n              type: 'router',\r\n              path: '/modelCad',\r\n              query: {\r\n                // baseUrl: baseUrl(),\r\n                cadId: this.fileBim,\r\n                projectId: this.cadRowProjectId,\r\n                steelName: this.cadRowCode,\r\n                showCad: this.IsUploadCad,\r\n                isSubAssembly: false,\r\n                isPart: true\r\n                // token: localStorage.getItem('Token'),\r\n                // auth_id: localStorage.getItem('Last_Working_Object_Id')\r\n              }\r\n            },\r\n            '*'\r\n          )\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.flex-row {\r\n  display: flex;\r\n\r\n  .cs-left {\r\n    background-color: #ffffff;\r\n    margin-right: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n    .cs-tree-wrapper {\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      overflow: hidden;\r\n      padding: 16px;\r\n\r\n      .tree-search {\r\n        display: flex;\r\n\r\n        .search-select {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .el-tree {\r\n        flex: 1;\r\n        overflow: auto;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.btn-x {\r\n  //margin-bottom: 16px;\r\n\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.demo-form-inline {\r\n  ::v-deep {\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-column-row{\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .cs-ell{\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n\r\n  }\r\n}\r\n</style>\r\n"]}]}
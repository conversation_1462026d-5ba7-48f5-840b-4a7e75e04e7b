{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\utils\\baseurl.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\utils\\baseurl.js", "mtime": 1757468112029}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["baseUrl", "promiseProduceBaseUrl", "document", "querySelector", "dataset", "undefined", "process", "env", "VUE_APP_BASE_API", "indexOf", "concat", "platformUrl", "promiseProducePlatformUrl", "VUE_APP_PLATFORM_API", "promiseProjectWebUrl", "VUE_APP_WEB_PROJ_API", "promiseProjectApi", "promiseProjectBaseUrl", "dfUrl", "promiseDigitalfactoryApiUrl", "VUE_APP_DF_API", "webUrl", "promiseProduceDomainWebUrl", "VUE_APP_WEB_URL"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/utils/baseurl.js"], "sourcesContent": ["export const baseUrl = () => {\r\n  const { promiseProduceBaseUrl } = document.querySelector('html').dataset\r\n  if (promiseProduceBaseUrl === undefined) {\r\n    // return window.URLGLOBAL.URL\r\n    return process.env.VUE_APP_BASE_API\r\n  }\r\n  if (promiseProduceBaseUrl !== null && promiseProduceBaseUrl.indexOf('http') === 0) {\r\n    return `${promiseProduceBaseUrl}`\r\n  }\r\n  // return window.URLGLOBAL.URL\r\n  return process.env.VUE_APP_BASE_API\r\n}\r\n\r\nexport const platformUrl = () => {\r\n  const { promiseProducePlatformUrl } = document.querySelector('html').dataset\r\n  if (promiseProducePlatformUrl === undefined) {\r\n    // return window.URLGLOBAL.URL\r\n    return process.env.VUE_APP_PLATFORM_API\r\n  }\r\n  if (promiseProducePlatformUrl !== null && promiseProducePlatformUrl.indexOf('http') === 0) {\r\n    return `${promiseProducePlatformUrl}`\r\n  }\r\n  // return window.URLGLOBAL.URL\r\n  return process.env.VUE_APP_PLATFORM_API\r\n}\r\n\r\nexport const promiseProjectWebUrl = () => {\r\n  const { promiseProjectWebUrl } = document.querySelector('html').dataset\r\n  if (promiseProjectWebUrl === undefined) {\r\n    return process.env.VUE_APP_WEB_PROJ_API\r\n  }\r\n  if (promiseProjectWebUrl !== null && promiseProjectWebUrl.indexOf('http') === 0) {\r\n    return `${promiseProjectWebUrl}`\r\n  }\r\n  return process.env.VUE_APP_WEB_PROJ_API\r\n}\r\n\r\nexport const promiseProjectApi = () => {\r\n  // return 'http://localhost:35000/'\r\n  const { promiseProjectBaseUrl } = document.querySelector('html').dataset\r\n  if (promiseProjectBaseUrl === undefined) {\r\n    return process.env.VUE_APP_WEB_PROJ_API\r\n  }\r\n  if (promiseProjectBaseUrl !== null && promiseProjectBaseUrl.indexOf('http') === 0) {\r\n    return `${promiseProjectBaseUrl}`\r\n  }\r\n  return process.env.VUE_APP_WEB_PROJ_API\r\n}\r\n\r\nexport const dfUrl = () => {\r\n  const { promiseDigitalfactoryApiUrl } = document.querySelector('html').dataset\r\n  if (promiseDigitalfactoryApiUrl === undefined) {\r\n    // return window.URLGLOBAL.URL\r\n    return process.env.VUE_APP_DF_API\r\n  }\r\n  if (promiseDigitalfactoryApiUrl !== null && promiseDigitalfactoryApiUrl.indexOf('http') === 0) {\r\n    return `${promiseDigitalfactoryApiUrl}`\r\n  }\r\n  // return window.URLGLOBAL.URL\r\n  return process.env.VUE_APP_DF_API\r\n}\r\n\r\nexport const webUrl = () => {\r\n  const { promiseProduceDomainWebUrl } = document.querySelector('html').dataset\r\n  if (promiseProduceDomainWebUrl === undefined) {\r\n    // return window.URLGLOBAL.URL\r\n    return process.env.VUE_APP_WEB_URL\r\n  }\r\n  if (promiseProduceDomainWebUrl !== null && promiseProduceDomainWebUrl.indexOf('http') === 0) {\r\n    return `${promiseProduceDomainWebUrl}`\r\n  }\r\n  // return window.URLGLOBAL.URL\r\n  return process.env.VUE_APP_WEB_URL\r\n}\r\n"], "mappings": "AAAA,OAAO,IAAMA,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;EAC3B,IAAQC,qBAAqB,GAAKC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAACC,OAAO,CAAhEH,qBAAqB;EAC7B,IAAIA,qBAAqB,KAAKI,SAAS,EAAE;IACvC;IACA,OAAOC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EACrC;EACA,IAAIP,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,CAACQ,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IACjF,UAAAC,MAAA,CAAUT,qBAAqB;EACjC;EACA;EACA,OAAOK,OAAO,CAACC,GAAG,CAACC,gBAAgB;AACrC,CAAC;AAED,OAAO,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;EAC/B,IAAQC,yBAAyB,GAAKV,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAACC,OAAO,CAApEQ,yBAAyB;EACjC,IAAIA,yBAAyB,KAAKP,SAAS,EAAE;IAC3C;IACA,OAAOC,OAAO,CAACC,GAAG,CAACM,oBAAoB;EACzC;EACA,IAAID,yBAAyB,KAAK,IAAI,IAAIA,yBAAyB,CAACH,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IACzF,UAAAC,MAAA,CAAUE,yBAAyB;EACrC;EACA;EACA,OAAON,OAAO,CAACC,GAAG,CAACM,oBAAoB;AACzC,CAAC;AAED,OAAO,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;EACxC,IAAQA,oBAAoB,GAAKZ,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAACC,OAAO,CAA/DU,oBAAoB;EAC5B,IAAIA,oBAAoB,KAAKT,SAAS,EAAE;IACtC,OAAOC,OAAO,CAACC,GAAG,CAACQ,oBAAoB;EACzC;EACA,IAAID,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,CAACL,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IAC/E,UAAAC,MAAA,CAAUI,oBAAoB;EAChC;EACA,OAAOR,OAAO,CAACC,GAAG,CAACQ,oBAAoB;AACzC,CAAC;AAED,OAAO,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;EACrC;EACA,IAAQC,qBAAqB,GAAKf,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAACC,OAAO,CAAhEa,qBAAqB;EAC7B,IAAIA,qBAAqB,KAAKZ,SAAS,EAAE;IACvC,OAAOC,OAAO,CAACC,GAAG,CAACQ,oBAAoB;EACzC;EACA,IAAIE,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,CAACR,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IACjF,UAAAC,MAAA,CAAUO,qBAAqB;EACjC;EACA,OAAOX,OAAO,CAACC,GAAG,CAACQ,oBAAoB;AACzC,CAAC;AAED,OAAO,IAAMG,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;EACzB,IAAQC,2BAA2B,GAAKjB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAACC,OAAO,CAAtEe,2BAA2B;EACnC,IAAIA,2BAA2B,KAAKd,SAAS,EAAE;IAC7C;IACA,OAAOC,OAAO,CAACC,GAAG,CAACa,cAAc;EACnC;EACA,IAAID,2BAA2B,KAAK,IAAI,IAAIA,2BAA2B,CAACV,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IAC7F,UAAAC,MAAA,CAAUS,2BAA2B;EACvC;EACA;EACA,OAAOb,OAAO,CAACC,GAAG,CAACa,cAAc;AACnC,CAAC;AAED,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;EAC1B,IAAQC,0BAA0B,GAAKpB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAACC,OAAO,CAArEkB,0BAA0B;EAClC,IAAIA,0BAA0B,KAAKjB,SAAS,EAAE;IAC5C;IACA,OAAOC,OAAO,CAACC,GAAG,CAACgB,eAAe;EACpC;EACA,IAAID,0BAA0B,KAAK,IAAI,IAAIA,0BAA0B,CAACb,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IAC3F,UAAAC,MAAA,CAAUY,0BAA0B;EACtC;EACA;EACA,OAAOhB,OAAO,CAACC,GAAG,CAACgB,eAAe;AACpC,CAAC", "ignoreList": []}]}
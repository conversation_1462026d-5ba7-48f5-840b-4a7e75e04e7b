{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\Group.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\Group.vue", "mtime": 1745557754678}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetWorkingTeamBase", "GetWorkingTeams", "UpdateProcessTeam", "data", "allList", "selectList", "list", "value", "processId", "filterMethod", "query", "item", "Display_Name", "indexOf", "methods", "init", "row", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "Id", "getAllList", "getCurrentList", "stop", "_this2", "Promise", "resolve", "then", "res", "IsSucceed", "Data", "map", "v", "$set", "Name", "$message", "message", "Message", "type", "_this3", "change", "array", "console", "log", "handleSubmit", "_this4", "teams", "$emit"], "sources": ["src/views/PRO/project-config/process-settings/component/Group.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-transfer\r\n      v-model=\"selectList\"\r\n      filterable\r\n      :button-texts=\"['移除','添加']\"\r\n      :filter-method=\"filterMethod\"\r\n      :titles=\"['全部班组', '已选班组']\"\r\n      filter-placeholder=\"搜索...\"\r\n      :data=\"allList\"\r\n      :props=\"{label:'Display_Name',value:'Id',key:'Id'}\"\r\n      @change=\"change\"\r\n    />\r\n    <div style=\"text-align: right;margin-top: 10px\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  GetWorkingTeamBase,\r\n  GetWorkingTeams,\r\n  UpdateProcessTeam\r\n} from '@/api/PRO/technology-lib'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      allList: [],\r\n      selectList: [],\r\n      list: [],\r\n      value: [],\r\n      processId: '',\r\n      filterMethod(query, item) {\r\n        return item.Display_Name.indexOf(query) > -1\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    async init(row) {\r\n      this.processId = row.Id\r\n      await this.getAllList()\r\n      await this.getCurrentList(row)\r\n    },\r\n    getAllList() {\r\n      return new Promise((resolve) => {\r\n        GetWorkingTeams({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.allList = res.Data.map(v => {\r\n              this.$set(v, 'Display_Name', v.Name)\r\n              return v\r\n            })\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getCurrentList() {\r\n      return new Promise((resolve) => {\r\n        GetWorkingTeamBase({\r\n          processId: this.processId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.selectList = res.Data.map(v => v.Id)\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    change(array) {\r\n      console.log('array', array)\r\n      this.selectList = array\r\n    },\r\n    handleSubmit() {\r\n      UpdateProcessTeam({\r\n        processId: this.processId,\r\n        teams: this.selectList\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$emit('close')\r\n          this.$emit('refresh')\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,SACAA,kBAAA,EACAC,eAAA,EACAC,iBAAA,QACA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,IAAA;MACAC,KAAA;MACAC,SAAA;MACAC,YAAA,WAAAA,aAAAC,KAAA,EAAAC,IAAA;QACA,OAAAA,IAAA,CAAAC,YAAA,CAAAC,OAAA,CAAAH,KAAA;MACA;IACA;EACA;EACAI,OAAA;IACAC,IAAA,WAAAA,KAAAC,GAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAT,KAAA,CAAAT,SAAA,GAAAQ,GAAA,CAAAW,EAAA;cAAAH,QAAA,CAAAE,IAAA;cAAA,OACAT,KAAA,CAAAW,UAAA;YAAA;cAAAJ,QAAA,CAAAE,IAAA;cAAA,OACAT,KAAA,CAAAY,cAAA,CAAAb,GAAA;YAAA;YAAA;cAAA,OAAAQ,QAAA,CAAAM,IAAA;UAAA;QAAA,GAAAT,OAAA;MAAA;IACA;IACAO,UAAA,WAAAA,WAAA;MAAA,IAAAG,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA;QACAhC,eAAA,KAAAiC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAL,MAAA,CAAA3B,OAAA,GAAA+B,GAAA,CAAAE,IAAA,CAAAC,GAAA,WAAAC,CAAA;cACAR,MAAA,CAAAS,IAAA,CAAAD,CAAA,kBAAAA,CAAA,CAAAE,IAAA;cACA,OAAAF,CAAA;YACA;YACAN,OAAA;UACA;YACAF,MAAA,CAAAW,QAAA;cACAC,OAAA,EAAAR,GAAA,CAAAS,OAAA;cACAC,IAAA;YACA;UACA;QACA;MACA;IACA;IACAhB,cAAA,WAAAA,eAAA;MAAA,IAAAiB,MAAA;MACA,WAAAd,OAAA,WAAAC,OAAA;QACAjC,kBAAA;UACAQ,SAAA,EAAAsC,MAAA,CAAAtC;QACA,GAAA0B,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAU,MAAA,CAAAzC,UAAA,GAAA8B,GAAA,CAAAE,IAAA,CAAAC,GAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAZ,EAAA;YAAA;YACAM,OAAA;UACA;YACAa,MAAA,CAAAJ,QAAA;cACAC,OAAA,EAAAR,GAAA,CAAAS,OAAA;cACAC,IAAA;YACA;UACA;QACA;MACA;IACA;IACAE,MAAA,WAAAA,OAAAC,KAAA;MACAC,OAAA,CAAAC,GAAA,UAAAF,KAAA;MACA,KAAA3C,UAAA,GAAA2C,KAAA;IACA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACAlD,iBAAA;QACAM,SAAA,OAAAA,SAAA;QACA6C,KAAA,OAAAhD;MACA,GAAA6B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAgB,MAAA,CAAAE,KAAA;UACAF,MAAA,CAAAE,KAAA;UACAF,MAAA,CAAAV,QAAA;YACAC,OAAA;YACAE,IAAA;UACA;QACA;UACAO,MAAA,CAAAV,QAAA;YACAC,OAAA,EAAAR,GAAA,CAAAS,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
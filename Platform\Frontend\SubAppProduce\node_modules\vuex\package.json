{"name": "vuex", "version": "3.1.0", "description": "state management for Vue.js", "main": "dist/vuex.common.js", "module": "dist/vuex.esm.js", "unpkg": "dist/vuex.js", "typings": "types/index.d.ts", "files": ["dist", "types/index.d.ts", "types/helpers.d.ts", "types/vue.d.ts"], "scripts": {"dev": "node examples/server.js", "dev:dist": "rollup -wm -c build/rollup.dev.config.js", "build": "npm run build:main && npm run build:logger", "build:main": "node build/build.main.js", "build:logger": "rollup -c build/rollup.logger.config.js", "lint": "eslint src test", "test": "npm run lint && npm run test:types && npm run test:unit && npm run test:ssr && npm run test:e2e", "test:unit": "rollup -c build/rollup.dev.config.js && jasmine JASMINE_CONFIG_PATH=test/unit/jasmine.json", "test:e2e": "node test/e2e/runner.js", "test:ssr": "rollup -c build/rollup.dev.config.js && cross-env VUE_ENV=server jasmine JASMINE_CONFIG_PATH=test/unit/jasmine.json", "test:types": "tsc -p types/test", "release": "bash build/release.sh", "docs": "vuepress dev docs", "docs:build": "vuepress build docs"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vuex.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vuex/issues"}, "homepage": "https://github.com/vuejs/vuex#readme", "devDependencies": {"babel-core": "^6.22.1", "babel-loader": "^7.1.2", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.22.0", "babel-preset-env": "^1.5.1", "chromedriver": "^2.45.0", "cross-env": "^5.2.0", "cross-spawn": "^6.0.5", "css-loader": "^2.1.0", "eslint": "^5.12.0", "eslint-plugin-vue-libs": "^3.0.0", "express": "^4.14.1", "jasmine": "2.8.0", "jasmine-core": "2.8.0", "nightwatch": "^0.9.12", "nightwatch-helpers": "^1.2.0", "phantomjs-prebuilt": "^2.1.14", "rollup": "^1.1.0", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.1.0", "selenium-server": "^2.53.1", "todomvc-app-css": "^2.1.0", "typescript": "^3.2.2", "uglify-js": "^3.1.2", "vue": "^2.5.22", "vue-loader": "^15.2.1", "vue-template-compiler": "^2.5.22", "vuepress": "^0.14.1", "vuepress-theme-vue": "^1.1.0", "webpack": "^4.8.3", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.19.1"}}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\production-execution\\transfer-receive-new\\part.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\production-execution\\transfer-receive-new\\part.vue", "mtime": 1758605570154}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KCmltcG9ydCBIb21lIGZyb20gJy4vaG9tZSc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUFJPUGFydFRyYW5zZmVyUmVjZWl2ZU5ldycsCiAgY29tcG9uZW50czogewogICAgSG9tZTogSG9tZQogIH0sCiAgcHJvdmlkZTogewogICAgcGFnZVR5cGU6ICdwYXJ0JywKICAgIGJvbUxldmVsOiAnMCcKICB9Cn07"}, {"version": 3, "names": ["Home", "name", "components", "provide", "pageType", "bomLevel"], "sources": ["src/views/PRO/production-execution/transfer-receive-new/part.vue"], "sourcesContent": ["<template>\n  <home />\n</template>\n\n<script>\nimport Home from './home'\nexport default {\n  name: 'PROPartTransferReceiveNew',\n  components: {\n    Home\n  },\n  provide: {\n    pageType: 'part',\n    bomLevel: '0'\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"], "mappings": ";;;;;AAKA,OAAAA,IAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAF,IAAA,EAAAA;EACA;EACAG,OAAA;IACAC,QAAA;IACAC,QAAA;EACA;AACA", "ignoreList": []}]}
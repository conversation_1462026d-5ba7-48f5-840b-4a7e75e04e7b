{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\component\\info.vue?vue&type=style&index=0&id=64a3602e&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\component\\info.vue", "mtime": 1757468112139}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICJ+QC9zdHlsZXMvbWl4aW4uc2NzcyI7DQoNCmgzIHsNCiAgY29sb3I6ICMyOThkZmY7DQp9DQoudGFnLXggew0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICAudGFnLXdyYXBwZXIgew0KICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAgaGVpZ2h0OiAxNjBweDsNCiAgICBvdmVyZmxvdzogYXV0bzsNCiAgICBAaW5jbHVkZSBzY3JvbGxCYXI7DQogICAgLmVsLXRhZyB7DQogICAgICBtYXJnaW46IDhweCAwIDAgOHB4Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["info.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2IA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "info.vue", "sourceRoot": "src/views/PRO/basic-information/group/component", "sourcesContent": ["<template>\r\n  <div style=\"padding: 16px 0;\">\r\n    <el-form\r\n      ref=\"form\"\r\n      :model=\"form\"\r\n      inline\r\n      :rules=\"rules\"\r\n      label-width=\"130px\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <h3>基本信息</h3>\r\n      <el-row>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"班组名称：\">\r\n            {{ form.Name || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"班组长：\">\r\n            {{ form.Manager_UserName || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <!-- <el-form-item label=\"负荷提醒线：\">\r\n        {{ form.Load || \"-\" }}\r\n      </el-form-item> -->\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"排序号：\">\r\n            {{ form.Sort || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"班组月均负荷(t)：\">\r\n            {{ form.Month_Avg_Load || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"是否外协：\">\r\n            {{ form.Is_Outsource === true ? \"是\" : \"否\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"是否启用：\">\r\n            {{ form. Is_Enabled === true ? \"是\" : \"否\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row>\r\n        <el-col :span=\"16\">\r\n          <el-form-item label=\"关联仓库/库位：\">\r\n            {{ form.Warehouse_Name ? form.Warehouse_Name + '/' + form.Location_Name : \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item v-if=\"Is_Workshop_Enabled\" label=\"所属车间：\">\r\n            {{ form.Workshop_Name || \"-\" }}\r\n          </el-form-item>\r\n        </el-col></el-row>\r\n      <h3>班组成员</h3>\r\n      <div class=\"tag-x\">\r\n        <div class=\"tag-wrapper\">\r\n          <el-tag\r\n            v-for=\"tag in tags\"\r\n            :key=\"tag.User_Id\"\r\n            size=\"large\"\r\n            type=\"info\"\r\n          >\r\n            {{ tag.User_Name }}\r\n          </el-tag>\r\n        </div>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetWorkingTeamInfo } from '@/api/PRO/technology-lib'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      tags: [],\r\n      form: {\r\n        Name: '',\r\n        Manager_UserName: '',\r\n        Manager_UserId: '',\r\n        Load: '',\r\n        Workshop_Name: '',\r\n        Month_Avg_Load: null,\r\n        Sort: 0,\r\n        Is_Outsource: false,\r\n        Is_Enabled: true,\r\n        Warehouse_Id: '',\r\n        Location_Id: '',\r\n        Warehouse_Name: '',\r\n        Location_Name: ''\r\n      },\r\n      rules: {},\r\n\r\n      Is_Workshop_Enabled: ''\r\n    }\r\n  },\r\n  created() {\r\n  },\r\n  methods: {\r\n    initData(row, Is_Workshop_Enabled) {\r\n      GetWorkingTeamInfo({\r\n        id: row.Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const { Manager_UserName, Manager_UserId, Load, Name, Users, Month_Avg_Load, Sort, Is_Outsource, Is_Enabled, Warehouse_Name, Location_Name } =\r\n            res.Data\r\n          this.form.Manager_UserName = Manager_UserName\r\n          this.form.Manager_UserId = Manager_UserId\r\n          this.form.Load = Load\r\n          this.form.Name = Name\r\n          this.tags = Users\r\n          this.form.Workshop_Name = row.Workshop_Name\r\n          this.Is_Workshop_Enabled = Is_Workshop_Enabled\r\n          this.form.Month_Avg_Load = Month_Avg_Load\r\n          this.form.Sort = Sort\r\n          this.form.Is_Outsource = Is_Outsource\r\n          this.form.Is_Enabled = Is_Enabled\r\n          this.form.Warehouse_Name = Warehouse_Name\r\n          this.form.Location_Name = Location_Name\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n\r\nh3 {\r\n  color: #298dff;\r\n}\r\n.tag-x {\r\n  text-align: left;\r\n  .tag-wrapper {\r\n    display: inline-block;\r\n    flex-wrap: wrap;\r\n    height: 160px;\r\n    overflow: auto;\r\n    @include scrollBar;\r\n    .el-tag {\r\n      margin: 8px 0 0 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
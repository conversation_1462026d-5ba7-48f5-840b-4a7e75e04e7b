{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-path\\index.vue?vue&type=style&index=0&id=275041e2&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-path\\index.vue", "mtime": 1757468113387}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudGIteHsNCiAgZmxleDogMTsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmKA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/process-path", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <top-header padding=\"0\">\r\n        <template #left>\r\n          <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\r\n        </template>\r\n      </top-header>\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          element-loading-spinner=\"el-icon-loading\"\r\n          element-loading-text=\"拼命加载中\"\r\n          empty-text=\"暂无数据\"\r\n          class=\"cs-vxe-table\"\r\n          height=\"auto\"\r\n          align=\"left\"\r\n          stripe\r\n          :loading=\"tbLoading\"\r\n          :row-config=\"{ isCurrent: true, isHover: true }\"\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n        >\r\n          <template>\r\n            <vxe-column\r\n              v-for=\"(item, index) in columns\"\r\n              :key=\"index\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              align=\"left\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width ? item.Width : 120\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                <div v-if=\"item.Code === 'Type'\">\r\n                  <span :style=\"{color:row[item.Code]===1 ?'#d29730': row[item.Code]===2?'#20bbc7':'#de85e4'}\">\r\n                    <!-- {{ row[item.Code]===1 ?'构件工艺':row[item.Code]===2?'零件工艺':'部件工艺' }} -->\r\n                    <!-- {{ row.Bom_Level === '-1' ? '构件工艺' : row.Bom_Level === '0' ? '零件工艺' : '部件工艺' }} -->\r\n                    {{ getBomName(row.Bom_Level) }}工艺\r\n                  </span>\r\n                </div>\r\n                <div v-else>\r\n                  {{ row[item.Code] | displayValue }}\r\n                </div>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-table-column title=\"操作\" :min-width=\"120\">\r\n              <template #default=\"{ row }\">\r\n                <el-button type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\r\n                <el-button type=\"text\" class=\"txt-red\" @click=\"handleDelete(row)\">删除</el-button>\r\n              </template>\r\n            </vxe-table-column>\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n      <Dialog ref=\"dialog\" :bom-list=\"bomList\" @refresh=\"fetchData\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport TopHeader from '@/components/TopHeader/index.vue'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport { DeleteTechnology, GetLibList } from '@/api/PRO/technology-lib'\r\nimport { mapGetters } from 'vuex'\r\nimport Dialog from './compoments/Add.vue'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROProcessPath',\r\n  components: { TopHeader, Dialog },\r\n  mixins: [getTbInfo],\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      tbLoading: false,\r\n      total: 0,\r\n      columns: [],\r\n      tbData: [],\r\n      bomList: [],\r\n      tbConfig: {}\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n  },\r\n  async mounted() {\r\n    await this.getTableConfig('ProcessPathList')\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    getBomName(code) {\r\n      const currentBomInfo = this.bomList.find(item => {\r\n        return item.Code.toString() === code.toString()\r\n      })\r\n      return currentBomInfo?.Display_Name || ''\r\n    },\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      GetLibList({\r\n        Id: '',\r\n        Type: 0\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const bomCode = this.bomList.map(item => +item.Code)\r\n          this.tbData = (res.Data || []).filter(item => bomCode.includes(item.Bom_Level))\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleAdd(row) {\r\n      this.$refs['dialog'].handleOpen()\r\n    },\r\n    handleEdit(row) {\r\n      this.$refs['dialog'].handleOpen(row)\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该工艺', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        DeleteTechnology({\r\n          technologyId: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.tb-x{\r\n  flex: 1;\r\n}\r\n</style>\r\n"]}]}